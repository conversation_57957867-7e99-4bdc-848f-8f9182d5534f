@extends('layouts.app')

@section('content')
<div class="py-12">
    <div class="container mx-auto px-6">
        <div class="max-w-3xl mx-auto">
            <!-- Message de succès -->
            <div class="text-center mb-12">
                <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-6">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                </div>
                <h1 class="text-3xl font-bold text-gray-900 mb-4">Commande confirmée</h1>
                <p class="text-lg text-gray-600">
                    Merci pour votre commande. Votre numéro de commande est <strong>{{ $order->order_number }}</strong>.
                </p>
            </div>

            <!-- Détails de la commande -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <!-- En-tête -->
                <div class="border-b border-gray-200 p-6">
                    <div class="flex justify-between items-center">
                        <h2 class="text-xl font-semibold">Détails de la commande</h2>
                        <span class="px-3 py-1 rounded-full text-sm 
                            @if($order->status === 'pending') bg-yellow-100 text-yellow-800
                            @elseif($order->status === 'processing') bg-blue-100 text-blue-800
                            @elseif($order->status === 'completed') bg-green-100 text-green-800
                            @else bg-gray-100 text-gray-800 @endif">
                            {{ $order->formatted_status }}
                        </span>
                    </div>
                    <p class="text-sm text-gray-600 mt-2">
                        Commandé le {{ $order->created_at->format('d/m/Y à H:i') }}
                    </p>
                </div>

                <!-- Produits -->
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-medium mb-4">Articles commandés</h3>
                    <div class="divide-y divide-gray-200">
                        @foreach($order->items as $item)
                            <div class="py-4 flex justify-between">
                                <div>
                                    <h4 class="font-medium">{{ $item->product_name }}</h4>
                                    <p class="text-sm text-gray-600">Quantité: {{ $item->quantity }}</p>
                                </div>
                                <p class="font-medium">{{ $item->formatted_subtotal }}</p>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Totaux -->
                <div class="p-6 bg-gray-50">
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Sous-total</span>
                            <span>{{ number_format($order->total_amount - $order->shipping_amount - $order->tax_amount, 2) }} DA</span>
                        </div>
                        @if($order->shipping_amount > 0)
                            <div class="flex justify-between">
                                <span class="text-gray-600">Livraison</span>
                                <span>{{ number_format($order->shipping_amount, 2) }} DA</span>
                            </div>
                        @endif
                        @if($order->tax_amount > 0)
                            <div class="flex justify-between">
                                <span class="text-gray-600">TVA</span>
                                <span>{{ number_format($order->tax_amount, 2) }} DA</span>
                            </div>
                        @endif
                        <div class="flex justify-between text-lg font-bold pt-4 border-t">
                            <span>Total</span>
                            <span>{{ $order->formatted_total }}</span>
                        </div>
                    </div>
                </div>

                <!-- Informations de livraison -->
                <div class="p-6 border-t border-gray-200">
                    <h3 class="text-lg font-medium mb-4">Informations de livraison</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-medium text-sm text-gray-600 mb-2">Adresse de livraison</h4>
                            <p class="text-gray-900">{{ $order->customerProfile->company_name }}</p>
                            <p class="text-gray-900">{{ $order->customerProfile->contact_person }}</p>
                            <p class="text-gray-600">{{ $order->customerProfile->address }}</p>
                            <p class="text-gray-600">{{ $order->customerProfile->city }}, {{ $order->customerProfile->state }} {{ $order->customerProfile->postal_code }}</p>
                        </div>
                        <div>
                            <h4 class="font-medium text-sm text-gray-600 mb-2">Méthode de paiement</h4>
                            <p class="text-gray-900">
                                @if($order->payment_method === 'bank_transfer')
                                    Virement bancaire
                                @else
                                    Paiement à la livraison
                                @endif
                            </p>
                            <p class="text-sm text-gray-600 mt-2">
                                Statut: {{ $order->formatted_payment_status }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="mt-8 flex justify-between">
                <a href="{{ route('products.index') }}" class="text-blue-600 hover:text-blue-800 font-medium">
                    Continuer vos achats
                </a>
                @if($order->payment_method === 'bank_transfer' && $order->payment_status === 'pending')
                    <a href="#" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700">
                        Procéder au paiement
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection
