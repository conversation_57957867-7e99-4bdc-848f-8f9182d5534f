<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('user_favorites', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('favoritable_type'); // Product, Color, Video, etc.
            $table->unsignedBigInteger('favoritable_id');
            $table->text('notes')->nullable(); // User notes about the favorite
            $table->timestamps();

            $table->index(['favoritable_type', 'favoritable_id']);
            $table->unique(['user_id', 'favoritable_type', 'favoritable_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_favorites');
    }
};
