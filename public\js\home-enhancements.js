// Améliorations JavaScript pour la page d'accueil ENAP

document.addEventListener('DOMContentLoaded', function() {
    console.log('🎨 Initialisation des améliorations ENAP...');
    
    // Initialiser toutes les fonctionnalités
    initScrollAnimations();
    initCounterAnimations();
    initParallaxEffects();
    initInteractiveElements();
    initPerformanceOptimizations();
    
    console.log('✅ Améliorations ENAP initialisées avec succès !');
});

// Animations au scroll
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('revealed');
                
                // Ajouter des animations spécifiques selon le type d'élément
                if (entry.target.classList.contains('stat-card')) {
                    animateCounter(entry.target);
                }
                
                if (entry.target.classList.contains('product-card')) {
                    animateProductCard(entry.target);
                }
                
                if (entry.target.classList.contains('testimonial-card')) {
                    animateTestimonial(entry.target);
                }
            }
        });
    }, observerOptions);

    // Observer tous les éléments avec la classe scroll-reveal
    document.querySelectorAll('.scroll-reveal').forEach(el => {
        observer.observe(el);
    });

    // Observer les cartes de statistiques
    document.querySelectorAll('[class*="group"]').forEach(el => {
        el.classList.add('scroll-reveal');
        observer.observe(el);
    });
}

// Animation des compteurs
function initCounterAnimations() {
    function animateCounter(element) {
        const counter = element.querySelector('.stat-counter, [class*="text-6xl"], [class*="text-5xl"], [class*="text-4xl"]');
        if (!counter) return;

        const target = parseInt(counter.textContent.replace(/[^\d]/g, ''));
        if (isNaN(target)) return;

        const duration = 2000; // 2 secondes
        const increment = target / (duration / 16); // 60 FPS
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Formater le nombre avec le suffixe original
            const originalText = counter.textContent;
            const suffix = originalText.replace(/[\d,]/g, '');
            counter.textContent = Math.floor(current).toLocaleString() + suffix;
        }, 16);
    }

    // Exposer la fonction globalement
    window.animateCounter = animateCounter;
}

// Effets parallax
function initParallaxEffects() {
    let ticking = false;

    function updateParallax() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('[class*="animate-float"], .floating-element');
        
        parallaxElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1);
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
        
        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick, { passive: true });
}

// Éléments interactifs
function initInteractiveElements() {
    // Effet de survol sur les cartes de produits
    document.querySelectorAll('.product-card, .group').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-12px) scale(1.02)';
            this.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });

    // Effet de clic sur les boutons
    document.querySelectorAll('button, .btn-enhanced, a[class*="bg-"]').forEach(button => {
        button.addEventListener('click', function(e) {
            // Créer un effet de ripple
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
            `;
            
            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Animation des étoiles dans les témoignages
    document.querySelectorAll('.testimonial-card').forEach(card => {
        const stars = card.querySelectorAll('svg[class*="text-yellow"]');
        
        card.addEventListener('mouseenter', function() {
            stars.forEach((star, index) => {
                setTimeout(() => {
                    star.style.transform = 'scale(1.2) rotate(10deg)';
                    star.style.transition = 'transform 0.2s ease';
                }, index * 50);
            });
        });

        card.addEventListener('mouseleave', function() {
            stars.forEach(star => {
                star.style.transform = '';
            });
        });
    });
}

// Animation des cartes de produits
function animateProductCard(card) {
    card.style.opacity = '0';
    card.style.transform = 'translateY(30px) scale(0.9)';
    
    setTimeout(() => {
        card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
        card.style.opacity = '1';
        card.style.transform = 'translateY(0) scale(1)';
    }, 100);
}

// Animation des témoignages
function animateTestimonial(card) {
    const avatar = card.querySelector('[class*="rounded-full"]');
    const content = card.querySelector('blockquote');
    const stars = card.querySelectorAll('svg[class*="text-yellow"]');
    
    // Animation de l'avatar
    if (avatar) {
        avatar.style.transform = 'scale(0)';
        setTimeout(() => {
            avatar.style.transition = 'transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            avatar.style.transform = 'scale(1)';
        }, 200);
    }
    
    // Animation du contenu
    if (content) {
        content.style.opacity = '0';
        content.style.transform = 'translateY(20px)';
        setTimeout(() => {
            content.style.transition = 'all 0.6s ease';
            content.style.opacity = '1';
            content.style.transform = 'translateY(0)';
        }, 400);
    }
    
    // Animation des étoiles
    stars.forEach((star, index) => {
        star.style.opacity = '0';
        star.style.transform = 'scale(0) rotate(-180deg)';
        setTimeout(() => {
            star.style.transition = 'all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
            star.style.opacity = '1';
            star.style.transform = 'scale(1) rotate(0deg)';
        }, 600 + (index * 100));
    });
}

// Optimisations de performance
function initPerformanceOptimizations() {
    // Lazy loading pour les images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('loading-shimmer');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            img.classList.add('loading-shimmer');
            imageObserver.observe(img);
        });
    }

    // Préchargement des images importantes
    const criticalImages = [
        '/images/slides/acryphob.jpg',
        '/images/slides/blancryl.jpg',
        '/images/slides/decosoie.jpg'
    ];

    criticalImages.forEach(src => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
    });

    // Optimisation des animations pour les appareils à faible performance
    const isLowPerformance = navigator.hardwareConcurrency < 4 || 
                            navigator.deviceMemory < 4 ||
                            window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (isLowPerformance) {
        document.documentElement.style.setProperty('--animation-duration', '0.3s');
        document.querySelectorAll('[class*="animate-"]').forEach(el => {
            el.style.animationDuration = '0.3s';
        });
    }
}

// Utilitaires
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Gestion des erreurs
window.addEventListener('error', function(e) {
    console.warn('Erreur dans les améliorations ENAP:', e.error);
});

// Ajout de styles CSS dynamiques pour l'animation ripple
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Export pour utilisation externe
window.ENAPEnhancements = {
    animateCounter,
    animateProductCard,
    animateTestimonial,
    debounce,
    throttle
};
