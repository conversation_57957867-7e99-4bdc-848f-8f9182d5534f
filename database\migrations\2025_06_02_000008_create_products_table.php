<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->json('name');
            $table->json('description')->nullable();
            $table->string('type');
            $table->string('surface_type');
            $table->string('finish_type');
            $table->decimal('coverage', 8, 2);
            $table->integer('drying_time');
            $table->boolean('is_available')->default(true);
            $table->timestamps();
            
            $table->index('type');
            $table->index('surface_type');
        });
    }

    public function down()
    {
        Schema::dropIfExists('products');
    }
};
