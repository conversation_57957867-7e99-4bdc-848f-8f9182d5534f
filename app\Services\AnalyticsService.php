<?php

namespace App\Services;

use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\Quote;
use App\Models\Event;
use App\Models\EventRegistration;
use App\Models\UserProject;
use App\Models\ColorPalette;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsService
{
    public function getDashboardMetrics(): array
    {
        $now = Carbon::now();
        $lastMonth = $now->copy()->subMonth();
        $lastYear = $now->copy()->subYear();

        return [
            'users' => [
                'total' => User::count(),
                'new_this_month' => User::where('created_at', '>=', $lastMonth)->count(),
                'active_this_month' => User::whereHas('profile', function($query) use ($lastMonth) {
                    $query->where('last_activity', '>=', $lastMonth);
                })->count(),
            ],
            'orders' => [
                'total' => Order::count(),
                'this_month' => Order::where('created_at', '>=', $lastMonth)->count(),
                'revenue_this_month' => Order::where('created_at', '>=', $lastMonth)
                    ->where('payment_status', 'paid')
                    ->sum('total_amount'),
                'average_order_value' => Order::where('payment_status', 'paid')->avg('total_amount'),
            ],
            'quotes' => [
                'total' => Quote::count(),
                'pending' => Quote::where('status', 'pending')->count(),
                'conversion_rate' => $this->getQuoteConversionRate(),
            ],
            'events' => [
                'total' => Event::count(),
                'upcoming' => Event::where('start_date', '>', $now)->count(),
                'registrations_this_month' => EventRegistration::where('created_at', '>=', $lastMonth)->count(),
            ],
            'products' => [
                'total' => Product::count(),
                'most_popular' => $this->getMostPopularProducts(5),
                'low_stock' => $this->getLowStockProducts(),
            ],
        ];
    }

    public function getSalesAnalytics(string $period = '30days'): array
    {
        $startDate = $this->getStartDateForPeriod($period);
        
        $salesData = Order::where('created_at', '>=', $startDate)
            ->where('payment_status', 'paid')
            ->selectRaw('DATE(created_at) as date, COUNT(*) as orders, SUM(total_amount) as revenue')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $productSales = Order::where('created_at', '>=', $startDate)
            ->where('payment_status', 'paid')
            ->join('order_items', 'orders.id', '=', 'order_items.order_id')
            ->join('products', 'order_items.product_id', '=', 'products.id')
            ->selectRaw('products.name, SUM(order_items.quantity) as quantity_sold, SUM(order_items.total_price) as revenue')
            ->groupBy('products.id', 'products.name')
            ->orderBy('revenue', 'desc')
            ->limit(10)
            ->get();

        return [
            'period' => $period,
            'total_revenue' => $salesData->sum('revenue'),
            'total_orders' => $salesData->sum('orders'),
            'average_order_value' => $salesData->sum('orders') > 0 ? $salesData->sum('revenue') / $salesData->sum('orders') : 0,
            'daily_sales' => $salesData,
            'top_products' => $productSales,
        ];
    }

    public function getUserAnalytics(): array
    {
        $userTypes = User::join('user_profiles', 'users.id', '=', 'user_profiles.user_id')
            ->selectRaw('user_profiles.user_type, COUNT(*) as count')
            ->groupBy('user_profiles.user_type')
            ->get();

        $userActivity = User::selectRaw('DATE(created_at) as date, COUNT(*) as new_users')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $topUsers = User::join('orders', 'users.id', '=', 'orders.user_id')
            ->where('orders.payment_status', 'paid')
            ->selectRaw('users.name, users.email, COUNT(orders.id) as order_count, SUM(orders.total_amount) as total_spent')
            ->groupBy('users.id', 'users.name', 'users.email')
            ->orderBy('total_spent', 'desc')
            ->limit(10)
            ->get();

        return [
            'user_types_distribution' => $userTypes,
            'registration_trend' => $userActivity,
            'top_customers' => $topUsers,
            'total_users' => User::count(),
            'active_users_30d' => User::whereHas('profile', function($query) {
                $query->where('last_activity', '>=', Carbon::now()->subDays(30));
            })->count(),
        ];
    }

    public function getProductAnalytics(): array
    {
        $categoryPerformance = Product::join('product_categories', 'products.category_id', '=', 'product_categories.id')
            ->leftJoin('order_items', 'products.id', '=', 'order_items.product_id')
            ->selectRaw('product_categories.name as category, COUNT(DISTINCT products.id) as product_count, COALESCE(SUM(order_items.quantity), 0) as total_sold')
            ->groupBy('product_categories.id', 'product_categories.name')
            ->get();

        $popularColors = ColorPalette::leftJoin('user_favorites', function($join) {
                $join->on('color_palettes.id', '=', 'user_favorites.favoritable_id')
                     ->where('user_favorites.favoritable_type', '=', ColorPalette::class);
            })
            ->selectRaw('color_palettes.name, color_palettes.hex_code, COUNT(user_favorites.id) as favorite_count')
            ->groupBy('color_palettes.id', 'color_palettes.name', 'color_palettes.hex_code')
            ->orderBy('favorite_count', 'desc')
            ->limit(10)
            ->get();

        $stockAlerts = DB::table('inventory')
            ->join('products', 'inventory.product_id', '=', 'products.id')
            ->join('production_units', 'inventory.location_id', '=', 'production_units.id')
            ->where('inventory.quantity_available', '<=', DB::raw('inventory.minimum_stock'))
            ->select('products.name as product_name', 'production_units.name as location', 'inventory.quantity_available', 'inventory.minimum_stock')
            ->get();

        return [
            'category_performance' => $categoryPerformance,
            'popular_colors' => $popularColors,
            'stock_alerts' => $stockAlerts,
            'total_products' => Product::count(),
            'out_of_stock' => DB::table('inventory')->where('quantity_available', 0)->count(),
        ];
    }

    public function getEventAnalytics(): array
    {
        $eventTypes = Event::selectRaw('type, COUNT(*) as count, AVG(max_participants) as avg_capacity')
            ->groupBy('type')
            ->get();

        $registrationTrends = EventRegistration::selectRaw('DATE(created_at) as date, COUNT(*) as registrations')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $popularEvents = Event::leftJoin('event_registrations', 'events.id', '=', 'event_registrations.event_id')
            ->selectRaw('events.title, events.type, COUNT(event_registrations.id) as registration_count, events.max_participants')
            ->groupBy('events.id', 'events.title', 'events.type', 'events.max_participants')
            ->orderBy('registration_count', 'desc')
            ->limit(10)
            ->get();

        return [
            'event_types_distribution' => $eventTypes,
            'registration_trends' => $registrationTrends,
            'popular_events' => $popularEvents,
            'total_events' => Event::count(),
            'total_registrations' => EventRegistration::count(),
            'upcoming_events' => Event::where('start_date', '>', Carbon::now())->count(),
        ];
    }

    public function getQuoteAnalytics(): array
    {
        $quotesByStatus = Quote::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        $conversionFunnel = [
            'total_quotes' => Quote::count(),
            'pending' => Quote::where('status', 'pending')->count(),
            'processing' => Quote::where('status', 'processing')->count(),
            'completed' => Quote::where('status', 'completed')->count(),
            'cancelled' => Quote::where('status', 'cancelled')->count(),
        ];

        $averageResponseTime = Quote::where('status', '!=', 'pending')
            ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, created_at, updated_at)) as avg_hours')
            ->first();

        $quotesByProjectType = Quote::selectRaw('project_type, COUNT(*) as count, AVG(surface_area) as avg_surface')
            ->groupBy('project_type')
            ->get();

        return [
            'quotes_by_status' => $quotesByStatus,
            'conversion_funnel' => $conversionFunnel,
            'average_response_time_hours' => $averageResponseTime->avg_hours ?? 0,
            'quotes_by_project_type' => $quotesByProjectType,
            'conversion_rate' => $this->getQuoteConversionRate(),
        ];
    }

    private function getQuoteConversionRate(): float
    {
        $totalQuotes = Quote::count();
        $completedQuotes = Quote::where('status', 'completed')->count();
        
        return $totalQuotes > 0 ? ($completedQuotes / $totalQuotes) * 100 : 0;
    }

    private function getMostPopularProducts(int $limit = 5): array
    {
        return Product::leftJoin('order_items', 'products.id', '=', 'order_items.product_id')
            ->selectRaw('products.name, COALESCE(SUM(order_items.quantity), 0) as total_sold')
            ->groupBy('products.id', 'products.name')
            ->orderBy('total_sold', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    private function getLowStockProducts(): int
    {
        return DB::table('inventory')
            ->where('quantity_available', '<=', DB::raw('minimum_stock'))
            ->count();
    }

    private function getStartDateForPeriod(string $period): Carbon
    {
        switch ($period) {
            case '7days':
                return Carbon::now()->subDays(7);
            case '30days':
                return Carbon::now()->subDays(30);
            case '90days':
                return Carbon::now()->subDays(90);
            case '1year':
                return Carbon::now()->subYear();
            default:
                return Carbon::now()->subDays(30);
        }
    }

    public function generateReport(string $type, array $filters = []): array
    {
        switch ($type) {
            case 'sales':
                return $this->getSalesAnalytics($filters['period'] ?? '30days');
            case 'users':
                return $this->getUserAnalytics();
            case 'products':
                return $this->getProductAnalytics();
            case 'events':
                return $this->getEventAnalytics();
            case 'quotes':
                return $this->getQuoteAnalytics();
            case 'dashboard':
                return $this->getDashboardMetrics();
            default:
                throw new \InvalidArgumentException("Unknown report type: {$type}");
        }
    }
}
