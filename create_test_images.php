<?php
// Script pour créer des images de test pour le slider ENAP

$slidesDir = 'public/images/slides/';

// Créer le dossier s'il n'existe pas
if (!is_dir($slidesDir)) {
    mkdir($slidesDir, 0755, true);
}

// Données des slides
$slides = [
    [
        'filename' => 'slide-1.jpg',
        'title' => 'Excellence ENAP',
        'subtitle' => 'Leader algérien en peintures',
        'color' => '#1e40af'
    ],
    [
        'filename' => 'slide-2.jpg',
        'title' => 'Innovation & Technologie',
        'subtitle' => '40+ ans d\'expertise',
        'color' => '#059669'
    ],
    [
        'filename' => 'slide-3.jpg',
        'title' => 'Simulateur de Couleurs',
        'subtitle' => 'Visualisation 3D révolutionnaire',
        'color' => '#7c3aed'
    ],
    [
        'filename' => 'slide-4.jpg',
        'title' => 'Gamme Professionnelle',
        'subtitle' => 'Solutions spécialisées',
        'color' => '#dc2626'
    ],
    [
        'filename' => 'slide-5.jpg',
        'title' => 'Service Client',
        'subtitle' => 'Accompagnement personnalisé',
        'color' => '#f59e0b'
    ]
];

foreach ($slides as $slide) {
    $filepath = $slidesDir . $slide['filename'];
    
    // Créer une image 1920x1080
    $image = imagecreatetruecolor(1920, 1080);
    
    // Couleur de fond
    $color = hexToRgb($slide['color']);
    $bgColor = imagecolorallocate($image, $color[0], $color[1], $color[2]);
    imagefill($image, 0, 0, $bgColor);
    
    // Ajouter un gradient (simulation)
    for ($y = 0; $y < 1080; $y++) {
        $alpha = ($y / 1080) * 50; // Gradient de 0 à 50% d'opacité
        $gradientColor = imagecolorallocatealpha($image, 0, 0, 0, $alpha);
        imageline($image, 0, $y, 1920, $y, $gradientColor);
    }
    
    // Couleur du texte
    $textColor = imagecolorallocate($image, 255, 255, 255);
    
    // Ajouter le titre (simulation - texte simple)
    $font = 5; // Police système
    $titleX = (1920 - strlen($slide['title']) * imagefontwidth($font)) / 2;
    $titleY = 400;
    imagestring($image, $font, $titleX, $titleY, $slide['title'], $textColor);
    
    // Ajouter le sous-titre
    $subtitleX = (1920 - strlen($slide['subtitle']) * imagefontwidth($font)) / 2;
    $subtitleY = 450;
    imagestring($image, $font, $subtitleX, $subtitleY, $slide['subtitle'], $textColor);
    
    // Ajouter le logo ENAP
    $logoX = (1920 - strlen('ENAP') * imagefontwidth($font)) / 2;
    $logoY = 500;
    imagestring($image, $font, $logoX, $logoY, 'ENAP', $textColor);
    
    // Sauvegarder l'image
    imagejpeg($image, $filepath, 85);
    imagedestroy($image);
    
    echo "✅ Image créée : {$slide['filename']}\n";
}

function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    return [
        hexdec(substr($hex, 0, 2)),
        hexdec(substr($hex, 2, 2)),
        hexdec(substr($hex, 4, 2))
    ];
}

echo "\n🎉 Toutes les images de test ont été créées !\n";
?>
