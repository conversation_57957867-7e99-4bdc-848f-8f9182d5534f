<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SlideTranslation extends Model
{
    use HasFactory;

    protected $fillable = [
        'slide_id',
        'locale',
        'title',
        'description',
        'button_text'
    ];

    /**
     * Relation avec le slide parent
     */
    public function slide()
    {
        return $this->belongsTo(Slide::class);
    }
}
