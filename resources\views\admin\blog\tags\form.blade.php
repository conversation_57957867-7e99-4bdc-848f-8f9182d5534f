@extends('layouts.admin')

@section('content')
<div class="container mx-auto px-6 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-semibold text-gray-900">
                {{ isset($tag) ? 'Modifier le tag' : 'Nouveau tag' }}
            </h1>
            <a href="{{ route('admin.blog.tags.index') }}" 
               class="text-gray-600 hover:text-gray-900">
                Retour à la liste
            </a>
        </div>

        <form action="{{ isset($tag) ? route('admin.blog.tags.update', $tag) : route('admin.blog.tags.store') }}" 
              method="POST" 
              class="bg-white rounded-lg shadow-lg p-6">
            @csrf
            @if(isset($tag))
                @method('PUT')
            @endif

            <div class="space-y-6">
                <!-- Nom -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">
                        Nom du tag <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           name="name" 
                           id="name" 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                           value="{{ old('name', $tag->name ?? '') }}" 
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700">
                        Description
                    </label>
                    <textarea name="description" 
                              id="description" 
                              rows="3" 
                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ old('description', $tag->description ?? '') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Boutons -->
                <div class="flex justify-end space-x-4">
                    <a href="{{ route('admin.blog.tags.index') }}" 
                       class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                        Annuler
                    </a>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700">
                        {{ isset($tag) ? 'Mettre à jour' : 'Créer' }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@endsection
