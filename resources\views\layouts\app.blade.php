<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'ENAP') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    @if(app()->getLocale() === 'ar')
    <style>
        /* RTL specific styles */
        .rtl {
            direction: rtl;
            text-align: right;
        }
        
        .rtl .space-x-4 > :not([hidden]) ~ :not([hidden]) {
            --tw-space-x-reverse: 1;
        }
        
        .rtl .space-x-2 > :not([hidden]) ~ :not([hidden]) {
            --tw-space-x-reverse: 1;
        }
    </style>
    @endif
</head>
<body class="font-sans antialiased {{ app()->getLocale() === 'ar' ? 'rtl' : '' }}">
    <!-- Header/Navigation -->
    <header class="bg-white shadow">
        <nav class="container mx-auto px-6 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <a href="{{ url('/') }}" class="text-xl font-bold">
                        <img src="/images/logo.png" alt="ENAP" class="h-12">
                    </a>
                </div>

                <!-- Search Bar -->
                <div class="hidden lg:flex flex-1 max-w-lg mx-8">
                    <div class="relative w-full" x-data="searchComponent()">
                        <input type="text"
                               x-model="query"
                               @input.debounce.300ms="getSuggestions()"
                               @keydown.enter="performSearch()"
                               @focus="showSuggestions = true"
                               @click.away="showSuggestions = false"
                               placeholder="{{ __('search.placeholder') }}"
                               class="w-full px-4 py-2 pl-10 pr-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>

                        <!-- Search Suggestions -->
                        <div x-show="showSuggestions && suggestions.length > 0"
                             x-transition:enter="transition ease-out duration-100"
                             x-transition:enter-start="transform opacity-0 scale-95"
                             x-transition:enter-end="transform opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-75"
                             x-transition:leave-start="transform opacity-100 scale-100"
                             x-transition:leave-end="transform opacity-0 scale-95"
                             class="absolute z-50 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200">
                            <template x-for="suggestion in suggestions" :key="suggestion.text">
                                <a :href="suggestion.url"
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 border-b border-gray-100 last:border-b-0">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                        <span x-text="suggestion.text"></span>
                                        <span x-show="suggestion.type === 'popular'" class="ml-auto text-xs text-blue-600">{{ __('search.popular') }}</span>
                                    </div>
                                </a>
                            </template>
                        </div>
                    </div>
                </div>                <div class="hidden md:flex items-center space-x-8">
                    <a href="{{ route('about') }}" class="text-gray-700 hover:text-blue-600">{{ __('navigation.about') }}</a>

                    <!-- Dropdown Nos Produits -->
                    <div class="relative group">
                        <a href="{{ route('products.index') }}" class="text-gray-700 hover:text-blue-600 flex items-center">
                            {{ __('navigation.products') }}
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </a>
                        <div class="absolute left-0 mt-2 w-64 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                            <div class="py-2">
                                <a href="{{ route('products.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ __('navigation.all_products') }}</a>
                                <a href="{{ route('technical-sheets.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ __('navigation.technical_sheets') }}</a>
                                <a href="{{ route('color-simulator') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ __('navigation.color_simulator') }}</a>
                                <a href="{{ route('calculator.index') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ __('navigation.calculator') }}</a>
                            </div>
                        </div>
                    </div>

                    <a href="{{ route('videos.index') }}" class="text-gray-700 hover:text-blue-600">{{ __('navigation.applications') }}</a>
                    <a href="{{ route('blog.index') }}" class="text-gray-700 hover:text-blue-600">{{ __('navigation.news') }}</a>
                    <a href="{{ route('promotions.index') }}" class="text-gray-700 hover:text-blue-600">{{ __('navigation.promotions') }}</a>
                    <a href="{{ route('documentation') }}" class="text-gray-700 hover:text-blue-600">{{ __('navigation.documentation') }}</a>
                    <a href="{{ route('production-units') }}" class="text-gray-700 hover:text-blue-600">{{ __('navigation.contact') }}</a>
                    <a href="{{ route('quotes.create') }}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">{{ __('navigation.quote') }}</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-12">
        <div class="container mx-auto px-6 py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">                <div>
                    <h3 class="text-lg font-semibold mb-4">À propos d'ENAP</h3>
                    <p class="text-gray-400">Entreprise Nationale des Peintures, leader algérien dans la production de revêtements organiques.</p>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Liens Rapides</h3>
                    <ul class="text-gray-400">
                        <li class="mb-2"><a href="{{ route('products.index') }}" class="hover:text-white">Nos Produits</a></li>
                        <li class="mb-2"><a href="{{ route('production-units') }}" class="hover:text-white">Unités de Production</a></li>
                        <li class="mb-2"><a href="{{ route('color-simulator') }}" class="hover:text-white">Simulateur de Couleurs</a></li>
                        <li class="mb-2"><a href="{{ route('quotes.create') }}" class="hover:text-white">Demander un Devis</a></li>
                    </ul>
                </div>
                    <h3 class="text-lg font-semibold mb-4">Nos Produits</h3>
                    <ul class="text-gray-400">
                        <li class="mb-2"><a href="#" class="hover:text-white">Peintures Bâtiment</a></li>
                        <li class="mb-2"><a href="#" class="hover:text-white">Peintures Industrielles</a></li>
                        <li class="mb-2"><a href="#" class="hover:text-white">Peintures Marines</a></li>
                        <li class="mb-2"><a href="#" class="hover:text-white">Vernis et Résines</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Contact</h3>
                    <ul class="text-gray-400">
                        <li class="mb-2">Tél: +213 XX XX XX XX</li>
                        <li class="mb-2">Email: <EMAIL></li>
                        <li class="mb-2">Siège: Oued-Smar, Alger</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-4">Réseaux Sociaux</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white">
                            <span class="sr-only">Facebook</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/></svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white">
                            <span class="sr-only">LinkedIn</span>
                            <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24"><path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/></svg>
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; {{ date('Y') }} ENAP - Tous droits réservés</p>
            </div>
        </div>
    </footer>

    <script>
    function searchComponent() {
        return {
            query: '',
            suggestions: [],
            showSuggestions: false,

            async getSuggestions() {
                if (this.query.length < 2) {
                    this.suggestions = [];
                    return;
                }

                try {
                    const response = await fetch(`{{ route('search.suggestions') }}?q=${encodeURIComponent(this.query)}`);
                    const data = await response.json();
                    this.suggestions = data;
                } catch (error) {
                    console.error('Error fetching suggestions:', error);
                    this.suggestions = [];
                }
            },

            performSearch() {
                if (this.query.trim()) {
                    window.location.href = `{{ route('search.index') }}?q=${encodeURIComponent(this.query.trim())}`;
                }
            }
        }
    }
    </script>
</body>
</html>
