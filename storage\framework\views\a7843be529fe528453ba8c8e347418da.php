

<?php $__env->startSection('content'); ?>
<div class="py-12 <?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">
    <div class="container mx-auto px-6">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold mb-4"><?php echo e(__('technical-sheets.library.title')); ?></h1>
            <p class="text-gray-600"><?php echo e(__('technical-sheets.library.subtitle')); ?></p>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Search -->
                <div class="col-span-1 md:col-span-2">
                    <input type="text" 
                           placeholder="<?php echo e(__('technical-sheets.library.search')); ?>"
                           class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                </div>

                <!-- Category Filter -->
                <div>
                    <select class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value=""><?php echo e(__('technical-sheets.library.filter.category')); ?></option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category->id); ?>">
                                <?php echo e($category->getTranslation('name', app()->getLocale())); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Sort -->
                <div>
                    <select class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value="newest"><?php echo e(__('technical-sheets.library.filter.date')); ?></option>
                        <option value="downloads"><?php echo e(__('technical-sheets.library.filter.downloads')); ?></option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Technical Sheets Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $sheets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sheet): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden group">
                <div class="p-6">
                    <h3 class="font-semibold text-lg mb-2">
                        <?php echo e($sheet->getTranslation('title', app()->getLocale())); ?>

                    </h3>
                    <div class="text-sm text-gray-600 mb-4">
                        <div class="mb-1">
                            <span class="font-medium"><?php echo e(__('technical-sheets.sheet.reference')); ?>:</span>
                            <?php echo e($sheet->reference_number); ?>

                        </div>
                        <div class="mb-1">
                            <span class="font-medium"><?php echo e(__('technical-sheets.sheet.version')); ?>:</span>
                            <?php echo e($sheet->version); ?>

                        </div>
                        <div>
                            <span class="font-medium"><?php echo e(__('technical-sheets.sheet.published')); ?>:</span>
                            <?php echo e($sheet->publication_date->format('d/m/Y')); ?>

                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between mt-4">
                        <a href="<?php echo e(route('technical-sheets.show', $sheet)); ?>" 
                           class="text-blue-600 hover:text-blue-800 font-medium">
                            <?php echo e(__('technical-sheets.sheet.specifications')); ?> &rarr;
                        </a>
                        
                        <div class="flex items-center space-x-2">
                            <?php if($sheet->hasModel3D()): ?>
                            <a href="<?php echo e(route('technical-sheets.view3d', $sheet)); ?>" 
                               class="text-gray-600 hover:text-gray-800"
                               title="<?php echo e(__('technical-sheets.sheet.3d_view.title')); ?>">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z"/>
                                </svg>
                            </a>
                            <?php endif; ?>
                            
                            <a href="<?php echo e(route('technical-sheets.download', $sheet)); ?>" 
                               class="text-gray-600 hover:text-gray-800"
                               title="<?php echo e(__('technical-sheets.sheet.download.pdf')); ?>">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            <?php echo e($sheets->links()); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\wsite-enap\resources\views/technical-sheets/index.blade.php ENDPATH**/ ?>