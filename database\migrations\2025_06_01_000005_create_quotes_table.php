<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('quotes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained('users');
            $table->string('reference_number')->unique();
            $table->string('client_name');
            $table->string('client_email');
            $table->string('client_phone')->nullable();
            $table->string('company_name')->nullable();
            $table->text('project_description');
            $table->string('surface_type');
            $table->decimal('surface_area', 10, 2);
            $table->integer('coats_number')->default(1);
            $table->json('products')->nullable(); // Produits sélectionnés avec quantités
            $table->json('technical_requirements')->nullable();
            $table->decimal('total_amount', 12, 2)->nullable();
            $table->string('status')->default('pending'); // pending, processed, accepted, rejected
            $table->text('internal_notes')->nullable();
            $table->timestamp('valid_until')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('quotes');
    }
};
