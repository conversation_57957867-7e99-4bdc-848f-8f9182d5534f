<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class RoomTemplate extends Model
{
    use SoftDeletes, HasTranslations;

    protected $fillable = [
        'name',
        'image_path',
        'mask_areas',
        'room_type',
        'is_active'
    ];

    public $translatable = ['name', 'description'];

    protected $casts = [
        'mask_areas' => 'array',
        'is_active' => 'boolean'
    ];

    public function simulations()
    {
        return $this->hasMany(SavedSimulation::class);
    }
}
