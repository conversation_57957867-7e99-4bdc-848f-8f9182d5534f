<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    public function run()
    {
        $products = [
            [
                'name' => [
                    'fr' => 'Peinture Acrylique Mate Premium',
                    'ar' => 'دهان أكريليك مطفي ممتاز'
                ],
                'description' => [
                    'fr' => 'Peinture acrylique mate de haute qualité pour murs intérieurs',
                    'ar' => 'دهان أكريليك مطفي عالي الجودة للجدران الداخلية'
                ],
                'type' => 'building',
                'surface_type' => 'interior',
                'finish_type' => 'matte',
                'coverage' => '10',
                'drying_time' => '30',
                'is_available' => true
            ],
            [
                'name' => [
                    'fr' => 'Peinture Époxy Industrielle',
                    'ar' => 'دهان إيبوكسي صناعي'
                ],
                'description' => [
                    'fr' => 'Revêtement époxy bi-composant pour sols industriels',
                    'ar' => 'طلاء إيبوكسي ثنائي المكون للأرضيات الصناعية'
                ],
                'type' => 'industrial',
                'surface_type' => 'floor',
                'finish_type' => 'glossy',
                'coverage' => '6',
                'drying_time' => '240',
                'is_available' => true
            ]
        ];

        foreach ($products as $product) {
            Product::create($product);
        }
    }
}
