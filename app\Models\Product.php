<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'technical_description',
        'category_id',
        'sku',
        'barcode',
        'price',
        'professional_price',
        'stock',
        'unit',
        'coverage_rate',
        'technical_specs',
        'application_steps',
        'safety_instructions',
        'meta_data',
        'is_active',
        'featured'
    ];

    protected $casts = [
        'technical_specs' => 'array',
        'application_steps' => 'array',
        'safety_instructions' => 'array',
        'meta_data' => 'array',
        'is_active' => 'boolean',
        'featured' => 'boolean',
        'price' => 'decimal:2',
        'professional_price' => 'decimal:2',
        'coverage_rate' => 'decimal:2'
    ];

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    public function colors()
    {
        return $this->belongsToMany(ColorPalette::class, 'product_colors')
                    ->withPivot('is_available')
                    ->withTimestamps();
    }

    public function calculateQuantityNeeded($surfaceArea, $coatsNumber = 1)
    {
        if (!$this->coverage_rate) {
            return null;
        }

        return ceil(($surfaceArea * $coatsNumber) / $this->coverage_rate);
    }
}
