<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('technical_description')->nullable();
            $table->foreignId('category_id')->constrained('product_categories');
            $table->string('sku')->unique();
            $table->string('barcode')->nullable();
            $table->decimal('price', 10, 2)->nullable();
            $table->decimal('professional_price', 10, 2)->nullable();
            $table->integer('stock')->default(0);
            $table->string('unit')->default('L'); // Litre par défaut
            $table->decimal('coverage_rate', 8, 2)->nullable(); // m²/L
            $table->json('technical_specs')->nullable();
            $table->json('application_steps')->nullable();
            $table->json('safety_instructions')->nullable();
            $table->json('meta_data')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('featured')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('products');
    }
};
