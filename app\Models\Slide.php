<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;

class Slide extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'title',
        'description',
        'image_path',
        'image_alt',
        'button_text',
        'button_url',
        'button_target',
        'order',
        'is_active',
        'background_color',
        'text_color',
        'text_position',
        'animation_type',
        'duration',
        'meta_data'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer',
        'duration' => 'integer',
        'meta_data' => 'array'
    ];

    protected $appends = ['image_url', 'localized_title', 'localized_description', 'localized_button_text'];

    /**
     * Scope pour les slides actifs
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope pour ordonner les slides
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('order', 'asc')->orderBy('created_at', 'asc');
    }

    /**
     * Relation avec les traductions
     */
    public function translations()
    {
        return $this->hasMany(SlideTranslation::class);
    }

    /**
     * Obtenir la traduction pour une locale donnée
     */
    public function translation($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        return $this->translations()->where('locale', $locale)->first();
    }

    /**
     * URL complète de l'image
     */
    public function getImageUrlAttribute()
    {
        if (!$this->image_path) {
            return asset('images/placeholder-slide.jpg');
        }

        if (filter_var($this->image_path, FILTER_VALIDATE_URL)) {
            return $this->image_path;
        }

        // Si le chemin commence par 'images/', utiliser asset() directement
        if (str_starts_with($this->image_path, 'images/')) {
            return asset($this->image_path);
        }

        // Sinon utiliser Storage::url() pour les fichiers uploadés
        return Storage::url($this->image_path);
    }

    /**
     * Titre localisé
     */
    public function getLocalizedTitleAttribute()
    {
        $translation = $this->translation();
        return $translation ? $translation->title : $this->title;
    }

    /**
     * Description localisée
     */
    public function getLocalizedDescriptionAttribute()
    {
        $translation = $this->translation();
        return $translation ? $translation->description : $this->description;
    }

    /**
     * Texte du bouton localisé
     */
    public function getLocalizedButtonTextAttribute()
    {
        $translation = $this->translation();
        return $translation ? $translation->button_text : $this->button_text;
    }

    /**
     * Supprimer l'image lors de la suppression du slide
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($slide) {
            if ($slide->image_path && !filter_var($slide->image_path, FILTER_VALIDATE_URL)) {
                Storage::delete($slide->image_path);
            }
        });
    }

    /**
     * Réorganiser les slides
     */
    public static function reorder(array $slideIds)
    {
        foreach ($slideIds as $index => $slideId) {
            static::where('id', $slideId)->update(['order' => $index + 1]);
        }
    }

    /**
     * Obtenir le prochain ordre disponible
     */
    public static function getNextOrder()
    {
        return static::max('order') + 1;
    }

    /**
     * Dupliquer un slide
     */
    public function duplicate()
    {
        $newSlide = $this->replicate();
        $newSlide->title = $this->title . ' (Copie)';
        $newSlide->order = static::getNextOrder();
        $newSlide->is_active = false;
        $newSlide->save();

        // Dupliquer les traductions
        foreach ($this->translations as $translation) {
            $newTranslation = $translation->replicate();
            $newTranslation->slide_id = $newSlide->id;
            $newTranslation->title = $translation->title . ' (Copie)';
            $newTranslation->save();
        }

        return $newSlide;
    }
}
