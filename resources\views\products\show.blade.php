@extends('layouts.app')

@section('content')
<div class="py-12 {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <div class="container mx-auto px-6">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="md:flex">
                <!-- Product Image -->
                <div class="md:w-1/2">
                    <img src="{{ $product->image_url }}" alt="{{ $product->getTranslation('name', app()->getLocale()) }}" class="w-full h-96 object-cover">
                </div>

                <!-- Product Info -->
                <div class="md:w-1/2 p-8">
                    <h1 class="text-3xl font-bold mb-4">{{ $product->getTranslation('name', app()->getLocale()) }}</h1>
                    <p class="text-gray-600 mb-6">{{ $product->getTranslation('description', app()->getLocale()) }}</p>

                    <!-- Technical Specifications -->
                    <div class="mb-6">
                        <h2 class="text-xl font-semibold mb-4">{{ __('products.product.specifications') }}</h2>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <span class="text-gray-600">{{ __('products.filters.attributes.surface') }}:</span>
                                <span class="font-medium">{{ $product->surface_type }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">{{ __('products.filters.attributes.finish') }}:</span>
                                <span class="font-medium">{{ $product->finish_type }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">{{ __('Séchage') }}:</span>
                                <span class="font-medium">{{ $product->drying_time }}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">{{ __('Rendement') }}:</span>
                                <span class="font-medium">{{ $product->coverage }} m²/L</span>
                            </div>
                        </div>
                    </div>

                    <!-- Application Method -->
                    <div class="mb-6">
                        <h2 class="text-xl font-semibold mb-4">{{ __('products.product.applications') }}</h2>
                        <p class="text-gray-600">{{ $product->getTranslation('application_method', app()->getLocale()) }}</p>
                    </div>

                    <!-- Call to Action -->
                    <div class="flex space-x-4">
                        <a href="{{ route('quotes.create', ['product_id' => $product->id]) }}" 
                           class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                            {{ __('products.product.request_quote') }}
                        </a>
                        <a href="{{ route('color-simulator', ['product_id' => $product->id]) }}" 
                           class="bg-gray-100 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors">
                            {{ __('products.product.colors') }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="p-8 border-t">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Usage Guidelines -->
                    <div>
                        <h2 class="text-xl font-semibold mb-4">Guide d'Utilisation</h2>
                        <div class="prose">
                            {!! $product->usage_guidelines !!}
                        </div>
                    </div>

                    <!-- Safety Information -->
                    <div>
                        <h2 class="text-xl font-semibold mb-4">Informations de Sécurité</h2>
                        <div class="prose">
                            {!! $product->safety_info !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Products -->
        @if($relatedProducts->count() > 0)
        <div class="mt-12">
            <h2 class="text-2xl font-bold mb-6">Produits Similaires</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @foreach($relatedProducts as $related)
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <img src="{{ $related->image_url }}" alt="{{ $related->name }}" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-2">{{ $related->name }}</h3>
                        <p class="text-gray-600 mb-4">{{ $related->short_description }}</p>
                        <a href="{{ route('products.show', $related->id) }}" class="text-blue-600 hover:text-blue-800">
                            En savoir plus →
                        </a>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
