<?php

namespace App\Http\Controllers;

use App\Models\TechnicalSheet;
use App\Models\TechnicalSheetCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class TechnicalSheetController extends Controller
{
    public function index(Request $request)
    {
        $query = TechnicalSheet::query()
            ->with(['product', 'category'])
            ->where('is_published', true);

        // Apply filters
        if ($request->has('category')) {
            $query->where('category_id', $request->category);
        }

        if ($request->has('product_type')) {
            $query->whereHas('product', function ($q) use ($request) {
                $q->where('type', $request->product_type);
            });
        }

        // Apply sorting
        $sort = $request->get('sort', 'newest');
        switch ($sort) {
            case 'downloads':
                $query->orderBy('downloads_count', 'desc');
                break;
            case 'oldest':
                $query->orderBy('publication_date', 'asc');
                break;
            case 'newest':
            default:
                $query->orderBy('publication_date', 'desc');
                break;
        }

        $sheets = $query->paginate(12);
        $categories = TechnicalSheetCategory::all();

        return view('technical-sheets.index', compact('sheets', 'categories'));
    }

    public function show(TechnicalSheet $technicalSheet)
    {
        return view('technical-sheets.show', [
            'sheet' => $technicalSheet->load(['product', 'category'])
        ]);
    }

    public function download(TechnicalSheet $technicalSheet)
    {
        $technicalSheet->incrementDownloads();

        return Storage::download($technicalSheet->pdf_url, $technicalSheet->reference_number . '.pdf');
    }

    public function email(Request $request, TechnicalSheet $technicalSheet)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        // Logic for sending email with PDF attachment
        // ...

        return response()->json([
            'message' => __('technical-sheets.email.success')
        ]);
    }

    public function qrCode(TechnicalSheet $technicalSheet)
    {
        $url = route('technical-sheets.show', $technicalSheet);
        
        return response(
            QrCode::size(300)->generate($url)
        )->header('Content-Type', 'image/svg+xml');
    }

    public function view3d(TechnicalSheet $technicalSheet)
    {
        if (!$technicalSheet->hasModel3D()) {
            abort(404);
        }

        return view('technical-sheets.3d-view', [
            'sheet' => $technicalSheet
        ]);
    }
}
