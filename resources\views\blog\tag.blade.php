@extends('layouts.app')

@section('meta')
    <title>{{ $tag->name }} - Articles - ENAP</title>
    <meta name="description" content="Découvrez tous les articles liés à {{ $tag->name }} sur ENAP.">
@endsection

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold mb-8">Articles avec le tag "{{ $tag->name }}"</h1>

        @if($tag->description)
            <div class="prose max-w-none mb-8">
                {{ $tag->description }}
            </div>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @forelse($posts as $post)
                <article class="bg-white rounded-lg shadow-md overflow-hidden">
                    @if($post->featured_image)
                        <img src="{{ asset('storage/' . $post->featured_image) }}" 
                             alt="{{ $post->title }}" 
                             class="w-full h-48 object-cover">
                    @endif
                    <div class="p-6">
                        <h2 class="text-xl font-semibold mb-2">
                            <a href="{{ route('blog.show', $post->slug) }}" 
                               class="text-gray-900 hover:text-blue-600">
                                {{ $post->title }}
                            </a>
                        </h2>
                        <div class="text-sm text-gray-600 mb-4">
                            <span>{{ $post->formatted_date }}</span>
                            @if($post->category)
                                <span class="mx-2">•</span>
                                <a href="{{ route('blog.category', $post->category->slug) }}" 
                                   class="hover:text-blue-600">
                                    {{ $post->category->name }}
                                </a>
                            @endif
                            <span class="mx-2">•</span>
                            <span>{{ $post->read_time }} min de lecture</span>
                        </div>
                        @if($post->excerpt)
                            <p class="text-gray-600 mb-4">{{ $post->excerpt }}</p>
                        @endif
                        <div class="flex flex-wrap gap-2">
                            @foreach($post->tags as $postTag)
                                <a href="{{ route('blog.tag', $postTag->slug) }}" 
                                   class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800 hover:bg-blue-100">
                                    {{ $postTag->name }}
                                </a>
                            @endforeach
                        </div>
                    </div>
                </article>
            @empty
                <div class="col-span-full text-center py-12">
                    <p class="text-gray-600">Aucun article trouvé pour ce tag.</p>
                </div>
            @endforelse
        </div>

        @if($posts->hasPages())
            <div class="mt-8">
                {{ $posts->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
