<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Product;
use Illuminate\Http\Request;

class CartController extends Controller
{
    public function index()
    {
        $cart = session()->get('cart', []);
        $products = collect($cart)->map(function ($item) {
            $product = Product::find($item['id']);
            return [
                'id' => $product->id,
                'name' => $product->name,
                'price' => $product->price,
                'quantity' => $item['quantity'],
                'image' => $product->primaryImage?->url,
                'subtotal' => $product->price * $item['quantity']
            ];
        });

        $total = $products->sum('subtotal');

        return view('shop.cart', compact('products', 'total'));
    }

    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1'
        ]);

        $cart = session()->get('cart', []);
        
        if (isset($cart[$request->product_id])) {
            $cart[$request->product_id]['quantity'] += $request->quantity;
        } else {
            $cart[$request->product_id] = [
                'id' => $request->product_id,
                'quantity' => $request->quantity
            ];
        }

        session()->put('cart', $cart);

        return response()->json([
            'message' => 'Produit ajouté au panier',
            'cartCount' => count($cart)
        ]);
    }

    public function update(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:0'
        ]);

        $cart = session()->get('cart', []);

        if ($request->quantity > 0) {
            $cart[$request->product_id]['quantity'] = $request->quantity;
        } else {
            unset($cart[$request->product_id]);
        }

        session()->put('cart', $cart);

        return response()->json([
            'message' => 'Panier mis à jour',
            'cartCount' => count($cart)
        ]);
    }

    public function remove(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id'
        ]);

        $cart = session()->get('cart', []);
        
        if (isset($cart[$request->product_id])) {
            unset($cart[$request->product_id]);
            session()->put('cart', $cart);
        }

        return response()->json([
            'message' => 'Produit retiré du panier',
            'cartCount' => count($cart)
        ]);
    }

    public function clear()
    {
        session()->forget('cart');

        return response()->json([
            'message' => 'Panier vidé',
            'cartCount' => 0
        ]);
    }
}
