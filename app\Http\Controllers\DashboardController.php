<?php

namespace App\Http\Controllers;

use App\Models\UserProject;
use App\Models\UserFavorite;
use App\Models\Quote;
use App\Models\EventRegistration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $user = Auth::user();
        
        // Statistics
        $stats = [
            'projects' => UserProject::where('user_id', $user->id)->count(),
            'favorites' => UserFavorite::where('user_id', $user->id)->count(),
            'quotes' => Quote::where('user_id', $user->id)->count(),
            'events' => EventRegistration::where('user_id', $user->id)->count(),
        ];

        // Recent projects
        $recentProjects = UserProject::where('user_id', $user->id)
            ->orderBy('updated_at', 'desc')
            ->limit(5)
            ->get();

        // Recent quotes
        $recentQuotes = Quote::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Upcoming events
        $upcomingEvents = EventRegistration::where('user_id', $user->id)
            ->whereHas('event', function($query) {
                $query->where('start_date', '>=', now());
            })
            ->with('event')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Recent favorites
        $recentFavorites = UserFavorite::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('dashboard.index', compact(
            'stats',
            'recentProjects',
            'recentQuotes',
            'upcomingEvents',
            'recentFavorites'
        ));
    }

    public function profile()
    {
        $user = Auth::user();
        $profile = $user->profile ?? new \App\Models\UserProfile();
        
        return view('dashboard.profile', compact('user', 'profile'));
    }

    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'company_name' => 'nullable|string|max:255',
            'job_title' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:10',
            'country' => 'nullable|string|max:100',
            'user_type' => 'required|in:individual,professional,distributor,architect,contractor',
            'siret' => 'nullable|string|max:20',
            'specialties' => 'nullable|array',
            'newsletter_subscribed' => 'boolean',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        // Update user basic info
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        // Handle avatar upload
        $avatarPath = null;
        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
        }

        // Update or create profile
        $profileData = $request->only([
            'company_name', 'job_title', 'phone', 'address', 'city',
            'postal_code', 'country', 'user_type', 'siret', 'specialties',
            'newsletter_subscribed'
        ]);

        if ($avatarPath) {
            $profileData['avatar'] = $avatarPath;
        }

        $user->profile()->updateOrCreate(
            ['user_id' => $user->id],
            $profileData
        );

        return redirect()->route('dashboard.profile')
            ->with('success', __('dashboard.profile_updated'));
    }
}
