@extends('layouts.app')

@section('content')
<div class="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <!-- Search Header -->
    <div class="bg-gray-50 py-12">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto">
                <h1 class="text-3xl font-bold text-gray-900 mb-6">
                    @if($query)
                        {{ __('search.results_for') }} "{{ $query }}"
                    @else
                        {{ __('search.search_title') }}
                    @endif
                </h1>
                
                @if($query)
                    <p class="text-gray-600 mb-8">{{ __('search.showing_results', ['count' => $total]) }}</p>
                @endif

                <!-- Enhanced Search Bar -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <form method="GET" action="{{ route('search.index') }}" class="space-y-4">
                        <div class="flex flex-col md:flex-row gap-4">
                            <div class="flex-1">
                                <input type="text" name="q" value="{{ $query }}" 
                                       placeholder="{{ __('search.placeholder') }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="md:w-48">
                                <select name="type" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="all" {{ $type === 'all' ? 'selected' : '' }}>{{ __('search.all_content') }}</option>
                                    <option value="products" {{ $type === 'products' ? 'selected' : '' }}>{{ __('search.products') }}</option>
                                    <option value="technical_sheets" {{ $type === 'technical_sheets' ? 'selected' : '' }}>{{ __('search.technical_sheets') }}</option>
                                    <option value="videos" {{ $type === 'videos' ? 'selected' : '' }}>{{ __('search.videos') }}</option>
                                    <option value="blog" {{ $type === 'blog' ? 'selected' : '' }}>{{ __('search.blog') }}</option>
                                    <option value="promotions" {{ $type === 'promotions' ? 'selected' : '' }}>{{ __('search.promotions') }}</option>
                                </select>
                            </div>
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition duration-300">
                                {{ __('search.search_btn') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Results -->
    <section class="py-12">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                @if($results->isNotEmpty())
                    <div class="space-y-6">
                        @foreach($results as $result)
                        <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition duration-300">
                            <div class="flex items-start space-x-4">
                                @if($result['image'])
                                    <div class="flex-shrink-0">
                                        <img src="{{ $result['image'] }}" alt="{{ $result['title'] }}" 
                                             class="w-20 h-20 object-cover rounded-lg">
                                    </div>
                                @else
                                    <div class="flex-shrink-0 w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                                        @switch($result['type'])
                                            @case('product')
                                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                                </svg>
                                                @break
                                            @case('technical_sheet')
                                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                @break
                                            @case('video')
                                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                @break
                                            @case('blog_post')
                                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                                </svg>
                                                @break
                                            @case('promotion')
                                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                </svg>
                                                @break
                                        @endswitch
                                    </div>
                                @endif
                                
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ __('search.types.' . $result['type']) }}
                                        </span>
                                        @if($result['category'])
                                            <span class="text-sm text-gray-500">{{ $result['category'] }}</span>
                                        @endif
                                    </div>
                                    
                                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                                        <a href="{{ $result['url'] }}" class="hover:text-blue-600 transition duration-300">
                                            {{ $result['title'] }}
                                        </a>
                                    </h3>
                                    
                                    <p class="text-gray-600 mb-4 line-clamp-2">{{ $result['description'] }}</p>
                                    
                                    <div class="flex items-center justify-between">
                                        <a href="{{ $result['url'] }}" 
                                           class="inline-flex items-center text-blue-600 hover:text-blue-800 font-medium">
                                            {{ __('search.view_details') }}
                                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </a>
                                        
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                                            <span class="text-xs text-gray-500">{{ __('search.relevance') }}: {{ $result['relevance'] }}%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <!-- No Results -->
                    <div class="text-center py-12">
                        <svg class="w-24 h-24 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ __('search.no_results') }}</h2>
                        
                        @if($query)
                            <p class="text-gray-600 mb-8">{{ __('search.no_results_for', ['query' => $query]) }}</p>
                            
                            <!-- Search Tips -->
                            <div class="bg-blue-50 rounded-lg p-6 max-w-2xl mx-auto">
                                <h3 class="text-lg font-semibold text-blue-900 mb-4">{{ __('search.search_tips.title') }}</h3>
                                <ul class="text-left text-blue-800 space-y-2">
                                    <li class="flex items-start">
                                        <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        {{ __('search.search_tips.tip1') }}
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        {{ __('search.search_tips.tip2') }}
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        {{ __('search.search_tips.tip3') }}
                                    </li>
                                </ul>
                            </div>
                        @endif
                        
                        <!-- Suggestions -->
                        @if($suggestions->isNotEmpty())
                            <div class="mt-8">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ __('search.suggestions') }}</h3>
                                <div class="flex flex-wrap justify-center gap-2">
                                    @foreach($suggestions as $suggestion)
                                        <a href="{{ route('search.index', ['q' => $suggestion]) }}" 
                                           class="inline-block bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm transition duration-300">
                                            {{ $suggestion }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Popular Searches -->
    @if($query === '' || $results->isEmpty())
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-2xl font-bold text-gray-900 mb-8">{{ __('search.popular_searches') }}</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <a href="{{ route('search.index', ['q' => 'peinture bâtiment']) }}" 
                       class="bg-white rounded-lg p-4 shadow hover:shadow-lg transition duration-300">
                        <div class="text-blue-600 font-semibold">{{ __('search.building_paint') }}</div>
                    </a>
                    <a href="{{ route('search.index', ['q' => 'peinture industrielle']) }}" 
                       class="bg-white rounded-lg p-4 shadow hover:shadow-lg transition duration-300">
                        <div class="text-blue-600 font-semibold">{{ __('search.industrial_paint') }}</div>
                    </a>
                    <a href="{{ route('search.index', ['q' => 'vernis']) }}" 
                       class="bg-white rounded-lg p-4 shadow hover:shadow-lg transition duration-300">
                        <div class="text-blue-600 font-semibold">{{ __('search.varnish') }}</div>
                    </a>
                    <a href="{{ route('search.index', ['q' => 'anti-corrosion']) }}" 
                       class="bg-white rounded-lg p-4 shadow hover:shadow-lg transition duration-300">
                        <div class="text-blue-600 font-semibold">{{ __('search.anti_corrosion') }}</div>
                    </a>
                </div>
            </div>
        </div>
    </section>
    @endif
</div>
@endsection
