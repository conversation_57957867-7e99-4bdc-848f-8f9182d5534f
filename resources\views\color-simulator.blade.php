<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('color-simulator.title') }}
        </h2>
    </x-slot>

    <div class="color-simulator">
        <!-- Left Sidebar - Room Templates -->
        <div class="room-templates">
            <h2 class="text-lg font-semibold mb-4">{{ __('color-simulator.rooms') }}</h2>
            <div class="room-template-list">
                @foreach($roomTemplates as $room)
                <div class="room-template" data-room-id="{{ $room->id }}">
                    <img src="{{ asset('storage/' . $room->image_path) }}" alt="{{ $room->name }}">
                    <span>{{ $room->name }}</span>
                </div>
                @endforeach
            </div>
        </div>

        <!-- Main Canvas Area -->
        <div class="canvas-container">
            <canvas id="simulator-canvas"></canvas>
            <div class="toolbar">
                <button id="undo-btn" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M7.793 2.232a.75.75 0 01-.025 1.06L3.622 7.25h10.003a5.375 5.375 0 010 10.75H10.75a.75.75 0 010-1.5h2.875a3.875 3.875 0 000-7.75H3.622l4.146 3.957a.75.75 0 01-1.036 1.085l-5.5-5.25a.75.75 0 010-1.085l5.5-5.25a.75.75 0 011.06.025z" clip-rule="evenodd" />
                    </svg>
                    {{ __('color-simulator.undo') }}
                </button>
                <button id="redo-btn" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.207 2.232a.75.75 0 00.025 1.06l4.146 3.958H6.375a5.375 5.375 0 000 10.75H9.25a.75.75 0 000-1.5H6.375a3.875 3.875 0 010-7.75h10.003l-4.146 3.957a.75.75 0 001.036 1.085l5.5-5.25a.75.75 0 000-1.085l-5.5-5.25a.75.75 0 00-1.06.025z" clip-rule="evenodd" />
                    </svg>
                    {{ __('color-simulator.redo') }}
                </button>
                <button id="save-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10.75 2.75a.75.75 0 00-1.5 0v8.614L6.295 8.235a.75.75 0 10-1.09 1.03l4.25 4.5a.75.75 0 001.09 0l4.25-4.5a.75.75 0 00-1.09-1.03l-2.955 3.129V2.75z" />
                        <path d="M3.5 12.75a.75.75 0 00-1.5 0v2.5A2.75 2.75 0 004.75 18h10.5A2.75 2.75 0 0018 15.25v-2.5a.75.75 0 00-1.5 0v2.5c0 .69-.56 1.25-1.25 1.25H4.75c-.69 0-1.25-.56-1.25-1.25v-2.5z" />
                    </svg>
                    {{ __('color-simulator.save') }}
                </button>
                <button id="export-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10.75 2.75a.75.75 0 00-1.5 0v8.614L6.295 8.235a.75.75 0 10-1.09 1.03l4.25 4.5a.75.75 0 001.09 0l4.25-4.5a.75.75 0 00-1.09-1.03l-2.955 3.129V2.75z" />
                        <path d="M3.5 12.75a.75.75 0 00-1.5 0v2.5A2.75 2.75 0 004.75 18h10.5A2.75 2.75 0 0018 15.25v-2.5a.75.75 0 00-1.5 0v2.5c0 .69-.56 1.25-1.25 1.25H4.75c-.69 0-1.25-.56-1.25-1.25v-2.5z" />
                    </svg>
                    {{ __('color-simulator.export') }}
                </button>
            </div>
        </div>

        <!-- Right Sidebar - Color Panel -->
        <div class="color-panel">
            <div class="color-palette">
                <h2 class="text-lg font-semibold mb-4">{{ __('color-simulator.colors') }}</h2>
                @foreach($colors as $color)
                <div 
                    class="color" 
                    data-color="{{ $color->hex_value }}"
                    style="background-color: {{ $color->hex_value }}"
                    title="{{ $color->name }}">
                </div>
                @endforeach
            </div>

            <div class="harmonies">
                <h2 class="text-lg font-semibold mb-4">{{ __('color-simulator.harmonies') }}</h2>
                <div class="harmonies-container"></div>
            </div>

            <div class="product-suggestions">
                <h2 class="text-lg font-semibold mb-4">{{ __('color-simulator.suggestions') }}</h2>
                <div class="suggestions-container"></div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="{{ asset('js/color-simulator.js') }}"></script>
    @endpush
                            <select id="colorCategory" class="w-full rounded-md border-gray-300">
                                <option value="">Toutes les catégories</option>
                                @foreach($colorCategories as $category)
                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Color Palette -->
                        <div class="mb-6">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">Palette de Couleurs</h3>
                            <div id="colorPalette" class="grid grid-cols-4 gap-2">
                                @foreach($colors as $color)
                                <button 
                                    class="w-full h-12 rounded cursor-pointer hover:opacity-75 transition-opacity"
                                    style="background-color: {{ $color->hex_code }}"
                                    data-color="{{ $color->hex_code }}"
                                    data-name="{{ $color->name }}"
                                    onclick="selectColor('{{ $color->hex_code }}', '{{ $color->name }}')">
                                </button>
                                @endforeach
                            </div>
                        </div>

                        <!-- Selected Color Info -->
                        <div id="selectedColorInfo" class="hidden">
                            <div class="p-4 border rounded-lg">
                                <h3 class="font-medium mb-2">Couleur Sélectionnée</h3>
                                <div class="flex items-center space-x-3">
                                    <div id="selectedColorPreview" class="w-10 h-10 rounded"></div>
                                    <div>
                                        <p id="selectedColorName" class="font-medium"></p>
                                        <p id="selectedColorCode" class="text-sm text-gray-600"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Simulation Area -->
                <div class="md:col-span-2">
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <h2 class="text-xl font-semibold mb-4">Zone de Simulation</h2>
                        
                        <!-- Room Type Selection -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Type de Pièce</label>
                            <select id="roomType" class="w-full rounded-md border-gray-300" onchange="changeRoom()">
                                <option value="living-room">Salon</option>
                                <option value="bedroom">Chambre</option>
                                <option value="kitchen">Cuisine</option>
                                <option value="bathroom">Salle de bain</option>
                                <option value="exterior">Extérieur</option>
                            </select>
                        </div>

                        <!-- Simulation Canvas -->
                        <div class="relative">
                            <img id="roomImage" src="/images/rooms/living-room.jpg" alt="Room Preview" class="w-full rounded-lg">
                            <canvas id="simulationCanvas" class="absolute top-0 left-0 w-full h-full"></canvas>
                        </div>

                        <!-- Actions -->
                        <div class="mt-6 flex space-x-4">
                            <button onclick="resetSimulation()" class="bg-gray-200 text-gray-800 px-4 py-2 rounded hover:bg-gray-300">
                                Réinitialiser
                            </button>
                            <button onclick="saveSimulation()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                Sauvegarder
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
let canvas;
let context;
let currentColor = '';
let currentColorName = '';

document.addEventListener('DOMContentLoaded', function() {
    initializeCanvas();
});

function initializeCanvas() {
    canvas = document.getElementById('simulationCanvas');
    context = canvas.getContext('2d');
    
    // Set canvas size to match the image
    const roomImage = document.getElementById('roomImage');
    canvas.width = roomImage.width;
    canvas.height = roomImage.height;
    
    // Add click event listener for painting
    canvas.addEventListener('click', handleCanvasClick);
}

function selectColor(hexCode, name) {
    currentColor = hexCode;
    currentColorName = name;
    
    // Update selected color display
    const selectedColorInfo = document.getElementById('selectedColorInfo');
    const selectedColorPreview = document.getElementById('selectedColorPreview');
    const selectedColorName = document.getElementById('selectedColorName');
    const selectedColorCode = document.getElementById('selectedColorCode');
    
    selectedColorInfo.classList.remove('hidden');
    selectedColorPreview.style.backgroundColor = hexCode;
    selectedColorName.textContent = name;
    selectedColorCode.textContent = hexCode;
}

function handleCanvasClick(event) {
    if (!currentColor) return;
    
    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // Apply color with semi-transparency
    context.fillStyle = currentColor + '80'; // 50% opacity
    context.beginPath();
    context.arc(x, y, 50, 0, Math.PI * 2);
    context.fill();
}

function changeRoom() {
    const roomType = document.getElementById('roomType').value;
    const roomImage = document.getElementById('roomImage');
    roomImage.src = `/images/rooms/${roomType}.jpg`;
    
    // Reset canvas when changing rooms
    resetSimulation();
}

function resetSimulation() {
    context.clearRect(0, 0, canvas.width, canvas.height);
}

function saveSimulation() {
    // Convert canvas to image
    const dataUrl = canvas.toDataURL('image/png');
    
    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.download = 'simulation.png';
    link.href = dataUrl;
    link.click();
}
</script>
@endpush
@endsection
