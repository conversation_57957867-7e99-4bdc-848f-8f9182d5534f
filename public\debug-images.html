<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Images ENAP</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Test des Images ENAP</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Informations de Debug</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <strong>URL de base:</strong> <span id="baseUrl"></span>
                </div>
                <div>
                    <strong>Chemin images:</strong> <span id="imagesPath"></span>
                </div>
                <div>
                    <strong>Timestamp:</strong> <span id="timestamp"></span>
                </div>
                <div>
                    <strong>User Agent:</strong> <span id="userAgent"></span>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Test des Images Produits</h2>
            <div id="imagesGrid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                <!-- Les images seront ajoutées ici par JavaScript -->
            </div>
        </div>

        <div class="mt-8 text-center">
            <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                Retour à l'accueil
            </a>
            <a href="/dashboard" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors ml-4">
                Tableau de bord
            </a>
        </div>
    </div>

    <script>
        // Informations de debug
        document.getElementById('baseUrl').textContent = window.location.origin;
        document.getElementById('imagesPath').textContent = window.location.origin + '/images/';
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
        document.getElementById('userAgent').textContent = navigator.userAgent;

        // Liste des produits ENAP
        const products = [
            { name: 'Acryphob', image: 'acryphob.jpg', description: 'Peinture acrylique hydrofuge haute performance' },
            { name: 'Arris', image: 'arris.jpg', description: 'Peinture décorative texturée pour intérieur' },
            { name: 'Blancryl', image: 'blancryl.jpg', description: 'Peinture acrylique blanche mate' },
            { name: 'Dcosat', image: 'dcosat.jpg', description: 'Peinture satinée décorative lavable' },
            { name: 'Decoperle', image: 'decoperle.jpg', description: 'Peinture effet perlé nacré' },
            { name: 'Decopisine', image: 'decopisine.jpg', description: 'Peinture spéciale piscines et bassins' },
            { name: 'Decosoie', image: 'decosoie.jpg', description: 'Peinture effet soie brillante' },
            { name: 'Katifa', image: 'katifa.jpg', description: 'Peinture veloutée mate profonde' },
            { name: 'Les Dunes', image: 'lesdune.jpg', description: 'Peinture effet sable du désert' },
            { name: 'Poudre', image: 'ppoudre.jpg', description: 'Peinture en poudre électrostatique' },
            { name: 'Ref', image: 'ref.jpg', description: 'Peinture de référence multi-usage' },
            { name: 'Tenysil', image: 'tenysil.jpg', description: 'Peinture silicone anti-humidité' },
            { name: 'Terreal', image: 'terreal.jpg', description: 'Peinture terre cuite naturelle' }
        ];

        // Fonction pour tester le chargement d'une image
        function testImage(src) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = () => resolve({ success: true, src, width: img.width, height: img.height });
                img.onerror = () => resolve({ success: false, src, error: 'Impossible de charger l\'image' });
                img.src = src;
            });
        }

        // Créer les cartes de produits
        async function createProductCards() {
            const grid = document.getElementById('imagesGrid');
            
            for (const product of products) {
                const imageUrl = `/images/${product.image}`;
                const testResult = await testImage(imageUrl);
                
                const card = document.createElement('div');
                card.className = `bg-gray-50 rounded-lg overflow-hidden shadow hover:shadow-lg transition-shadow ${testResult.success ? 'border-green-500' : 'border-red-500'} border-2`;
                
                card.innerHTML = `
                    <div class="aspect-square relative">
                        ${testResult.success ? 
                            `<img src="${imageUrl}" alt="${product.name}" class="w-full h-full object-cover">
                             <div class="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">✓</div>` :
                            `<div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                             </div>
                             <div class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">✗</div>`
                        }
                    </div>
                    <div class="p-3">
                        <h3 class="font-semibold text-sm mb-1">${product.name}</h3>
                        <p class="text-xs text-gray-600 mb-2">${product.description}</p>
                        <div class="text-xs">
                            <div class="mb-1">
                                <strong>URL:</strong> <span class="font-mono">${imageUrl}</span>
                            </div>
                            <div class="mb-1">
                                <strong>Statut:</strong> 
                                <span class="${testResult.success ? 'text-green-600' : 'text-red-600'}">
                                    ${testResult.success ? 'Chargée' : 'Erreur'}
                                </span>
                            </div>
                            ${testResult.success ? 
                                `<div><strong>Dimensions:</strong> ${testResult.width}x${testResult.height}px</div>` :
                                `<div class="text-red-600"><strong>Erreur:</strong> ${testResult.error}</div>`
                            }
                        </div>
                    </div>
                `;
                
                grid.appendChild(card);
            }
        }

        // Initialiser les tests
        document.addEventListener('DOMContentLoaded', function() {
            createProductCards();
        });

        // Test de connectivité
        function testConnectivity() {
            fetch('/images/logo-enap.svg')
                .then(response => {
                    if (response.ok) {
                        console.log('✓ Connectivité OK - Dossier images accessible');
                    } else {
                        console.error('✗ Erreur de connectivité:', response.status);
                    }
                })
                .catch(error => {
                    console.error('✗ Erreur de réseau:', error);
                });
        }

        testConnectivity();
    </script>
</body>
</html>
