<?php

namespace App\Services;

use App\Models\User;
use App\Models\Product;
use App\Models\ColorPalette;
use App\Models\UserFavorite;
use App\Models\UserProject;
use App\Models\Quote;
use Illuminate\Support\Collection;

class RecommendationService
{
    public function getProductRecommendations(User $user, int $limit = 10): Collection
    {
        // Get user's project types and preferences
        $userProjects = UserProject::where('user_id', $user->id)->get();
        $userQuotes = Quote::where('user_id', $user->id)->get();
        $userFavorites = UserFavorite::where('user_id', $user->id)
            ->where('favoritable_type', Product::class)
            ->get();

        // Analyze user preferences
        $preferredTypes = $this->analyzeProjectTypes($userProjects, $userQuotes);
        $favoriteProducts = $userFavorites->pluck('favoritable_id');

        // Get recommendations based on:
        // 1. Similar project types
        // 2. Complementary products
        // 3. Popular products in user's category
        $recommendations = collect();

        // Type-based recommendations
        if ($preferredTypes->isNotEmpty()) {
            $typeRecommendations = Product::whereIn('category', $preferredTypes->keys())
                ->whereNotIn('id', $favoriteProducts)
                ->orderBy('popularity_score', 'desc')
                ->limit($limit / 2)
                ->get();
            
            $recommendations = $recommendations->merge($typeRecommendations);
        }

        // Collaborative filtering - users with similar preferences
        $similarUsers = $this->findSimilarUsers($user);
        if ($similarUsers->isNotEmpty()) {
            $collaborativeRecommendations = UserFavorite::whereIn('user_id', $similarUsers->pluck('id'))
                ->where('favoritable_type', Product::class)
                ->whereNotIn('favoritable_id', $favoriteProducts)
                ->with('favoritable')
                ->get()
                ->pluck('favoritable')
                ->unique('id')
                ->take($limit / 2);
            
            $recommendations = $recommendations->merge($collaborativeRecommendations);
        }

        // Fill remaining slots with trending products
        if ($recommendations->count() < $limit) {
            $trending = Product::where('is_trending', true)
                ->whereNotIn('id', $recommendations->pluck('id'))
                ->whereNotIn('id', $favoriteProducts)
                ->limit($limit - $recommendations->count())
                ->get();
            
            $recommendations = $recommendations->merge($trending);
        }

        return $recommendations->unique('id')->take($limit);
    }

    public function getColorRecommendations(User $user, int $limit = 8): Collection
    {
        $userProjects = UserProject::where('user_id', $user->id)->get();
        $usedColors = collect();
        
        foreach ($userProjects as $project) {
            if ($project->colors_used) {
                $usedColors = $usedColors->merge($project->colors_used);
            }
        }

        $usedColors = $usedColors->unique();

        // Get color harmonies for used colors
        $recommendations = collect();
        
        if ($usedColors->isNotEmpty()) {
            $baseColors = ColorPalette::whereIn('id', $usedColors)->get();
            
            foreach ($baseColors as $color) {
                $harmonies = $this->getColorHarmonies($color);
                $recommendations = $recommendations->merge($harmonies);
            }
        }

        // Add trending colors
        $trending = ColorPalette::where('is_trending', true)
            ->whereNotIn('id', $usedColors)
            ->whereNotIn('id', $recommendations->pluck('id'))
            ->limit($limit / 2)
            ->get();
        
        $recommendations = $recommendations->merge($trending);

        // Add seasonal colors
        $seasonal = $this->getSeasonalColors()
            ->whereNotIn('id', $usedColors)
            ->whereNotIn('id', $recommendations->pluck('id'))
            ->take($limit / 4);
        
        $recommendations = $recommendations->merge($seasonal);

        return $recommendations->unique('id')->take($limit);
    }

    public function getProjectRecommendations(User $user): array
    {
        $profile = $user->profile;
        $completedProjects = UserProject::where('user_id', $user->id)
            ->where('status', 'completed')
            ->get();

        $recommendations = [];

        // Based on user type
        if ($profile && $profile->isProfessional()) {
            $recommendations[] = [
                'type' => 'commercial',
                'title' => 'Projet Commercial',
                'description' => 'Idéal pour votre activité professionnelle',
                'estimated_duration' => '2-4 semaines',
                'complexity' => 'medium',
            ];
        }

        // Based on completed projects
        if ($completedProjects->where('type', 'residential')->count() > 0) {
            $recommendations[] = [
                'type' => 'residential',
                'title' => 'Extension Résidentielle',
                'description' => 'Continuez vos projets résidentiels',
                'estimated_duration' => '1-2 semaines',
                'complexity' => 'low',
            ];
        }

        // Seasonal recommendations
        $season = $this->getCurrentSeason();
        $seasonalProjects = $this->getSeasonalProjectRecommendations($season);
        $recommendations = array_merge($recommendations, $seasonalProjects);

        return $recommendations;
    }

    private function analyzeProjectTypes(Collection $projects, Collection $quotes): Collection
    {
        $types = collect();
        
        foreach ($projects as $project) {
            $types->push($project->type);
        }
        
        foreach ($quotes as $quote) {
            $types->push($quote->project_type);
        }
        
        return $types->countBy()->sortDesc();
    }

    private function findSimilarUsers(User $user): Collection
    {
        $userFavorites = UserFavorite::where('user_id', $user->id)
            ->pluck('favoritable_id')
            ->toArray();

        if (empty($userFavorites)) {
            return collect();
        }

        // Find users with similar favorites
        $similarUsers = UserFavorite::whereIn('favoritable_id', $userFavorites)
            ->where('user_id', '!=', $user->id)
            ->select('user_id')
            ->groupBy('user_id')
            ->havingRaw('COUNT(*) >= ?', [max(1, count($userFavorites) * 0.3)])
            ->pluck('user_id');

        return User::whereIn('id', $similarUsers)->get();
    }

    private function getColorHarmonies(ColorPalette $color): Collection
    {
        // Simple color harmony calculation
        $hue = $this->hexToHsl($color->hex_code)['h'];
        
        $harmonies = collect();
        
        // Complementary (180°)
        $complementaryHue = ($hue + 180) % 360;
        $harmonies = $harmonies->merge($this->findColorsByHue($complementaryHue, 30));
        
        // Triadic (120°)
        $triadicHue1 = ($hue + 120) % 360;
        $triadicHue2 = ($hue + 240) % 360;
        $harmonies = $harmonies->merge($this->findColorsByHue($triadicHue1, 30));
        $harmonies = $harmonies->merge($this->findColorsByHue($triadicHue2, 30));
        
        return $harmonies->unique('id');
    }

    private function getSeasonalColors(): Collection
    {
        $season = $this->getCurrentSeason();
        
        $seasonalTags = [
            'spring' => ['fresh', 'green', 'light'],
            'summer' => ['bright', 'warm', 'vibrant'],
            'autumn' => ['warm', 'orange', 'brown'],
            'winter' => ['cool', 'blue', 'white'],
        ];

        $tags = $seasonalTags[$season] ?? [];
        
        return ColorPalette::where(function($query) use ($tags) {
            foreach ($tags as $tag) {
                $query->orWhere('tags', 'like', "%{$tag}%");
            }
        })->limit(10)->get();
    }

    private function getCurrentSeason(): string
    {
        $month = now()->month;
        
        if ($month >= 3 && $month <= 5) return 'spring';
        if ($month >= 6 && $month <= 8) return 'summer';
        if ($month >= 9 && $month <= 11) return 'autumn';
        return 'winter';
    }

    private function getSeasonalProjectRecommendations(string $season): array
    {
        $recommendations = [
            'spring' => [
                [
                    'type' => 'residential',
                    'title' => 'Rafraîchissement Printemps',
                    'description' => 'Parfait pour renouveler votre intérieur',
                    'estimated_duration' => '1 semaine',
                    'complexity' => 'low',
                ]
            ],
            'summer' => [
                [
                    'type' => 'marine',
                    'title' => 'Protection Marine',
                    'description' => 'Protégez vos structures marines',
                    'estimated_duration' => '2-3 semaines',
                    'complexity' => 'high',
                ]
            ],
            'autumn' => [
                [
                    'type' => 'industrial',
                    'title' => 'Maintenance Industrielle',
                    'description' => 'Préparez vos installations pour l\'hiver',
                    'estimated_duration' => '3-4 semaines',
                    'complexity' => 'high',
                ]
            ],
            'winter' => [
                [
                    'type' => 'residential',
                    'title' => 'Rénovation Intérieure',
                    'description' => 'Idéal pour les travaux intérieurs',
                    'estimated_duration' => '2 semaines',
                    'complexity' => 'medium',
                ]
            ],
        ];

        return $recommendations[$season] ?? [];
    }

    private function hexToHsl(string $hex): array
    {
        $hex = ltrim($hex, '#');
        $r = hexdec(substr($hex, 0, 2)) / 255;
        $g = hexdec(substr($hex, 2, 2)) / 255;
        $b = hexdec(substr($hex, 4, 2)) / 255;

        $max = max($r, $g, $b);
        $min = min($r, $g, $b);
        $diff = $max - $min;

        $l = ($max + $min) / 2;

        if ($diff == 0) {
            $h = $s = 0;
        } else {
            $s = $l > 0.5 ? $diff / (2 - $max - $min) : $diff / ($max + $min);

            switch ($max) {
                case $r:
                    $h = (($g - $b) / $diff) + ($g < $b ? 6 : 0);
                    break;
                case $g:
                    $h = (($b - $r) / $diff) + 2;
                    break;
                case $b:
                    $h = (($r - $g) / $diff) + 4;
                    break;
            }
            $h /= 6;
        }

        return [
            'h' => $h * 360,
            's' => $s * 100,
            'l' => $l * 100,
        ];
    }

    private function findColorsByHue(float $targetHue, float $tolerance): Collection
    {
        // This would require storing HSL values in the database
        // For now, return a simple query
        return ColorPalette::limit(2)->get();
    }
}
