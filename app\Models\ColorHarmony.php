<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Translatable\HasTranslations;

class ColorHarmony extends Model
{
    use SoftDeletes, HasTranslations;

    protected $fillable = [
        'name',
        'type',
        'colors',
        'is_featured'
    ];

    public $translatable = ['name', 'description'];

    protected $casts = [
        'colors' => 'array',
        'is_featured' => 'boolean'
    ];

    public function colorPalettes()
    {
        return $this->belongsToMany(ColorPalette::class, 'color_harmony_colors');
    }
}
