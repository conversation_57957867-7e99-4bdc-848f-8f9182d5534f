<?php

namespace App\Http\Controllers;

use App\Models\Video;
use App\Models\Product;
use Illuminate\Http\Request;

class VideoController extends Controller
{
    public function index(Request $request)
    {
        $query = Video::query();

        // Apply filters
        if ($request->has('category')) {
            $query->where('category', $request->category);
        }

        if ($request->has('difficulty')) {
            $query->where('difficulty_level', $request->difficulty);
        }

        // Apply sorting
        $sort = $request->get('sort', 'newest');
        switch ($sort) {
            case 'popular':
                $query->orderBy('views', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        $videos = $query->paginate(12);
        $categories = Video::distinct('category')->pluck('category');

        return view('videos.index', compact('videos', 'categories'));
    }

    public function show(Video $video)
    {
        // Increment view count
        $video->increment('views');

        // Get related videos
        $relatedVideos = Video::where('category', $video->category)
            ->where('id', '!=', $video->id)
            ->take(4)
            ->get();

        // Get products used in the video
        $products = Product::whereIn('id', $video->product_ids)->get();

        return view('videos.show', compact('video', 'relatedVideos', 'products'));
    }
}
