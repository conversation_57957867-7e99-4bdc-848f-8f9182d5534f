@extends('layouts.app')

@section('title', $post->meta_title ?? $post->title)

@section('meta')
<meta name="description" content="{{ $post->meta_description ?? $post->excerpt }}">
@endsection

@section('content')
<div class="bg-gray-50 py-12">
    <div class="container mx-auto px-4">
        <article class="max-w-4xl mx-auto">
            <!-- En-tête de l'article -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                <!-- Image à la une -->
                <div class="relative h-[400px]">
                    <img src="{{ $post->featured_image }}" 
                         alt="{{ $post->title }}"
                         class="w-full h-full object-cover">
                    <!-- Catégorie -->
                    @if($post->category)
                    <a href="{{ route('blog.category', $post->category->slug) }}"
                       class="absolute top-6 left-6 bg-blue-600 text-white px-4 py-2 rounded-full font-medium hover:bg-blue-700">
                        {{ $post->category->name }}
                    </a>
                    @endif
                </div>

                <div class="p-8">
                    <!-- Titre et métadonnées -->
                    <h1 class="text-4xl font-bold text-gray-900 mb-6">{{ $post->title }}</h1>
                    <div class="flex items-center justify-between text-gray-600 mb-8">
                        <div class="flex items-center space-x-4">
                            @if($post->author->avatar)
                            <img src="{{ $post->author->avatar }}" 
                                 alt="{{ $post->author->name }}"
                                 class="w-10 h-10 rounded-full">
                            @endif
                            <div>
                                <div class="font-medium">{{ $post->author->name }}</div>
                                <div class="text-sm">{{ $post->formatted_date }}</div>
                            </div>
                        </div>
                        <div class="flex items-center text-sm">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            {{ $post->read_time }} min de lecture
                        </div>
                    </div>

                    <!-- Contenu de l'article -->
                    <div class="prose max-w-none">
                        {!! $post->content !!}
                    </div>

                    <!-- Tags -->
                    @if(!empty($post->tags))
                    <div class="border-t mt-8 pt-8">
                        <h3 class="text-lg font-semibold mb-4">Tags</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($post->tags as $tag)
                            <a href="{{ route('blog.tag', $tag) }}" 
                               class="px-4 py-2 bg-gray-100 text-gray-700 rounded-full hover:bg-blue-100 hover:text-blue-700">
                                {{ $tag }}
                            </a>
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Articles similaires -->
            @if($relatedPosts->isNotEmpty())
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6">Articles similaires</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    @foreach($relatedPosts as $relatedPost)
                    <a href="{{ route('blog.show', $relatedPost->slug) }}" 
                       class="group block">
                        <div class="relative h-48 rounded-lg overflow-hidden mb-4">
                            <img src="{{ $relatedPost->featured_image }}" 
                                 alt="{{ $relatedPost->title }}"
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 line-clamp-2 mb-2">
                            {{ $relatedPost->title }}
                        </h3>
                        <p class="text-sm text-gray-600">{{ $relatedPost->formatted_date }}</p>
                    </a>
                    @endforeach
                </div>
            </div>
            @endif
        </article>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css">
<style>
.prose pre {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
}
.prose img {
    border-radius: 0.5rem;
}
</style>
@endpush

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/prism.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-php.min.js"></script>
@endpush
