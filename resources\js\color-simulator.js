class ColorSimulator {
    constructor() {
        this.canvas = document.getElementById('simulator-canvas');
        this.ctx = this.canvas.getContext('2d');
        this.currentRoom = null;
        this.selectedColor = null;
        this.currentHarmony = null;
        this.paintedAreas = [];
        this.undoStack = [];
        this.redoStack = [];
        
        this.initialize();
    }

    initialize() {
        this.setupEventListeners();
        this.loadInitialRoom();
    }

    setupEventListeners() {
        // Room template selection
        document.querySelectorAll('.room-template').forEach(template => {
            template.addEventListener('click', (e) => this.changeRoom(e.target.dataset.roomId));
        });

        // Color selection
        document.querySelectorAll('.color-palette .color').forEach(color => {
            color.addEventListener('click', (e) => this.selectColor(e.target.dataset.color));
        });

        // Canvas painting
        this.canvas.addEventListener('mousedown', this.startPainting.bind(this));
        this.canvas.addEventListener('mousemove', this.paint.bind(this));
        this.canvas.addEventListener('mouseup', this.stopPainting.bind(this));
        this.canvas.addEventListener('mouseleave', this.stopPainting.bind(this));

        // Toolbar actions
        document.getElementById('undo-btn').addEventListener('click', () => this.undo());
        document.getElementById('redo-btn').addEventListener('click', () => this.redo());
        document.getElementById('save-btn').addEventListener('click', () => this.saveSimulation());
        document.getElementById('export-btn').addEventListener('click', () => this.exportSimulation());
    }

    async loadInitialRoom() {
        try {
            const response = await fetch('/api/rooms/default');
            const data = await response.json();
            await this.loadRoom(data.room);
        } catch (error) {
            console.error('Error loading initial room:', error);
        }
    }

    async changeRoom(roomId) {
        try {
            const response = await fetch(`/api/rooms/${roomId}`);
            const data = await response.json();
            await this.loadRoom(data.room);
        } catch (error) {
            console.error('Error changing room:', error);
        }
    }

    async loadRoom(roomData) {
        return new Promise((resolve, reject) => {
            const image = new Image();
            image.onload = () => {
                this.canvas.width = image.width;
                this.canvas.height = image.height;
                this.ctx.drawImage(image, 0, 0);
                this.currentRoom = roomData;
                this.paintedAreas = [];
                this.undoStack = [];
                this.redoStack = [];
                resolve();
            };
            image.onerror = reject;
            image.src = roomData.image_url;
        });
    }

    selectColor(color) {
        this.selectedColor = color;
        // Update UI to show selected color
        document.querySelectorAll('.color-palette .color').forEach(el => {
            el.classList.toggle('selected', el.dataset.color === color);
        });
    }

    startPainting(e) {
        if (!this.selectedColor) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // Save current canvas state for undo
        this.undoStack.push(this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height));
        this.redoStack = [];
        
        this.isPainting = true;
        this.paint(e);
    }

    paint(e) {
        if (!this.isPainting) return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // Implement flood fill or brush painting based on selected tool
        this.floodFill(Math.floor(x), Math.floor(y), this.selectedColor);
    }

    stopPainting() {
        this.isPainting = false;
    }

    floodFill(startX, startY, fillColor) {
        const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
        const pixels = imageData.data;
        
        const startPos = (startY * this.canvas.width + startX) * 4;
        const startR = pixels[startPos];
        const startG = pixels[startPos + 1];
        const startB = pixels[startPos + 2];
        
        const fillColorRGB = this.hexToRgb(fillColor);
        
        // Simple flood fill implementation
        const pixelsToCheck = [[startX, startY]];
        const checked = new Set();
        
        while (pixelsToCheck.length > 0) {
            const [x, y] = pixelsToCheck.pop();
            const pos = (y * this.canvas.width + x) * 4;
            
            if (checked.has(`${x},${y}`)) continue;
            checked.add(`${x},${y}`);
            
            if (this.matchesColor(pixels, pos, startR, startG, startB)) {
                pixels[pos] = fillColorRGB.r;
                pixels[pos + 1] = fillColorRGB.g;
                pixels[pos + 2] = fillColorRGB.b;
                
                // Add adjacent pixels
                if (x > 0) pixelsToCheck.push([x - 1, y]);
                if (x < this.canvas.width - 1) pixelsToCheck.push([x + 1, y]);
                if (y > 0) pixelsToCheck.push([x, y - 1]);
                if (y < this.canvas.height - 1) pixelsToCheck.push([x, y + 1]);
            }
        }
        
        this.ctx.putImageData(imageData, 0, 0);
    }

    matchesColor(pixels, pos, r, g, b) {
        const tolerance = 32;
        return Math.abs(pixels[pos] - r) <= tolerance &&
               Math.abs(pixels[pos + 1] - g) <= tolerance &&
               Math.abs(pixels[pos + 2] - b) <= tolerance;
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    undo() {
        if (this.undoStack.length > 0) {
            const previousState = this.undoStack.pop();
            this.redoStack.push(this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height));
            this.ctx.putImageData(previousState, 0, 0);
        }
    }

    redo() {
        if (this.redoStack.length > 0) {
            const nextState = this.redoStack.pop();
            this.undoStack.push(this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height));
            this.ctx.putImageData(nextState, 0, 0);
        }
    }

    async saveSimulation() {
        try {
            const simulationData = {
                room_id: this.currentRoom.id,
                image: this.canvas.toDataURL(),
                painted_areas: this.paintedAreas,
                color_harmony: this.currentHarmony
            };

            const response = await fetch('/api/simulations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify(simulationData)
            });

            const result = await response.json();
            if (result.success) {
                // Show success message
                this.showNotification('success', 'Simulation saved successfully');
            }
        } catch (error) {
            console.error('Error saving simulation:', error);
            this.showNotification('error', 'Error saving simulation');
        }
    }

    exportSimulation() {
        const link = document.createElement('a');
        link.download = 'room-simulation.png';
        link.href = this.canvas.toDataURL();
        link.click();
    }

    showNotification(type, message) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize the simulator when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.colorSimulator = new ColorSimulator();
});
