<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CustomerProfile extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'company_name',
        'registration_number',
        'tax_number',
        'phone',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'contact_person',
        'contact_email',
        'contact_phone',
        'customer_type',
        'is_approved',
        'notes',
        'approved_at'
    ];

    protected $casts = [
        'is_approved' => 'boolean',
        'approved_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function getFullAddressAttribute()
    {
        return "{$this->address}, {$this->city}, {$this->state} {$this->postal_code}, {$this->country}";
    }

    public function getCustomerTypeTextAttribute()
    {
        return match($this->customer_type) {
            'retail' => 'Détaillant',
            'wholesale' => 'Grossiste',
            'distributor' => 'Distributeur',
            default => $this->customer_type
        };
    }

    public function approve()
    {
        $this->update([
            'is_approved' => true,
            'approved_at' => now()
        ]);
    }

    public function reject()
    {
        $this->update([
            'is_approved' => false,
            'approved_at' => null
        ]);
    }

    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    public function scopePending($query)
    {
        return $query->where('is_approved', false);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('customer_type', $type);
    }
}
