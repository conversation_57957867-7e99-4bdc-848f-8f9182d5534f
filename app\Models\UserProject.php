<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserProject extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'type',
        'surface_area',
        'status',
        'start_date',
        'end_date',
        'budget',
        'colors_used',
        'products_used',
        'images',
        'notes',
    ];

    protected $casts = [
        'colors_used' => 'array',
        'products_used' => 'array',
        'images' => 'array',
        'start_date' => 'date',
        'end_date' => 'date',
        'budget' => 'decimal:2',
        'surface_area' => 'decimal:2',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getStatusBadgeAttribute()
    {
        $badges = [
            'planning' => 'bg-yellow-100 text-yellow-800',
            'in_progress' => 'bg-blue-100 text-blue-800',
            'completed' => 'bg-green-100 text-green-800',
            'on_hold' => 'bg-red-100 text-red-800',
        ];

        return $badges[$this->status] ?? 'bg-gray-100 text-gray-800';
    }

    public function getTypeBadgeAttribute()
    {
        $badges = [
            'residential' => 'bg-purple-100 text-purple-800',
            'commercial' => 'bg-indigo-100 text-indigo-800',
            'industrial' => 'bg-gray-100 text-gray-800',
            'marine' => 'bg-blue-100 text-blue-800',
            'automotive' => 'bg-red-100 text-red-800',
        ];

        return $badges[$this->type] ?? 'bg-gray-100 text-gray-800';
    }

    public function getProgressPercentageAttribute()
    {
        if (!$this->start_date || !$this->end_date) {
            return 0;
        }

        $total = $this->start_date->diffInDays($this->end_date);
        $elapsed = $this->start_date->diffInDays(now());

        if ($elapsed <= 0) return 0;
        if ($elapsed >= $total) return 100;

        return round(($elapsed / $total) * 100);
    }

    public function getEstimatedCostAttribute()
    {
        if (!$this->surface_area) {
            return null;
        }

        // Estimation basée sur le type de projet (prix au m²)
        $pricePerSqm = [
            'residential' => 25,
            'commercial' => 35,
            'industrial' => 45,
            'marine' => 60,
            'automotive' => 80,
        ];

        return $this->surface_area * ($pricePerSqm[$this->type] ?? 30);
    }
}
