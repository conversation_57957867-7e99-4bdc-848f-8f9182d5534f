<?php

namespace App\Http\Controllers;

use App\Models\ColorPalette;
use App\Models\Product;
use App\Models\ColorHarmony;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ColorChartController extends Controller
{
    public function index(Request $request)
    {
        $category = $request->get('category', 'all');
        $search = $request->get('search', '');
        $sort = $request->get('sort', 'name');

        $query = ColorPalette::where('is_available', true);

        // Apply filters
        if ($category !== 'all') {
            $query->where('category', $category);
        }

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('code', 'LIKE', "%{$search}%")
                  ->orWhere('hex_value', 'LIKE', "%{$search}%");
            });
        }

        // Apply sorting
        switch ($sort) {
            case 'hue':
                $query->orderByRaw('CAST(SUBSTRING(hex_value, 2) AS UNSIGNED)');
                break;
            case 'popularity':
                $query->orderBy('usage_count', 'desc');
                break;
            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;
            case 'name':
            default:
                $query->orderBy('name');
                break;
        }

        $colors = $query->paginate(24);
        $categories = ColorPalette::distinct('category')->pluck('category');
        $featuredHarmonies = ColorHarmony::where('is_featured', true)->with('colorPalettes')->get();

        return view('color-chart.index', compact('colors', 'categories', 'featuredHarmonies', 'category', 'search', 'sort'));
    }

    public function show(ColorPalette $color)
    {
        $color->load('products');
        
        // Get color harmonies
        $harmonies = $this->generateColorHarmonies($color->hex_value);
        
        // Get similar colors
        $similarColors = $this->getSimilarColors($color);
        
        // Get products using this color
        $products = $color->products()->where('is_active', true)->take(6)->get();

        // Increment view count
        $color->increment('view_count');

        return view('color-chart.show', compact('color', 'harmonies', 'similarColors', 'products'));
    }

    public function harmonies(Request $request)
    {
        $hexColor = $request->get('color');
        $type = $request->get('type', 'complementary');

        if (!$hexColor) {
            return response()->json(['error' => 'Color parameter required'], 400);
        }

        $harmonies = $this->generateColorHarmonies($hexColor, $type);

        return response()->json($harmonies);
    }

    public function compare(Request $request)
    {
        $colorIds = $request->get('colors', []);
        
        if (count($colorIds) < 2 || count($colorIds) > 5) {
            return response()->json(['error' => 'Please select 2-5 colors to compare'], 400);
        }

        $colors = ColorPalette::whereIn('id', $colorIds)->get();
        $comparison = $this->compareColors($colors);

        return response()->json($comparison);
    }

    public function export(Request $request)
    {
        $colorIds = $request->get('colors', []);
        $format = $request->get('format', 'pdf');

        $colors = ColorPalette::whereIn('id', $colorIds)->get();

        switch ($format) {
            case 'pdf':
                return $this->exportToPdf($colors);
            case 'ase':
                return $this->exportToAse($colors);
            case 'json':
                return $this->exportToJson($colors);
            default:
                return response()->json(['error' => 'Unsupported format'], 400);
        }
    }

    public function trending()
    {
        $trendingColors = Cache::remember('trending_colors', 3600, function () {
            return ColorPalette::where('is_available', true)
                ->orderBy('usage_count', 'desc')
                ->orderBy('view_count', 'desc')
                ->take(12)
                ->get();
        });

        return response()->json($trendingColors);
    }

    public function search(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $colors = ColorPalette::where('is_available', true)
            ->where(function ($q) use ($query) {
                $q->where('name', 'LIKE', "%{$query}%")
                  ->orWhere('code', 'LIKE', "%{$query}%");
            })
            ->limit(10)
            ->get(['id', 'name', 'code', 'hex_value']);

        return response()->json($colors);
    }

    private function generateColorHarmonies($hexColor, $type = 'all')
    {
        $rgb = $this->hexToRgb($hexColor);
        $hsl = $this->rgbToHsl($rgb['r'], $rgb['g'], $rgb['b']);

        $harmonies = [];

        if ($type === 'all' || $type === 'complementary') {
            $harmonies['complementary'] = $this->getComplementaryColor($hsl);
        }

        if ($type === 'all' || $type === 'triadic') {
            $harmonies['triadic'] = $this->getTriadicColors($hsl);
        }

        if ($type === 'all' || $type === 'analogous') {
            $harmonies['analogous'] = $this->getAnalogousColors($hsl);
        }

        if ($type === 'all' || $type === 'split_complementary') {
            $harmonies['split_complementary'] = $this->getSplitComplementaryColors($hsl);
        }

        if ($type === 'all' || $type === 'tetradic') {
            $harmonies['tetradic'] = $this->getTetradicColors($hsl);
        }

        return $harmonies;
    }

    private function getSimilarColors(ColorPalette $color, $limit = 8)
    {
        $rgb = $this->hexToRgb($color->hex_value);
        
        return ColorPalette::where('is_available', true)
            ->where('id', '!=', $color->id)
            ->get()
            ->map(function ($c) use ($rgb) {
                $cRgb = $this->hexToRgb($c->hex_value);
                $distance = $this->calculateColorDistance($rgb, $cRgb);
                $c->distance = $distance;
                return $c;
            })
            ->sortBy('distance')
            ->take($limit);
    }

    private function compareColors($colors)
    {
        $comparison = [
            'colors' => $colors,
            'analysis' => []
        ];

        foreach ($colors as $color) {
            $rgb = $this->hexToRgb($color->hex_value);
            $hsl = $this->rgbToHsl($rgb['r'], $rgb['g'], $rgb['b']);
            
            $comparison['analysis'][$color->id] = [
                'brightness' => $this->calculateBrightness($rgb),
                'saturation' => $hsl['s'],
                'hue' => $hsl['h'],
                'contrast_ratios' => $this->calculateContrastRatios($color, $colors)
            ];
        }

        return $comparison;
    }

    private function hexToRgb($hex)
    {
        $hex = ltrim($hex, '#');
        return [
            'r' => hexdec(substr($hex, 0, 2)),
            'g' => hexdec(substr($hex, 2, 2)),
            'b' => hexdec(substr($hex, 4, 2))
        ];
    }

    private function rgbToHsl($r, $g, $b)
    {
        $r /= 255;
        $g /= 255;
        $b /= 255;

        $max = max($r, $g, $b);
        $min = min($r, $g, $b);
        $diff = $max - $min;

        $l = ($max + $min) / 2;

        if ($diff == 0) {
            $h = $s = 0;
        } else {
            $s = $l > 0.5 ? $diff / (2 - $max - $min) : $diff / ($max + $min);

            switch ($max) {
                case $r:
                    $h = ($g - $b) / $diff + ($g < $b ? 6 : 0);
                    break;
                case $g:
                    $h = ($b - $r) / $diff + 2;
                    break;
                case $b:
                    $h = ($r - $g) / $diff + 4;
                    break;
            }
            $h /= 6;
        }

        return [
            'h' => $h * 360,
            's' => $s * 100,
            'l' => $l * 100
        ];
    }

    private function hslToHex($h, $s, $l)
    {
        $h /= 360;
        $s /= 100;
        $l /= 100;

        if ($s == 0) {
            $r = $g = $b = $l;
        } else {
            $hue2rgb = function ($p, $q, $t) {
                if ($t < 0) $t += 1;
                if ($t > 1) $t -= 1;
                if ($t < 1/6) return $p + ($q - $p) * 6 * $t;
                if ($t < 1/2) return $q;
                if ($t < 2/3) return $p + ($q - $p) * (2/3 - $t) * 6;
                return $p;
            };

            $q = $l < 0.5 ? $l * (1 + $s) : $l + $s - $l * $s;
            $p = 2 * $l - $q;
            $r = $hue2rgb($p, $q, $h + 1/3);
            $g = $hue2rgb($p, $q, $h);
            $b = $hue2rgb($p, $q, $h - 1/3);
        }

        return sprintf("#%02x%02x%02x", round($r * 255), round($g * 255), round($b * 255));
    }

    private function getComplementaryColor($hsl)
    {
        $complementaryHue = ($hsl['h'] + 180) % 360;
        return $this->hslToHex($complementaryHue, $hsl['s'], $hsl['l']);
    }

    private function getTriadicColors($hsl)
    {
        return [
            $this->hslToHex(($hsl['h'] + 120) % 360, $hsl['s'], $hsl['l']),
            $this->hslToHex(($hsl['h'] + 240) % 360, $hsl['s'], $hsl['l'])
        ];
    }

    private function getAnalogousColors($hsl)
    {
        return [
            $this->hslToHex(($hsl['h'] + 30) % 360, $hsl['s'], $hsl['l']),
            $this->hslToHex(($hsl['h'] - 30 + 360) % 360, $hsl['s'], $hsl['l'])
        ];
    }

    private function getSplitComplementaryColors($hsl)
    {
        return [
            $this->hslToHex(($hsl['h'] + 150) % 360, $hsl['s'], $hsl['l']),
            $this->hslToHex(($hsl['h'] + 210) % 360, $hsl['s'], $hsl['l'])
        ];
    }

    private function getTetradicColors($hsl)
    {
        return [
            $this->hslToHex(($hsl['h'] + 90) % 360, $hsl['s'], $hsl['l']),
            $this->hslToHex(($hsl['h'] + 180) % 360, $hsl['s'], $hsl['l']),
            $this->hslToHex(($hsl['h'] + 270) % 360, $hsl['s'], $hsl['l'])
        ];
    }

    private function calculateColorDistance($rgb1, $rgb2)
    {
        return sqrt(
            pow($rgb1['r'] - $rgb2['r'], 2) +
            pow($rgb1['g'] - $rgb2['g'], 2) +
            pow($rgb1['b'] - $rgb2['b'], 2)
        );
    }

    private function calculateBrightness($rgb)
    {
        return ($rgb['r'] * 299 + $rgb['g'] * 587 + $rgb['b'] * 114) / 1000;
    }

    private function calculateContrastRatios($color, $colors)
    {
        $ratios = [];
        $rgb1 = $this->hexToRgb($color->hex_value);
        $l1 = $this->calculateRelativeLuminance($rgb1);

        foreach ($colors as $otherColor) {
            if ($color->id !== $otherColor->id) {
                $rgb2 = $this->hexToRgb($otherColor->hex_value);
                $l2 = $this->calculateRelativeLuminance($rgb2);
                
                $ratio = ($l1 > $l2) ? ($l1 + 0.05) / ($l2 + 0.05) : ($l2 + 0.05) / ($l1 + 0.05);
                $ratios[$otherColor->id] = round($ratio, 2);
            }
        }

        return $ratios;
    }

    private function calculateRelativeLuminance($rgb)
    {
        $r = $rgb['r'] / 255;
        $g = $rgb['g'] / 255;
        $b = $rgb['b'] / 255;

        $r = $r <= 0.03928 ? $r / 12.92 : pow(($r + 0.055) / 1.055, 2.4);
        $g = $g <= 0.03928 ? $g / 12.92 : pow(($g + 0.055) / 1.055, 2.4);
        $b = $b <= 0.03928 ? $b / 12.92 : pow(($b + 0.055) / 1.055, 2.4);

        return 0.2126 * $r + 0.7152 * $g + 0.0722 * $b;
    }

    private function exportToPdf($colors)
    {
        // Implementation for PDF export
        // This would use a library like TCPDF or DomPDF
        return response()->json(['message' => 'PDF export not implemented yet']);
    }

    private function exportToAse($colors)
    {
        // Implementation for Adobe Swatch Exchange format
        return response()->json(['message' => 'ASE export not implemented yet']);
    }

    private function exportToJson($colors)
    {
        $data = $colors->map(function ($color) {
            return [
                'name' => $color->name,
                'code' => $color->code,
                'hex' => $color->hex_value,
                'rgb' => $color->rgb_value,
                'category' => $color->category
            ];
        });

        return response()->json($data);
    }
}
