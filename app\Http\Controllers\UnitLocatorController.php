<?php

namespace App\Http\Controllers;

use App\Models\ProductionUnit;
use Illuminate\Http\Request;

class UnitLocatorController extends Controller
{
    public function index()
    {
        $units = ProductionUnit::where('is_active', true)
            ->orderBy('name')
            ->get();

        return view('units.index', compact('units'));
    }

    public function show(ProductionUnit $unit)
    {
        return view('units.show', compact('unit'));
    }

    public function findNearest(Request $request)
    {
        $request->validate([
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'radius' => 'nullable|numeric|min:1|max:1000'
        ]);

        $radius = $request->input('radius', 50); // Default 50km radius
        $lat = $request->latitude;
        $lng = $request->longitude;

        // Haversine formula to find closest units
        $units = ProductionUnit::selectRaw("
                *,
                (6371 * acos(cos(radians(?)) 
                * cos(radians(latitude)) 
                * cos(radians(longitude) - radians(?)) 
                + sin(radians(?)) 
                * sin(radians(latitude)))) AS distance", [$lat, $lng, $lat])
            ->where('is_active', true)
            ->having('distance', '<=', $radius)
            ->orderBy('distance')
            ->get();

        return response()->json([
            'units' => $units,
            'total' => $units->count()
        ]);
    }

    public function openingHours(ProductionUnit $unit)
    {
        return response()->json([
            'opening_hours' => $unit->opening_hours,
            'is_open' => $unit->isOpenNow()
        ]);
    }
}
