<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Slide;
use Illuminate\Support\Facades\Storage;

class OptimizedSlidesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // <PERSON><PERSON>er le répertoire slides s'il n'existe pas
        if (!Storage::exists('slides')) {
            Storage::makeDirectory('slides');
        }

        // Slides de démonstration avec images optimisées
        $slides = [
            [
                'title' => 'ACRYPHOB - Excellence en Peintures',
                'description' => 'Découvrez ACRYPHOB, notre peinture acrylique de haute performance pour tous vos projets de décoration.',
                'image_path' => 'slides/acryphob_demo.jpg',
                'image_alt' => 'Peinture ACRYPHOB ENAP',
                'button_text' => 'Découvrir ACRYPHOB',
                'button_url' => '/products/acryphob',
                'button_target' => '_self',
                'order' => 1,
                'is_active' => true,
                'background_color' => '#1e40af',
                'text_color' => '#ffffff',
                'text_position' => 'left',
                'animation_type' => 'fade',
                'duration' => 5000,
                'meta_data' => [
                    'featured' => true,
                    'category' => 'products'
                ]
            ],
            [
                'title' => 'Innovation et Qualité ENAP',
                'description' => 'Leader algérien en peintures et revêtements, ENAP vous accompagne dans tous vos projets.',
                'image_path' => 'slides/enap_innovation.jpg',
                'image_alt' => 'Innovation ENAP',
                'button_text' => 'Notre Histoire',
                'button_url' => '/about',
                'button_target' => '_self',
                'order' => 2,
                'is_active' => true,
                'background_color' => '#059669',
                'text_color' => '#ffffff',
                'text_position' => 'center',
                'animation_type' => 'slide',
                'duration' => 6000,
                'meta_data' => [
                    'featured' => true,
                    'category' => 'company'
                ]
            ],
            [
                'title' => 'Solutions Professionnelles',
                'description' => 'Des peintures techniques et décoratives pour répondre aux exigences les plus strictes.',
                'image_path' => 'slides/solutions_pro.jpg',
                'image_alt' => 'Solutions professionnelles ENAP',
                'button_text' => 'Voir nos Solutions',
                'button_url' => '/solutions',
                'button_target' => '_self',
                'order' => 3,
                'is_active' => true,
                'background_color' => '#7c3aed',
                'text_color' => '#ffffff',
                'text_position' => 'right',
                'animation_type' => 'zoom',
                'duration' => 5500,
                'meta_data' => [
                    'featured' => true,
                    'category' => 'solutions'
                ]
            ]
        ];

        foreach ($slides as $slideData) {
            // Créer le slide
            $slide = Slide::create($slideData);
            
            // Simuler des images optimisées (en production, ces images seraient générées par le service d'optimisation)
            $optimizedImages = [
                'original' => [
                    'path' => $slideData['image_path'],
                    'url' => Storage::url($slideData['image_path']),
                    'width' => 2560,
                    'height' => 1440,
                    'size' => 850000
                ],
                '1920w' => [
                    'path' => str_replace('.jpg', '_1920w.jpg', $slideData['image_path']),
                    'url' => Storage::url(str_replace('.jpg', '_1920w.jpg', $slideData['image_path'])),
                    'width' => 1920,
                    'height' => 1080,
                    'size' => 420000
                ],
                '1200w' => [
                    'path' => str_replace('.jpg', '_1200w.jpg', $slideData['image_path']),
                    'url' => Storage::url(str_replace('.jpg', '_1200w.jpg', $slideData['image_path'])),
                    'width' => 1200,
                    'height' => 675,
                    'size' => 180000
                ],
                '768w' => [
                    'path' => str_replace('.jpg', '_768w.jpg', $slideData['image_path']),
                    'url' => Storage::url(str_replace('.jpg', '_768w.jpg', $slideData['image_path'])),
                    'width' => 768,
                    'height' => 432,
                    'size' => 85000
                ],
                '480w' => [
                    'path' => str_replace('.jpg', '_480w.jpg', $slideData['image_path']),
                    'url' => Storage::url(str_replace('.jpg', '_480w.jpg', $slideData['image_path'])),
                    'width' => 480,
                    'height' => 270,
                    'size' => 35000
                ]
            ];

            // Générer le srcset
            $srcset = [];
            foreach ($optimizedImages as $sizeName => $imageData) {
                if ($sizeName !== 'original') {
                    $srcset[] = "{$imageData['url']} {$imageData['width']}w";
                }
            }

            // Mettre à jour le slide avec les données d'optimisation
            $slide->update([
                'optimized_images' => $optimizedImages,
                'image_srcset' => implode(', ', $srcset)
            ]);

            $this->command->info("Slide créé: {$slide->title}");
        }

        $this->command->info('Slides optimisés créés avec succès !');
    }
}
