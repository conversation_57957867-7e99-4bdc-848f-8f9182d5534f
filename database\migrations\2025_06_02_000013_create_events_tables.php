<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('events', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description');
            $table->text('short_description')->nullable();
            $table->enum('type', ['formation', 'conference', 'workshop', 'webinar', 'exhibition', 'networking']);
            $table->datetime('start_date');
            $table->datetime('end_date');
            $table->string('location');
            $table->text('address')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->integer('max_participants')->nullable();
            $table->decimal('price', 10, 2)->default(0);
            $table->string('currency', 3)->default('DZD');
            $table->boolean('is_free')->default(true);
            $table->boolean('is_online')->default(false);
            $table->string('meeting_url')->nullable();
            $table->string('featured_image')->nullable();
            $table->json('gallery_images')->nullable();
            $table->json('agenda')->nullable();
            $table->json('requirements')->nullable();
            $table->text('target_audience')->nullable();
            $table->json('learning_objectives')->nullable();
            $table->boolean('certificate_provided')->default(false);
            $table->boolean('is_published')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->integer('view_count')->default(0);
            $table->datetime('registration_deadline')->nullable();
            $table->timestamps();

            $table->index(['is_published', 'start_date']);
            $table->index('type');
            $table->index('location');
            $table->index('is_featured');
        });

        Schema::create('event_registrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('registration_number')->unique();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email');
            $table->string('phone');
            $table->string('company')->nullable();
            $table->string('position')->nullable();
            $table->text('special_requirements')->nullable();
            $table->enum('status', ['confirmed', 'pending', 'cancelled', 'waitlist'])->default('confirmed');
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending');
            $table->string('payment_method')->nullable();
            $table->string('payment_reference')->nullable();
            $table->boolean('attended')->default(false);
            $table->boolean('certificate_issued')->default(false);
            $table->integer('feedback_rating')->nullable();
            $table->text('feedback_comment')->nullable();
            $table->timestamps();

            $table->index(['event_id', 'status']);
            $table->index('email');
            $table->index('registration_number');
        });

        Schema::create('event_speakers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('title')->nullable();
            $table->string('company')->nullable();
            $table->text('bio')->nullable();
            $table->string('photo')->nullable();
            $table->string('email')->nullable();
            $table->string('linkedin')->nullable();
            $table->string('twitter')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        Schema::create('event_speaker_assignments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_id')->constrained()->onDelete('cascade');
            $table->foreignId('event_speaker_id')->constrained()->onDelete('cascade');
            $table->string('role')->default('speaker'); // speaker, moderator, panelist
            $table->text('bio')->nullable(); // Event-specific bio
            $table->integer('order')->default(0);
            $table->timestamps();

            $table->unique(['event_id', 'event_speaker_id']);
        });

        Schema::create('event_newsletter_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique();
            $table->boolean('is_active')->default(true);
            $table->timestamp('subscribed_at');
            $table->timestamp('unsubscribed_at')->nullable();
            $table->timestamps();
        });

        Schema::create('color_palettes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->string('hex_value');
            $table->string('rgb_value')->nullable();
            $table->string('cmyk_value')->nullable();
            $table->string('category')->default('general');
            $table->json('complementary_colors')->nullable();
            $table->json('meta_data')->nullable();
            $table->boolean('is_available')->default(true);
            $table->integer('usage_count')->default(0);
            $table->integer('view_count')->default(0);
            $table->timestamps();

            $table->index('category');
            $table->index('is_available');
            $table->index(['usage_count', 'view_count']);
        });

        Schema::create('color_harmonies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // complementary, triadic, analogous, etc.
            $table->json('color_codes'); // Array of hex codes
            $table->text('description')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->timestamps();
        });

        Schema::create('color_harmony_palettes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('color_harmony_id')->constrained()->onDelete('cascade');
            $table->foreignId('color_palette_id')->constrained()->onDelete('cascade');
            $table->integer('order')->default(0);
            $table->timestamps();

            $table->unique(['color_harmony_id', 'color_palette_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('color_harmony_palettes');
        Schema::dropIfExists('color_harmonies');
        Schema::dropIfExists('color_palettes');
        Schema::dropIfExists('event_newsletter_subscriptions');
        Schema::dropIfExists('event_speaker_assignments');
        Schema::dropIfExists('event_speakers');
        Schema::dropIfExists('event_registrations');
        Schema::dropIfExists('events');
    }
};
