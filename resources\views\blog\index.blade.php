@extends('layouts.app')

@section('title', 'Blog ENAP - Actualités et Conseils')

@section('content')
<div class="bg-gray-50 py-12">
    <div class="container mx-auto px-4">
        <!-- En-tête -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Blog ENAP</h1>
            <p class="text-xl text-gray-600">Découvrez nos actualités, conseils techniques et tendances</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Barr<PERSON> latérale -->
            <div class="lg:col-span-1">
                <!-- Recherche -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                    <h3 class="text-lg font-semibold mb-4">Rechercher</h3>
                    <form action="{{ route('blog.index') }}" method="GET">
                        <div class="relative">
                            <input type="search" name="search" 
                                   class="w-full rounded-lg border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500"
                                   placeholder="Rechercher un article..."
                                   value="{{ request('search') }}">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Catégories -->
                <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                    <h3 class="text-lg font-semibold mb-4">Catégories</h3>
                    <ul class="space-y-3">
                        @foreach($categories as $category)
                        <li>
                            <a href="{{ route('blog.category', $category->slug) }}" 
                               class="flex justify-between items-center text-gray-600 hover:text-blue-600">
                                <span>{{ $category->name }}</span>
                                <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
                                    {{ $category->posts_count }}
                                </span>
                            </a>
                        </li>
                        @endforeach
                    </ul>
                </div>

                <!-- Tags -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">Tags</h3>
                    <div class="flex flex-wrap gap-2">
                        @foreach($tags as $tag)
                        <a href="{{ route('blog.tag', $tag) }}" 
                           class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-blue-100 hover:text-blue-700">
                            {{ $tag }}
                        </a>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Articles -->
            <div class="lg:col-span-3">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    @foreach($posts as $post)
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
                        <a href="{{ route('blog.show', $post->slug) }}" class="block">
                            <div class="relative h-48">
                                <img src="{{ $post->featured_image }}" 
                                     alt="{{ $post->title }}"
                                     class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                                @if($post->category)
                                <span class="absolute top-4 left-4 bg-blue-600 text-white text-sm px-3 py-1 rounded-full">
                                    {{ $post->category->name }}
                                </span>
                                @endif
                            </div>
                            <div class="p-6">
                                <h2 class="text-xl font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600">
                                    {{ $post->title }}
                                </h2>
                                <p class="text-gray-600 mb-4 line-clamp-3">{{ $post->excerpt }}</p>
                                <div class="flex items-center justify-between text-sm">
                                    <div class="flex items-center text-gray-500">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        {{ $post->read_time }} min de lecture
                                    </div>
                                    <span class="text-gray-500">{{ $post->formatted_date }}</span>
                                </div>
                            </div>
                        </a>
                    </article>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-12">
                    {{ $posts->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
