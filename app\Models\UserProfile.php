<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'company_name',
        'job_title',
        'phone',
        'address',
        'city',
        'postal_code',
        'country',
        'user_type',
        'siret',
        'specialties',
        'newsletter_subscribed',
        'preferences',
        'avatar',
        'last_activity',
    ];

    protected $casts = [
        'specialties' => 'array',
        'preferences' => 'array',
        'newsletter_subscribed' => 'boolean',
        'last_activity' => 'datetime',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getFullAddressAttribute()
    {
        $parts = array_filter([
            $this->address,
            $this->postal_code,
            $this->city,
            $this->country
        ]);
        
        return implode(', ', $parts);
    }

    public function isProfessional()
    {
        return in_array($this->user_type, ['professional', 'distributor', 'architect', 'contractor']);
    }

    public function getDisplayNameAttribute()
    {
        if ($this->company_name) {
            return $this->company_name;
        }
        
        return $this->user->name;
    }
}
