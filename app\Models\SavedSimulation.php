<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SavedSimulation extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'name',
        'image_path',
        'colors_used',
        'products_suggested',
        'notes'
    ];

    protected $casts = [
        'colors_used' => 'array',
        'products_suggested' => 'array'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function roomTemplate()
    {
        return $this->belongsTo(RoomTemplate::class);
    }

    public function products()
    {
        return $this->belongsToMany(Product::class, 'simulation_products')
            ->withPivot(['quantity', 'area']);
    }
}
