@extends('layouts.app')

@section('content')
<div class="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-red-900 to-red-600 text-white py-20">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-5xl font-bold mb-6">{{ __('promotions.hero.title') }}</h1>
                <p class="text-xl mb-8">{{ __('promotions.hero.subtitle') }}</p>
                <div class="flex justify-center">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                        <div class="flex items-center space-x-4">
                            <svg class="w-8 h-8 text-red-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            <span class="text-lg font-semibold">{{ __('promotions.hero.save_up_to') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="flex flex-col md:flex-row gap-4 items-center">
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('promotions.filters.type') }}</label>
                            <select id="type-filter" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent">
                                <option value="">{{ __('promotions.filters.all_types') }}</option>
                                <option value="percentage">{{ __('promotions.filters.percentage') }}</option>
                                <option value="fixed_amount">{{ __('promotions.filters.fixed_amount') }}</option>
                                <option value="seasonal">{{ __('promotions.filters.seasonal') }}</option>
                                <option value="clearance">{{ __('promotions.filters.clearance') }}</option>
                            </select>
                        </div>
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('promotions.filters.sort') }}</label>
                            <select id="sort-filter" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent">
                                <option value="newest">{{ __('promotions.filters.newest') }}</option>
                                <option value="ending_soon">{{ __('promotions.filters.ending_soon') }}</option>
                                <option value="biggest_discount">{{ __('promotions.filters.biggest_discount') }}</option>
                            </select>
                        </div>
                        <div class="md:w-auto">
                            <button id="apply-filters" class="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold transition duration-300 mt-6 md:mt-0">
                                {{ __('promotions.filters.apply') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Promotions Grid -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">{{ __('promotions.current.title') }}</h2>
                    <p class="text-gray-600">{{ __('promotions.current.subtitle') }}</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="promotions-grid">
                    @forelse($promotions as $promotion)
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300 border-2 border-transparent hover:border-red-200">
                        <!-- Promotion Badge -->
                        <div class="relative">
                            @if($promotion->image_url)
                                <img src="{{ $promotion->image_url }}" alt="{{ $promotion->title }}" class="w-full h-48 object-cover">
                            @else
                                <div class="w-full h-48 bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center">
                                    <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                            @endif
                            
                            <!-- Discount Badge -->
                            <div class="absolute top-4 right-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                                {{ $promotion->discount_text }}
                            </div>
                            
                            <!-- Expiring Soon Badge -->
                            @if($promotion->isExpiringSoon())
                            <div class="absolute top-4 left-4 bg-orange-500 text-white px-3 py-1 rounded-full text-xs font-semibold">
                                {{ __('promotions.expiring_soon') }}
                            </div>
                            @endif
                        </div>
                        
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $promotion->title }}</h3>
                            <p class="text-gray-600 mb-4 line-clamp-2">{{ $promotion->description }}</p>
                            
                            <!-- Countdown Timer -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                <div class="text-sm text-gray-600 mb-2">{{ __('promotions.time_remaining') }}</div>
                                <div class="flex items-center space-x-2">
                                    <div class="bg-red-600 text-white px-2 py-1 rounded text-sm font-bold">
                                        {{ $promotion->days_remaining }}
                                    </div>
                                    <span class="text-sm text-gray-600">{{ __('promotions.days_left') }}</span>
                                </div>
                            </div>
                            
                            <!-- Products Count -->
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-sm text-gray-500">
                                    {{ $promotion->products->count() }} {{ __('promotions.products_included') }}
                                </span>
                                <span class="text-xs text-gray-400">
                                    {{ __('promotions.valid_until') }} {{ $promotion->end_date->format('d/m/Y') }}
                                </span>
                            </div>
                            
                            <div class="flex gap-2">
                                <a href="{{ route('promotions.show', $promotion) }}" 
                                   class="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-center font-semibold transition duration-300">
                                    {{ __('promotions.view_details') }}
                                </a>
                                <button onclick="sharePromotion('{{ $promotion->title }}', '{{ route('promotions.show', $promotion) }}')"
                                        class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg transition duration-300">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="col-span-3 text-center py-12">
                        <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                        <p class="text-gray-600 mb-4">{{ __('promotions.no_promotions') }}</p>
                        <p class="text-gray-500">{{ __('promotions.check_back_soon') }}</p>
                    </div>
                    @endforelse
                </div>
                
                <!-- Pagination -->
                @if($promotions->hasPages())
                <div class="mt-12">
                    {{ $promotions->links() }}
                </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Newsletter Subscription -->
    <section class="py-16 bg-red-50">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-6">{{ __('promotions.newsletter.title') }}</h2>
                <p class="text-gray-600 mb-8">{{ __('promotions.newsletter.subtitle') }}</p>
                
                <form id="newsletter-form" class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" id="newsletter-email" placeholder="{{ __('promotions.newsletter.email_placeholder') }}" 
                           class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold transition duration-300">
                        {{ __('promotions.newsletter.subscribe') }}
                    </button>
                </form>
                
                <p class="text-sm text-gray-500 mt-4">{{ __('promotions.newsletter.privacy_notice') }}</p>
            </div>
        </div>
    </section>
</div>

<script>
// Filter functionality
document.getElementById('apply-filters').addEventListener('click', function() {
    const type = document.getElementById('type-filter').value;
    const sort = document.getElementById('sort-filter').value;
    
    const url = new URL(window.location);
    if (type) url.searchParams.set('type', type);
    else url.searchParams.delete('type');
    
    if (sort) url.searchParams.set('sort', sort);
    else url.searchParams.delete('sort');
    
    window.location.href = url.toString();
});

// Share functionality
function sharePromotion(title, url) {
    if (navigator.share) {
        navigator.share({
            title: title,
            url: url
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(url).then(() => {
            alert('{{ __("promotions.link_copied") }}');
        });
    }
}

// Newsletter subscription
document.getElementById('newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('newsletter-email').value;
    
    fetch('{{ route("promotions.newsletter") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ email: email })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            document.getElementById('newsletter-email').value = '';
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("promotions.newsletter.error") }}');
    });
});
</script>
@endsection
