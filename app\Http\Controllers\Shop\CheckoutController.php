<?php

namespace App\Http\Controllers\Shop;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\CustomerProfile;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class CheckoutController extends Controller
{
    public function index()
    {
        $cart = session()->get('cart', []);
        
        if (empty($cart)) {
            return redirect()->route('cart.index')
                ->with('error', 'Votre panier est vide');
        }

        $products = collect($cart)->map(function ($item) {
            $product = Product::find($item['id']);
            return [
                'id' => $product->id,
                'name' => $product->name,
                'price' => $product->price,
                'quantity' => $item['quantity'],
                'subtotal' => $product->price * $item['quantity']
            ];
        });

        $total = $products->sum('subtotal');
        $customerProfile = null;

        if (auth()->check()) {
            $customerProfile = CustomerProfile::where('user_id', auth()->id())->first();
        }

        return view('shop.checkout', compact('products', 'total', 'customerProfile'));
    }

    public function store(Request $request)
    {
        $cart = session()->get('cart', []);
        
        if (empty($cart)) {
            return redirect()->route('cart.index')
                ->with('error', 'Votre panier est vide');
        }

        $request->validate([
            'company_name' => 'required|string|max:255',
            'contact_person' => 'required|string|max:255',
            'email' => 'required|email',
            'phone' => 'required|string',
            'address' => 'required|string',
            'city' => 'required|string',
            'state' => 'required|string',
            'postal_code' => 'required|string',
            'notes' => 'nullable|string'
        ]);

        // Créer ou mettre à jour le profil client
        $customerProfile = CustomerProfile::updateOrCreate(
            ['user_id' => auth()->id()],
            [
                'company_name' => $request->company_name,
                'contact_person' => $request->contact_person,
                'contact_email' => $request->email,
                'contact_phone' => $request->phone,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'postal_code' => $request->postal_code
            ]
        );

        // Créer la commande
        $order = Order::create([
            'user_id' => auth()->id(),
            'customer_profile_id' => $customerProfile->id,
            'order_number' => 'CMD-' . strtoupper(Str::random(8)),
            'status' => 'pending',
            'payment_method' => $request->payment_method,
            'notes' => $request->notes,
            'total_amount' => 0, // Sera calculé plus tard
            'tax_amount' => 0,
            'shipping_amount' => 0
        ]);

        // Créer les items de la commande
        $total = 0;
        foreach ($cart as $item) {
            $product = Product::find($item['id']);
            $subtotal = $product->price * $item['quantity'];
            $total += $subtotal;

            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'quantity' => $item['quantity'],
                'unit_price' => $product->price,
                'subtotal' => $subtotal
            ]);
        }

        // Mettre à jour le total de la commande
        $order->update([
            'total_amount' => $total
        ]);

        // Vider le panier
        session()->forget('cart');

        // Rediriger vers la page de confirmation
        return redirect()->route('checkout.confirmation', $order)
            ->with('success', 'Votre commande a été enregistrée avec succès');
    }

    public function confirmation(Order $order)
    {
        if (!auth()->check() || (auth()->id() !== $order->user_id)) {
            abort(403);
        }

        return view('shop.confirmation', compact('order'));
    }
}
