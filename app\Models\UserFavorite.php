<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserFavorite extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'favoritable_type',
        'favoritable_id',
        'notes',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function favoritable()
    {
        return $this->morphTo();
    }

    public static function toggle($userId, $type, $id, $notes = null)
    {
        $favorite = static::where([
            'user_id' => $userId,
            'favoritable_type' => $type,
            'favoritable_id' => $id,
        ])->first();

        if ($favorite) {
            $favorite->delete();
            return false; // Removed from favorites
        } else {
            static::create([
                'user_id' => $userId,
                'favoritable_type' => $type,
                'favoritable_id' => $id,
                'notes' => $notes,
            ]);
            return true; // Added to favorites
        }
    }

    public static function isFavorite($userId, $type, $id)
    {
        return static::where([
            'user_id' => $userId,
            'favoritable_type' => $type,
            'favoritable_id' => $id,
        ])->exists();
    }
}
