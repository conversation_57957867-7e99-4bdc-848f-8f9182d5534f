<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    // Relationships
    public function profile()
    {
        return $this->hasOne(UserProfile::class);
    }

    public function projects()
    {
        return $this->hasMany(UserProject::class);
    }

    public function favorites()
    {
        return $this->hasMany(UserFavorite::class);
    }

    public function quotes()
    {
        return $this->hasMany(Quote::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function eventRegistrations()
    {
        return $this->hasMany(EventRegistration::class);
    }

    public function supportChats()
    {
        return $this->hasMany(SupportChat::class);
    }

    // Helper methods
    public function getDisplayNameAttribute()
    {
        return $this->profile && $this->profile->company_name
            ? $this->profile->company_name
            : $this->name;
    }

    public function isProfessional()
    {
        return $this->profile && $this->profile->isProfessional();
    }

    public function hasRole($role)
    {
        // Simple role check - can be expanded with a proper role system
        return $this->email === '<EMAIL>' && $role === 'admin';
    }

    public function updateLastActivity()
    {
        if ($this->profile) {
            $this->profile->update(['last_activity' => now()]);
        }
    }
}
