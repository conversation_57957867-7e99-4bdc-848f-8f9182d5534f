<?php

namespace App\Http\Controllers;

use App\Models\ColorPalette;
use App\Models\Product;
use App\Models\RoomTemplate;
use App\Models\ColorHarmony;
use App\Models\SavedSimulation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;

class ColorSimulatorController extends Controller
{
    public function index()
    {
        $colors = ColorPalette::where('is_available', true)->get();
        $products = Product::whereHas('attributes', function($query) {
            $query->where('name', 'type')->where('value', 'peinture');
        })->get();
        $roomTemplates = RoomTemplate::where('is_active', true)->get();
        $harmonies = ColorHarmony::where('is_featured', true)->with('colorPalettes')->get();
        $savedSimulations = auth()->check() ? auth()->user()->savedSimulations()->latest()->take(5)->get() : collect();

        return view('color-simulator', compact('colors', 'products', 'roomTemplates', 'harmonies', 'savedSimulations'));
    }

    /**
     * Get the default room template
     */
    public function getDefaultRoom()
    {
        $room = RoomTemplate::with('translations')->first();
        return response()->json(['room' => $room]);
    }

    /**
     * Get a specific room template
     */
    public function getRoom($id)
    {
        $room = RoomTemplate::with('translations')->findOrFail($id);
        return response()->json(['room' => $room]);
    }

    public function getRooms()
    {
        $rooms = RoomTemplate::where('is_active', true)
            ->get()
            ->map(function ($room) {
                return [
                    'id' => $room->id,
                    'name' => $room->name,
                    'type' => $room->room_type,
                    'image_url' => asset('storage/' . $room->image_path),
                    'mask_areas' => $room->mask_areas
                ];
            });

        return response()->json($rooms);
    }

    public function getHarmonies($colorId)
    {
        $color = ColorPalette::findOrFail($colorId);
        $harmonies = ColorHarmony::where('is_featured', true)
            ->whereJsonContains('colors', $colorId)
            ->with('colorPalettes')
            ->get()
            ->map(function ($harmony) {
                return [
                    'id' => $harmony->id,
                    'name' => $harmony->name,
                    'type' => $harmony->type,
                    'colors' => $harmony->colorPalettes->map(function ($color) {
                        return [
                            'id' => $color->id,
                            'name' => $color->name,
                            'hex' => $color->hex_value,
                            'rgb' => $color->rgb_value
                        ];
                    })
                ];
            });

        return response()->json($harmonies);
    }

    /**
     * Get the default room template
     */
    public function getDefaultRoom()
    {
        $room = RoomTemplate::with('translations')->first();
        return response()->json(['room' => $room]);
    }

    /**
     * Get a specific room template
     */
    public function getRoom($id)
    {
        $room = RoomTemplate::with('translations')->findOrFail($id);
        return response()->json(['room' => $room]);
    }

    /**
     * Save a simulation
     */
    public function saveSimulation(Request $request)
    {
        $validated = $request->validate([
            'room_id' => 'required|exists:room_templates,id',
            'image' => 'required|string',
            'painted_areas' => 'required|array',
            'color_harmony' => 'nullable|exists:color_harmonies,id'
        ]);

        $simulation = new SavedSimulation();
        $simulation->user_id = auth()->id();
        $simulation->room_template_id = $validated['room_id'];
        $simulation->image_data = $validated['image'];
        $simulation->painted_areas = $validated['painted_areas'];
        $simulation->color_harmony_id = $validated['color_harmony'];
        $simulation->save();

        return response()->json([
            'success' => true,
            'simulation' => $simulation
        ]);
    }

    /**
     * Get color harmonies for a given color
     */
    public function getColorHarmonies($colorHex)
    {
        $color = Color::fromHex($colorHex);
        $harmonies = ColorHarmony::with('translations')
            ->get()
            ->map(function ($harmony) use ($color) {
                return [
                    'id' => $harmony->id,
                    'name' => $harmony->name,
                    'colors' => $harmony->calculateColors($color)
                ];
            });

        return response()->json(['harmonies' => $harmonies]);
    }

    /**
     * Get product suggestions based on a color
     */
    public function getProductSuggestions($colorHex)
    {
        $color = Color::fromHex($colorHex);
        
        // Find products with similar colors
        $products = Product::with(['translations', 'images'])
            ->whereHas('attributes', function ($query) use ($color) {
                $query->where('type', 'color')
                    ->where(function ($q) use ($color) {
                        $q->whereRaw('ABS(CAST(value->"$.hue" AS DECIMAL) - ?) <= 15', [$color->hue]);
                    });
            })
            ->take(6)
            ->get();

        return response()->json(['products' => $products]);
    }    public function saveSimulation(Request $request)
    {
        $validated = $request->validate([
            'room_id' => 'required|exists:room_templates,id',
            'image' => 'required|string',
            'painted_areas' => 'required|array',
            'color_harmony' => 'nullable|exists:color_harmonies,id'
        ]);

        $simulation = new SavedSimulation();
        $simulation->user_id = auth()->id();
        $simulation->room_template_id = $validated['room_id'];
        $simulation->image_data = $validated['image'];
        $simulation->painted_areas = $validated['painted_areas'];
        $simulation->color_harmony_id = $validated['color_harmony'];
        $simulation->save();

        return response()->json([
            'success' => true,
            'simulation' => $simulation
        ]);
    }

    protected function calculateArea($positions)
    {
        // Calculer la surface approximative en m² basée sur les points du masque
        // Cette implémentation est simplifiée et devrait être adaptée selon vos besoins
        $points = collect($positions);
        $minX = $points->min('x');
        $maxX = $points->max('x');
        $minY = $points->min('y');
        $maxY = $points->max('y');
        
        // Convertir les pixels en mètres carrés (approximation)
        $pixelToMeter = 0.0002645833; // environ 1 pixel = 0.026cm
        return ($maxX - $minX) * ($maxY - $minY) * $pixelToMeter * $pixelToMeter;
    }
}
