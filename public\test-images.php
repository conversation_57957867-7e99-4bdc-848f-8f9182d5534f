<?php
// Script de test pour vérifier les images
echo "<h1>Test des images ENAP</h1>";

$imagesPath = __DIR__ . '/images';
echo "<p>Chemin des images: $imagesPath</p>";

if (is_dir($imagesPath)) {
    echo "<p style='color: green;'>✓ Le dossier images existe</p>";
    
    $files = scandir($imagesPath);
    $imageFiles = array_filter($files, function($file) use ($imagesPath) {
        $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
        return in_array($ext, ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp']) && is_file($imagesPath . '/' . $file);
    });
    
    echo "<p>Images trouvées: " . count($imageFiles) . "</p>";
    
    if (count($imageFiles) > 0) {
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 20px; margin-top: 20px;'>";
        
        foreach ($imageFiles as $file) {
            $filePath = '/images/' . $file;
            $fileSize = filesize($imagesPath . '/' . $file);
            $fileSizeKB = round($fileSize / 1024, 1);
            
            echo "<div style='border: 1px solid #ddd; padding: 10px; border-radius: 8px;'>";
            echo "<img src='$filePath' alt='$file' style='width: 100%; height: 150px; object-fit: cover; border-radius: 4px;'>";
            echo "<p style='margin: 10px 0 5px 0; font-weight: bold;'>$file</p>";
            echo "<p style='margin: 0; color: #666; font-size: 12px;'>$fileSizeKB KB</p>";
            echo "</div>";
        }
        
        echo "</div>";
    } else {
        echo "<p style='color: red;'>✗ Aucune image trouvée dans le dossier</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Le dossier images n'existe pas</p>";
}

// Test des URLs Laravel
echo "<h2>Test des URLs Laravel</h2>";
echo "<p>URL de base: " . (isset($_SERVER['HTTP_HOST']) ? 'http://' . $_SERVER['HTTP_HOST'] : 'localhost') . "</p>";

// Produits de test
$products = [
    ['name' => 'Acryphob', 'image' => 'acryphob.jpg'],
    ['name' => 'Arris', 'image' => 'arris.jpg'],
    ['name' => 'Blancryl', 'image' => 'blancryl.jpg'],
    ['name' => 'Dcosat', 'image' => 'dcosat.jpg'],
    ['name' => 'Decoperle', 'image' => 'decoperle.jpg'],
];

echo "<h3>Test des produits ENAP</h3>";
echo "<div style='display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 15px;'>";

foreach ($products as $product) {
    $imagePath = '/images/' . $product['image'];
    $exists = file_exists($imagesPath . '/' . $product['image']);
    
    echo "<div style='border: 1px solid " . ($exists ? '#4ade80' : '#ef4444') . "; padding: 10px; border-radius: 8px;'>";
    
    if ($exists) {
        echo "<img src='$imagePath' alt='{$product['name']}' style='width: 100%; height: 100px; object-fit: cover; border-radius: 4px;'>";
        echo "<p style='margin: 8px 0 0 0; font-size: 14px; text-align: center;'>{$product['name']}</p>";
        echo "<p style='margin: 4px 0 0 0; font-size: 12px; color: #4ade80; text-align: center;'>✓ Disponible</p>";
    } else {
        echo "<div style='width: 100%; height: 100px; background: #f3f4f6; border-radius: 4px; display: flex; align-items: center; justify-content: center;'>";
        echo "<span style='color: #9ca3af;'>Image manquante</span>";
        echo "</div>";
        echo "<p style='margin: 8px 0 0 0; font-size: 14px; text-align: center;'>{$product['name']}</p>";
        echo "<p style='margin: 4px 0 0 0; font-size: 12px; color: #ef4444; text-align: center;'>✗ Manquant</p>";
    }
    
    echo "</div>";
}

echo "</div>";

echo "<h3>Instructions</h3>";
echo "<ol>";
echo "<li>Vérifiez que toutes les images sont présentes dans le dossier <code>public/images</code></li>";
echo "<li>Les images manquantes doivent être ajoutées avec les noms exacts</li>";
echo "<li>Formats supportés: JPG, PNG, GIF, SVG, WEBP</li>";
echo "<li>Taille recommandée: moins de 500KB par image</li>";
echo "</ol>";

echo "<p><a href='/dashboard' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 6px;'>Retour au tableau de bord</a></p>";
?>
