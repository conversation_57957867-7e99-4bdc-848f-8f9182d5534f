<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('user_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('company_name')->nullable();
            $table->string('job_title')->nullable();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country')->default('Algeria');
            $table->enum('user_type', ['individual', 'professional', 'distributor', 'architect', 'contractor'])->default('individual');
            $table->string('siret')->nullable(); // For professional users
            $table->text('specialties')->nullable(); // JSON field for professional specialties
            $table->boolean('newsletter_subscribed')->default(false);
            $table->json('preferences')->nullable(); // User preferences (language, units, etc.)
            $table->string('avatar')->nullable();
            $table->timestamp('last_activity')->nullable();
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('user_profiles');
    }
};
