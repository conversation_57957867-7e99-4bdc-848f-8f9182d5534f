<?php

namespace App\Services;

class Color
{
    public $hue;
    public $saturation;
    public $lightness;
    public $hex;

    public function __construct($hue, $saturation, $lightness)
    {
        $this->hue = $hue;
        $this->saturation = $saturation;
        $this->lightness = $lightness;
        $this->hex = $this->hslToHex($hue, $saturation, $lightness);
    }

    public static function fromHex($hex)
    {
        $hex = ltrim($hex, '#');
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));

        list($h, $s, $l) = self::rgbToHsl($r, $g, $b);
        return new self($h, $s, $l);
    }

    private static function rgbToHsl($r, $g, $b)
    {
        $r = $r / 255;
        $g = $g / 255;
        $b = $b / 255;

        $max = max($r, $g, $b);
        $min = min($r, $g, $b);
        $h = $s = $l = ($max + $min) / 2;

        if ($max == $min) {
            $h = $s = 0;
        } else {
            $d = $max - $min;
            $s = $l > 0.5 ? $d / (2 - $max - $min) : $d / ($max + $min);

            switch ($max) {
                case $r:
                    $h = ($g - $b) / $d + ($g < $b ? 6 : 0);
                    break;
                case $g:
                    $h = ($b - $r) / $d + 2;
                    break;
                case $b:
                    $h = ($r - $g) / $d + 4;
                    break;
            }

            $h /= 6;
        }

        return [
            round($h * 360),
            round($s * 100),
            round($l * 100)
        ];
    }

    public function hslToHex($h, $s, $l)
    {
        $h = $h / 360;
        $s = $s / 100;
        $l = $l / 100;

        if ($s == 0) {
            $r = $g = $b = $l;
        } else {
            $q = $l < 0.5 ? $l * (1 + $s) : $l + $s - $l * $s;
            $p = 2 * $l - $q;

            $r = $this->hueToRgb($p, $q, $h + 1/3);
            $g = $this->hueToRgb($p, $q, $h);
            $b = $this->hueToRgb($p, $q, $h - 1/3);
        }

        return sprintf("#%02x%02x%02x",
            round($r * 255),
            round($g * 255),
            round($b * 255)
        );
    }

    private function hueToRgb($p, $q, $t)
    {
        if ($t < 0) $t += 1;
        if ($t > 1) $t -= 1;
        if ($t < 1/6) return $p + ($q - $p) * 6 * $t;
        if ($t < 1/2) return $q;
        if ($t < 2/3) return $p + ($q - $p) * (2/3 - $t) * 6;
        return $p;
    }

    public function getComplementary()
    {
        $h = ($this->hue + 180) % 360;
        return new self($h, $this->saturation, $this->lightness);
    }

    public function getAnalogous()
    {
        return [
            new self(($this->hue - 30 + 360) % 360, $this->saturation, $this->lightness),
            new self(($this->hue + 30) % 360, $this->saturation, $this->lightness)
        ];
    }

    public function getTriadic()
    {
        return [
            new self(($this->hue + 120) % 360, $this->saturation, $this->lightness),
            new self(($this->hue + 240) % 360, $this->saturation, $this->lightness)
        ];
    }

    public function getTetradic()
    {
        return [
            new self(($this->hue + 90) % 360, $this->saturation, $this->lightness),
            new self(($this->hue + 180) % 360, $this->saturation, $this->lightness),
            new self(($this->hue + 270) % 360, $this->saturation, $this->lightness)
        ];
    }

    public function toArray()
    {
        return [
            'hex' => $this->hex,
            'hsl' => [
                'h' => $this->hue,
                's' => $this->saturation,
                'l' => $this->lightness
            ]
        ];
    }
}
