<?php

namespace App\Notifications;

use App\Models\Quote;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class QuoteStatusUpdated extends Notification implements ShouldQueue
{
    use Queueable;

    protected $quote;

    public function __construct(Quote $quote)
    {
        $this->quote = $quote;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        $statusMessages = [
            'pending' => 'Votre demande de devis est en cours de traitement.',
            'processing' => 'Votre devis est en cours de préparation par nos experts.',
            'completed' => 'Votre devis est prêt ! Vous pouvez le consulter dans votre espace client.',
            'cancelled' => 'Votre demande de devis a été annulée.',
        ];

        return (new MailMessage)
            ->subject('Mise à jour de votre devis - ' . $this->quote->project_name)
            ->greeting('Bonjour ' . $notifiable->name . ',')
            ->line($statusMessages[$this->quote->status] ?? 'Le statut de votre devis a été mis à jour.')
            ->line('Projet: ' . $this->quote->project_name)
            ->line('Statut: ' . ucfirst($this->quote->status))
            ->action('Voir le devis', route('quotes.show', $this->quote))
            ->line('Merci de faire confiance à ENAP !');
    }

    public function toArray($notifiable)
    {
        return [
            'quote_id' => $this->quote->id,
            'project_name' => $this->quote->project_name,
            'status' => $this->quote->status,
            'message' => 'Le statut de votre devis "' . $this->quote->project_name . '" a été mis à jour.',
        ];
    }
}
