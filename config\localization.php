<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Application Locales
    |--------------------------------------------------------------------------
    |
    | Les langues supportées par l'application
    |
    */
    'locales' => [
        'fr' => [
            'name' => 'Français',
            'direction' => 'ltr',
            'flag' => '🇫🇷'
        ],
        'ar' => [
            'name' => 'العربية',
            'direction' => 'rtl',
            'flag' => '🇩🇿'
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Locale par défaut
    |--------------------------------------------------------------------------
    |
    | La langue par défaut de l'application
    |
    */
    'default_locale' => 'fr',

    /*
    |--------------------------------------------------------------------------
    | URLs avec langue
    |--------------------------------------------------------------------------
    |
    | Définit si les URLs doivent inclure le code de langue
    | Exemple: /fr/blog, /ar/blog
    |
    */
    'locale_in_url' => true,

    /*
    |--------------------------------------------------------------------------
    | Fallback Locale
    |--------------------------------------------------------------------------
    |
    | La langue à utiliser si une traduction n'est pas disponible
    |
    */
    'fallback_locale' => 'fr',
];
