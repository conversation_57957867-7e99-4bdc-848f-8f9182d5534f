<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('room_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('image_path');
            $table->json('mask_areas')->nullable(); // Zones prédéfinies pour la peinture
            $table->string('room_type'); // salon, chambre, cuisine, etc.
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('room_template_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('room_template_id')->constrained()->onDelete('cascade');
            $table->string('locale')->index();
            $table->string('name');
            $table->text('description')->nullable();
            $table->unique(['room_template_id', 'locale']);
            $table->timestamps();
        });

        Schema::create('color_harmonies', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // complementary, analogous, triadic, etc.
            $table->json('colors'); // Array of color IDs
            $table->boolean('is_featured')->default(false);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('color_harmony_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('color_harmony_id')->constrained()->onDelete('cascade');
            $table->string('locale')->index();
            $table->string('name');
            $table->text('description')->nullable();
            $table->unique(['color_harmony_id', 'locale']);
            $table->timestamps();
        });

        Schema::create('saved_simulations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('image_path');
            $table->json('colors_used'); // Array of used colors with their positions
            $table->json('products_suggested'); // Array of suggested product IDs
            $table->text('notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('saved_simulations');
        Schema::dropIfExists('color_harmony_translations');
        Schema::dropIfExists('color_harmonies');
        Schema::dropIfExists('room_template_translations');
        Schema::dropIfExists('room_templates');
    }
};
