<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\BlogCategoryController;
use App\Http\Controllers\Admin\BlogTagController;

Route::middleware(['auth', 'admin'])->group(function () {
    // Routes pour les articles
    Route::resource('posts', BlogController::class)
        ->except(['show'])
        ->names([
            'index' => 'admin.blog.posts.index',
            'create' => 'admin.blog.posts.create',
            'store' => 'admin.blog.posts.store',
            'edit' => 'admin.blog.posts.edit',
            'update' => 'admin.blog.posts.update',
            'destroy' => 'admin.blog.posts.destroy',
        ]);

    // Routes pour les catégories
    Route::resource('categories', BlogCategoryController::class)
        ->except(['show'])
        ->names([
            'index' => 'admin.blog.categories.index',
            'create' => 'admin.blog.categories.create',
            'store' => 'admin.blog.categories.store',
            'edit' => 'admin.blog.categories.edit',
            'update' => 'admin.blog.categories.update',
            'destroy' => 'admin.blog.categories.destroy',
        ]);

    // Routes pour les tags
    Route::resource('tags', BlogTagController::class)
        ->except(['show'])
        ->names([
            'index' => 'admin.blog.tags.index',
            'create' => 'admin.blog.tags.create',
            'store' => 'admin.blog.tags.store',
            'edit' => 'admin.blog.tags.edit',
            'update' => 'admin.blog.tags.update',
            'destroy' => 'admin.blog.tags.destroy',
        ]);
});
