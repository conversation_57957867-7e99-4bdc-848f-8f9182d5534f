@extends('layouts.app')

@section('title', 'Calculateur de Peinture - ENAP')

@section('content')
<div class="py-12">
    <div class="container mx-auto px-4">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Calculateur de Peinture</h1>
            <p class="text-lg text-gray-600">Estimez la quantité de peinture nécessaire pour votre projet</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Formulaire de calcul -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <form id="calculator-form" class="space-y-6">
                        <!-- Choix du produit -->
                        <div>
                            <label for="product_id" class="block text-sm font-medium text-gray-700 mb-1">
                                Produit
                            </label>
                            <select name="product_id" id="product_id" required
                                    class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Sélectionnez un produit</option>
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}" data-coverage="{{ $product->coverage }}">
                                        {{ $product->name }} ({{ $product->coverage }} m²/L)
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Type de surface -->
                        <div>
                            <label for="surface_type" class="block text-sm font-medium text-gray-700 mb-1">
                                Type de Surface
                            </label>
                            <select name="surface_type" id="surface_type" required
                                    class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Sélectionnez le type de surface</option>
                                <option value="wall">Murs</option>
                                <option value="ceiling">Plafond</option>
                                <option value="floor">Sol</option>
                                <option value="metal">Surface métallique</option>
                                <option value="wood">Surface en bois</option>
                            </select>
                        </div>

                        <!-- Dimensions -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="width" class="block text-sm font-medium text-gray-700 mb-1">
                                    Largeur (m)
                                </label>
                                <input type="number" name="width" id="width" required min="0.1" step="0.1"
                                       class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div>
                                <label for="length" class="block text-sm font-medium text-gray-700 mb-1">
                                    Longueur (m)
                                </label>
                                <input type="number" name="length" id="length" required min="0.1" step="0.1"
                                       class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- Hauteur pour les murs -->
                        <div x-data="{ showHeight: false }" x-show="showHeight" x-cloak>
                            <label for="height" class="block text-sm font-medium text-gray-700 mb-1">
                                Hauteur (m)
                            </label>
                            <input type="number" name="height" id="height" min="0.1" step="0.1"
                                   class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                        </div>

                        <!-- Ouvertures pour les murs -->
                        <div x-data="{ showOpenings: false }" x-show="showOpenings" x-cloak
                             class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="windows" class="block text-sm font-medium text-gray-700 mb-1">
                                    Nombre de fenêtres
                                </label>
                                <input type="number" name="windows" id="windows" min="0"
                                       class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                            </div>
                            <div>
                                <label for="doors" class="block text-sm font-medium text-gray-700 mb-1">
                                    Nombre de portes
                                </label>
                                <input type="number" name="doors" id="doors" min="0"
                                       class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- Nombre de couches -->
                        <div>
                            <label for="coats" class="block text-sm font-medium text-gray-700 mb-1">
                                Nombre de couches
                            </label>
                            <select name="coats" id="coats" required
                                    class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                <option value="1">1 couche</option>
                                <option value="2" selected>2 couches</option>
                                <option value="3">3 couches</option>
                            </select>
                        </div>

                        <!-- Marge de perte -->
                        <div>
                            <label for="wastage" class="block text-sm font-medium text-gray-700 mb-1">
                                Marge de perte
                            </label>
                            <select name="wastage" id="wastage" required
                                    class="w-full rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                <option value="10">10% (recommandé)</option>
                                <option value="15">15%</option>
                                <option value="20">20%</option>
                                <option value="25">25%</option>
                                <option value="30">30%</option>
                            </select>
                        </div>

                        <!-- Bouton de calcul -->
                        <div>
                            <button type="submit" 
                                    class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                Calculer
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Résultats -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-semibold mb-4">Résultats</h2>
                    
                    <div id="calculator-results" class="hidden space-y-6">
                        <!-- Surface à peindre -->
                        <div>
                            <div class="text-sm text-gray-600 mb-1">Surface à peindre</div>
                            <div class="text-2xl font-bold text-gray-900">
                                <span id="result-area">0</span> m²
                            </div>
                        </div>

                        <!-- Volume de peinture -->
                        <div>
                            <div class="text-sm text-gray-600 mb-1">Quantité de peinture nécessaire</div>
                            <div class="text-2xl font-bold text-gray-900">
                                <span id="result-volume">0</span> L
                            </div>
                        </div>

                        <!-- Conditionnements recommandés -->
                        <div>
                            <div class="text-sm text-gray-600 mb-2">Conditionnements recommandés</div>
                            <ul id="result-packaging" class="space-y-2"></ul>
                        </div>

                        <!-- Prix estimatif -->
                        <div class="border-t pt-4">
                            <div class="text-sm text-gray-600 mb-1">Prix estimatif</div>
                            <div class="text-2xl font-bold text-blue-600">
                                <span id="result-price">0</span> DA
                            </div>
                            <p class="text-xs text-gray-500 mt-1">
                                *Prix indicatif basé sur le tarif public
                            </p>
                        </div>

                        <!-- Actions -->
                        <div class="space-y-3">
                            <a href="#" id="quote-link" 
                               class="block w-full text-center bg-green-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-green-700 transition-colors">
                                Demander un devis
                            </a>
                            <button type="button" onclick="window.print()"
                                    class="block w-full text-center border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                                Imprimer
                            </button>
                        </div>
                    </div>

                    <!-- Message initial -->
                    <div id="calculator-placeholder" class="text-center py-8">
                        <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                        </svg>
                        <p class="text-gray-600">
                            Remplissez le formulaire pour obtenir une estimation de la quantité de peinture nécessaire
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('calculator-form');
    const surfaceTypeSelect = document.getElementById('surface_type');
    const resultsDiv = document.getElementById('calculator-results');
    const placeholderDiv = document.getElementById('calculator-placeholder');

    // Gérer l'affichage des champs supplémentaires selon le type de surface
    surfaceTypeSelect.addEventListener('change', function() {
        const showHeight = this.value === 'wall';
        document.querySelector('[x-data="{ showHeight: false }"]').setAttribute('x-data', `{ showHeight: ${showHeight} }`);
        document.querySelector('[x-data="{ showOpenings: false }"]').setAttribute('x-data', `{ showOpenings: ${showHeight} }`);
    });

    // Gérer la soumission du formulaire
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        
        fetch('/calculator/calculate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify(Object.fromEntries(formData))
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Mettre à jour les résultats
                document.getElementById('result-area').textContent = data.result.area;
                document.getElementById('result-volume').textContent = data.result.volume_needed;
                document.getElementById('result-price').textContent = data.result.price_estimate.toLocaleString();

                // Mettre à jour les conditionnements recommandés
                const packagingList = document.getElementById('result-packaging');
                packagingList.innerHTML = data.result.recommended_packaging
                    .map(p => `<li class="flex justify-between">
                                <span>${p.count}x ${p.volume}L</span>
                                <span class="font-medium">${(p.count * p.volume)}L</span>
                              </li>`)
                    .join('');

                // Mettre à jour le lien du devis
                const quoteLink = document.getElementById('quote-link');
                quoteLink.href = `/quote/create?product_id=${formData.get('product_id')}&volume=${data.result.volume_needed}`;

                // Afficher les résultats
                resultsDiv.classList.remove('hidden');
                placeholderDiv.classList.add('hidden');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Une erreur est survenue lors du calcul. Veuillez réessayer.');
        });
    });
});
</script>
@endpush

@push('styles')
<style>
[x-cloak] { display: none !important; }

@media print {
    header, footer, .container > :not(:first-child) {
        display: none !important;
    }
    .container {
        max-width: 100% !important;
        padding: 0 !important;
    }
    #calculator-results {
        page-break-inside: avoid;
    }
}
</style>
@endpush
