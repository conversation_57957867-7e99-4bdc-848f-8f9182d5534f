<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur d'Images Placeholder pour Slides ENAP</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .slides-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .slide-preview {
            position: relative;
            width: 100%;
            height: 200px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        .slide-preview:hover {
            transform: scale(1.05);
        }
        .slide-content {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            padding: 20px;
        }
        .slide-1 { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); }
        .slide-2 { background: linear-gradient(135deg, #059669 0%, #10b981 100%); }
        .slide-3 { background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%); }
        .slide-4 { background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); }
        .slide-5 { background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%); }
        
        .download-section {
            text-align: center;
            margin-top: 30px;
        }
        .download-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.3s ease;
        }
        .download-btn:hover {
            transform: translateY(-2px);
        }
        .instructions {
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .instructions h3 {
            color: #1e40af;
            margin-top: 0;
        }
        .instructions ol {
            color: #64748b;
        }
        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Générateur d'Images Placeholder pour Slides ENAP</h1>
        
        <div class="slides-grid">
            <div class="slide-preview slide-1" onclick="downloadSlide(1)">
                <div class="slide-content">
                    <div>
                        <h3>Excellence en Peintures</h3>
                        <p>Leader algérien depuis 1967</p>
                    </div>
                </div>
            </div>
            
            <div class="slide-preview slide-2" onclick="downloadSlide(2)">
                <div class="slide-content">
                    <div>
                        <h3>Innovation & Technologie</h3>
                        <p>40+ ans d'expertise</p>
                    </div>
                </div>
            </div>
            
            <div class="slide-preview slide-3" onclick="downloadSlide(3)">
                <div class="slide-content">
                    <div>
                        <h3>Simulateur de Couleurs</h3>
                        <p>Visualisation 3D révolutionnaire</p>
                    </div>
                </div>
            </div>
            
            <div class="slide-preview slide-4" onclick="downloadSlide(4)">
                <div class="slide-content">
                    <div>
                        <h3>Gamme Professionnelle</h3>
                        <p>Solutions spécialisées</p>
                    </div>
                </div>
            </div>
            
            <div class="slide-preview slide-5" onclick="downloadSlide(5)">
                <div class="slide-content">
                    <div>
                        <h3>Service Client</h3>
                        <p>Accompagnement personnalisé</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="download-section">
            <button class="download-btn" onclick="downloadAllSlides()">
                📥 Télécharger toutes les images (ZIP)
            </button>
            <button class="download-btn" onclick="generateCustomSlide()">
                ✨ Créer un slide personnalisé
            </button>
        </div>
        
        <div class="instructions">
            <h3>📋 Instructions d'utilisation</h3>
            <ol>
                <li><strong>Cliquez sur un slide</strong> pour télécharger l'image correspondante (1920x1080px)</li>
                <li><strong>Placez les images</strong> dans le dossier <code>public/images/slides/</code> de votre projet Laravel</li>
                <li><strong>Nommez les fichiers</strong> : slide-1.jpg, slide-2.jpg, slide-3.jpg, etc.</li>
                <li><strong>Exécutez les migrations</strong> : <code>php artisan migrate</code></li>
                <li><strong>Lancez le seeder</strong> : <code>php artisan db:seed --class=SlideSeeder</code></li>
                <li><strong>Vérifiez l'affichage</strong> sur votre page d'accueil</li>
            </ol>
            
            <h3>🎯 Recommandations</h3>
            <ul>
                <li><strong>Format optimal</strong> : 1920x1080px (16:9)</li>
                <li><strong>Poids recommandé</strong> : Moins de 500KB par image</li>
                <li><strong>Formats supportés</strong> : JPG, PNG, WebP</li>
                <li><strong>Qualité</strong> : Utilisez des images haute résolution pour un rendu professionnel</li>
            </ul>
        </div>
    </div>

    <script>
        function downloadSlide(slideNumber) {
            // Créer un canvas pour générer l'image
            const canvas = document.createElement('canvas');
            canvas.width = 1920;
            canvas.height = 1080;
            const ctx = canvas.getContext('2d');
            
            // Définir les gradients et contenus pour chaque slide
            const slideData = {
                1: {
                    gradient: ['#1e40af', '#3b82f6'],
                    title: 'Excellence en Peintures et Revêtements',
                    subtitle: 'Leader algérien depuis 1967 - ENAP'
                },
                2: {
                    gradient: ['#059669', '#10b981'],
                    title: 'Innovation et Technologie',
                    subtitle: 'Plus de 40 ans d\'expertise dans la peinture'
                },
                3: {
                    gradient: ['#7c3aed', '#a855f7'],
                    title: 'Simulateur de Couleurs Interactif',
                    subtitle: 'Visualisation 3D révolutionnaire'
                },
                4: {
                    gradient: ['#dc2626', '#ef4444'],
                    title: 'Gamme Professionnelle',
                    subtitle: 'Solutions spécialisées pour les pros'
                },
                5: {
                    gradient: ['#f59e0b', '#fbbf24'],
                    title: 'Service Client Exceptionnel',
                    subtitle: 'Accompagnement personnalisé'
                }
            };
            
            const data = slideData[slideNumber];
            
            // Créer le gradient
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, data.gradient[0]);
            gradient.addColorStop(1, data.gradient[1]);
            
            // Remplir le fond
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Ajouter un motif subtil
            ctx.globalAlpha = 0.1;
            for (let i = 0; i < 50; i++) {
                ctx.beginPath();
                ctx.arc(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    Math.random() * 100 + 20,
                    0,
                    2 * Math.PI
                );
                ctx.fillStyle = 'white';
                ctx.fill();
            }
            
            // Réinitialiser l'opacité
            ctx.globalAlpha = 1;
            
            // Ajouter le texte
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // Titre principal
            ctx.font = 'bold 120px Arial';
            ctx.fillText(data.title, canvas.width / 2, canvas.height / 2 - 60);
            
            // Sous-titre
            ctx.font = '60px Arial';
            ctx.globalAlpha = 0.9;
            ctx.fillText(data.subtitle, canvas.width / 2, canvas.height / 2 + 60);
            
            // Logo ENAP (simulé)
            ctx.globalAlpha = 0.8;
            ctx.font = 'bold 80px Arial';
            ctx.fillText('ENAP', canvas.width - 200, 100);
            
            // Télécharger l'image
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `slide-${slideNumber}.jpg`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/jpeg', 0.9);
        }
        
        function downloadAllSlides() {
            for (let i = 1; i <= 5; i++) {
                setTimeout(() => downloadSlide(i), i * 1000);
            }
            alert('Téléchargement de toutes les images en cours... Veuillez patienter.');
        }
        
        function generateCustomSlide() {
            const title = prompt('Titre du slide personnalisé:');
            const subtitle = prompt('Sous-titre:');
            
            if (title && subtitle) {
                // Logique pour créer un slide personnalisé
                const canvas = document.createElement('canvas');
                canvas.width = 1920;
                canvas.height = 1080;
                const ctx = canvas.getContext('2d');
                
                // Gradient personnalisé
                const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
                gradient.addColorStop(0, '#667eea');
                gradient.addColorStop(1, '#764ba2');
                
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // Texte personnalisé
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.font = 'bold 120px Arial';
                ctx.fillText(title, canvas.width / 2, canvas.height / 2 - 60);
                ctx.font = '60px Arial';
                ctx.fillText(subtitle, canvas.width / 2, canvas.height / 2 + 60);
                
                // Télécharger
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'slide-custom.jpg';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                }, 'image/jpeg', 0.9);
            }
        }
    </script>
</body>
</html>
