<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('slides', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('image_path');
            $table->string('image_alt')->nullable();
            $table->string('button_text')->nullable();
            $table->string('button_url')->nullable();
            $table->string('button_target')->default('_self'); // _self, _blank
            $table->integer('order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->string('background_color')->nullable();
            $table->string('text_color')->default('#ffffff');
            $table->enum('text_position', ['left', 'center', 'right'])->default('left');
            $table->enum('animation_type', ['fade', 'slide', 'zoom'])->default('fade');
            $table->integer('duration')->default(5000); // milliseconds
            $table->json('meta_data')->nullable(); // Additional data
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('slide_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('slide_id')->constrained()->onDelete('cascade');
            $table->string('locale', 5)->index();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('button_text')->nullable();
            $table->unique(['slide_id', 'locale']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('slide_translations');
        Schema::dropIfExists('slides');
    }
};
