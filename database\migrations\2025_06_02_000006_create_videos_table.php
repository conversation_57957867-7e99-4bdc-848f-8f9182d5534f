<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('videos', function (Blueprint $table) {
            $table->id();
            $table->json('title');
            $table->json('description')->nullable();
            $table->string('thumbnail_url');
            $table->string('video_url');
            $table->string('category');
            $table->string('difficulty_level');
            $table->integer('duration')->comment('Duration in seconds');
            $table->integer('views')->default(0);
            $table->json('product_ids')->nullable();
            $table->json('tools_needed')->nullable();
            $table->json('safety_notes')->nullable();
            $table->json('application_steps')->nullable();
            $table->timestamps();
        });

        Schema::create('product_video', function (Blueprint $table) {
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('video_id')->constrained()->onDelete('cascade');
            $table->primary(['product_id', 'video_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('product_video');
        Schema::dropIfExists('videos');
    }
};
