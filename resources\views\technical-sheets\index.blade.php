@extends('layouts.app')

@section('content')
<div class="py-12 {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <div class="container mx-auto px-6">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold mb-4">{{ __('technical-sheets.library.title') }}</h1>
            <p class="text-gray-600">{{ __('technical-sheets.library.subtitle') }}</p>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Search -->
                <div class="col-span-1 md:col-span-2">
                    <input type="text" 
                           placeholder="{{ __('technical-sheets.library.search') }}"
                           class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                </div>

                <!-- Category Filter -->
                <div>
                    <select class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value="">{{ __('technical-sheets.library.filter.category') }}</option>
                        @foreach($categories as $category)
                            <option value="{{ $category->id }}">
                                {{ $category->getTranslation('name', app()->getLocale()) }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Sort -->
                <div>
                    <select class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value="newest">{{ __('technical-sheets.library.filter.date') }}</option>
                        <option value="downloads">{{ __('technical-sheets.library.filter.downloads') }}</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Technical Sheets Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($sheets as $sheet)
            <div class="bg-white rounded-lg shadow-lg overflow-hidden group">
                <div class="p-6">
                    <h3 class="font-semibold text-lg mb-2">
                        {{ $sheet->getTranslation('title', app()->getLocale()) }}
                    </h3>
                    <div class="text-sm text-gray-600 mb-4">
                        <div class="mb-1">
                            <span class="font-medium">{{ __('technical-sheets.sheet.reference') }}:</span>
                            {{ $sheet->reference_number }}
                        </div>
                        <div class="mb-1">
                            <span class="font-medium">{{ __('technical-sheets.sheet.version') }}:</span>
                            {{ $sheet->version }}
                        </div>
                        <div>
                            <span class="font-medium">{{ __('technical-sheets.sheet.published') }}:</span>
                            {{ $sheet->publication_date->format('d/m/Y') }}
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between mt-4">
                        <a href="{{ route('technical-sheets.show', $sheet) }}" 
                           class="text-blue-600 hover:text-blue-800 font-medium">
                            {{ __('technical-sheets.sheet.specifications') }} &rarr;
                        </a>
                        
                        <div class="flex items-center space-x-2">
                            @if($sheet->hasModel3D())
                            <a href="{{ route('technical-sheets.view3d', $sheet) }}" 
                               class="text-gray-600 hover:text-gray-800"
                               title="{{ __('technical-sheets.sheet.3d_view.title') }}">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z"/>
                                </svg>
                            </a>
                            @endif
                            
                            <a href="{{ route('technical-sheets.download', $sheet) }}" 
                               class="text-gray-600 hover:text-gray-800"
                               title="{{ __('technical-sheets.sheet.download.pdf') }}">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            {{ $sheets->links() }}
        </div>
    </div>
</div>
@endsection
