<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EventRegistration extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'user_id',
        'registration_number',
        'first_name',
        'last_name',
        'email',
        'phone',
        'company',
        'position',
        'special_requirements',
        'status',
        'payment_status',
        'payment_method',
        'payment_reference',
        'attended',
        'certificate_issued',
        'feedback_rating',
        'feedback_comment'
    ];

    protected $casts = [
        'attended' => 'boolean',
        'certificate_issued' => 'boolean',
        'feedback_rating' => 'integer'
    ];

    public function event(): BelongsTo
    {
        return $this->belongsTo(Event::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function getStatusColorAttribute()
    {
        $colors = [
            'confirmed' => 'green',
            'pending' => 'yellow',
            'cancelled' => 'red',
            'waitlist' => 'blue'
        ];

        return $colors[$this->status] ?? 'gray';
    }

    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopeAttended($query)
    {
        return $query->where('attended', true);
    }
}
