<?php

namespace App\Notifications;

use App\Models\Event;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EventReminder extends Notification implements ShouldQueue
{
    use Queueable;

    protected $event;
    protected $reminderType;

    public function __construct(Event $event, $reminderType = '24h')
    {
        $this->event = $event;
        $this->reminderType = $reminderType;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        $timeMessages = [
            '24h' => 'dans 24 heures',
            '1h' => 'dans 1 heure',
            'now' => 'maintenant',
        ];

        return (new MailMessage)
            ->subject('Rappel: ' . $this->event->title . ' - ' . $timeMessages[$this->reminderType])
            ->greeting('Bonjour ' . $notifiable->name . ',')
            ->line('Nous vous rappelons que l\'événement "' . $this->event->title . '" commence ' . $timeMessages[$this->reminderType] . '.')
            ->line('Date: ' . $this->event->start_date->format('d/m/Y à H:i'))
            ->line('Lieu: ' . ($this->event->location ?: 'En ligne'))
            ->when($this->event->description, function ($mail) {
                return $mail->line('Description: ' . $this->event->description);
            })
            ->action('Voir les détails', route('events.show', $this->event))
            ->line('À bientôt chez ENAP !');
    }

    public function toArray($notifiable)
    {
        return [
            'event_id' => $this->event->id,
            'event_title' => $this->event->title,
            'start_date' => $this->event->start_date,
            'reminder_type' => $this->reminderType,
            'message' => 'Rappel: "' . $this->event->title . '" commence bientôt.',
        ];
    }
}
