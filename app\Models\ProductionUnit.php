<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductionUnit extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'code',
        'description',
        'address',
        'city',
        'wilaya',
        'postal_code',
        'phone',
        'email',
        'latitude',
        'longitude',
        'opening_hours',
        'specialties',
        'distribution_zone',
        'meta_data',
        'is_active'
    ];

    protected $casts = [
        'opening_hours' => 'array',
        'specialties' => 'array',
        'distribution_zone' => 'array',
        'meta_data' => 'array',
        'is_active' => 'boolean',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8'
    ];

    public function getFullAddressAttribute()
    {
        return "{$this->address}, {$this->city}, {$this->wilaya}";
    }

    public function isOpenNow()
    {
        if (!$this->opening_hours) {
            return false;
        }

        $now = now();
        $day = strtolower($now->format('l'));
        $time = $now->format('H:i');

        $hours = $this->opening_hours[$day] ?? null;
        if (!$hours) {
            return false;
        }

        return $time >= $hours['open'] && $time <= $hours['close'];
    }
}
