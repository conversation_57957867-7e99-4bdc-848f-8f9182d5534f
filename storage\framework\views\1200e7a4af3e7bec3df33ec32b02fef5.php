<!-- Slider Moderne ENAP -->
<div id="modern-slider" class="relative -mt-32 h-screen overflow-hidden bg-gray-900" x-data="modernSlider()">
    <!-- Container des slides -->
    <div class="relative w-full h-full">
        <!-- Slides -->
        <template x-for="(slide, index) in slides" :key="slide.id">
            <div class="absolute inset-0 w-full h-full transition-all duration-1000 ease-in-out"
                 :class="{
                     'opacity-100 scale-100': currentSlide === index,
                     'opacity-0 scale-105': currentSlide !== index
                 }"
                 x-show="currentSlide === index"
                 x-transition:enter="transition-all duration-1000 ease-out"
                 x-transition:enter-start="opacity-0 scale-105"
                 x-transition:enter-end="opacity-100 scale-100"
                 x-transition:leave="transition-all duration-1000 ease-in"
                 x-transition:leave-start="opacity-100 scale-100"
                 x-transition:leave-end="opacity-0 scale-95">
                
                <!-- Image de fond -->
                <div class="absolute inset-0">
                    <img :src="slide.image_url" 
                         :alt="slide.image_alt || slide.title"
                         class="w-full h-full object-cover"
                         loading="lazy">
                    
                    <!-- Overlay avec couleur personnalisée -->
                    <div class="absolute inset-0 bg-black bg-opacity-40"
                         :style="slide.background_color ? `background: linear-gradient(135deg, ${slide.background_color}CC, ${slide.background_color}80)` : ''"></div>
                </div>

                <!-- Contenu du slide -->
                <div class="relative z-10 h-full flex items-center">
                    <div class="container mx-auto px-6">
                        <div class="max-w-4xl"
                             :class="{
                                 'text-left': slide.text_position === 'left',
                                 'text-center mx-auto': slide.text_position === 'center',
                                 'text-right ml-auto': slide.text_position === 'right'
                             }">
                            
                            <!-- Titre -->
                            <h1 class="text-5xl lg:text-7xl font-bold mb-6 leading-tight animate-fade-in-up"
                                :style="`color: ${slide.text_color}`"
                                x-text="slide.title"></h1>
                            
                            <!-- Description -->
                            <p class="text-xl lg:text-2xl mb-8 leading-relaxed animate-fade-in-up animation-delay-200"
                               :style="`color: ${slide.text_color}CC`"
                               x-text="slide.description"
                               x-show="slide.description"></p>
                            
                            <!-- Bouton CTA -->
                            <div class="animate-fade-in-up animation-delay-400" x-show="slide.button_text">
                                <a :href="slide.button_url || '#'"
                                   :target="slide.button_target || '_self'"
                                   class="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                    <span x-text="slide.button_text"></span>
                                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <!-- Message de chargement -->
        <div x-show="loading" class="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div class="text-center text-white">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                <p class="text-lg">Chargement des slides...</p>
            </div>
        </div>

        <!-- Message d'erreur -->
        <div x-show="error" class="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div class="text-center text-white">
                <svg class="w-16 h-16 mx-auto mb-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <p class="text-lg">Erreur lors du chargement des slides</p>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div x-show="slides.length > 1" class="absolute bottom-8 left-0 right-0 z-20">
        <div class="container mx-auto px-6">
            <div class="flex justify-center items-center space-x-4">
                <!-- Bouton précédent -->
                <button @click="previousSlide()" 
                        class="p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-300 transform hover:scale-110">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>

                <!-- Indicateurs -->
                <div class="flex space-x-2">
                    <template x-for="(slide, index) in slides" :key="index">
                        <button @click="goToSlide(index)"
                                class="w-3 h-3 rounded-full transition-all duration-300"
                                :class="{
                                    'bg-white scale-125': currentSlide === index,
                                    'bg-white/50 hover:bg-white/75': currentSlide !== index
                                }"></button>
                    </template>
                </div>

                <!-- Bouton suivant -->
                <button @click="nextSlide()" 
                        class="p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-300 transform hover:scale-110">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Bouton play/pause -->
                <button @click="toggleAutoplay()" 
                        class="p-3 rounded-full bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 transition-all duration-300 transform hover:scale-110 ml-4">
                    <svg x-show="isPlaying" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6"></path>
                    </svg>
                    <svg x-show="!isPlaying" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Barre de progression -->
    <div x-show="slides.length > 1 && isPlaying" class="absolute bottom-0 left-0 right-0 z-20">
        <div class="h-1 bg-white/20">
            <div class="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-100 ease-linear"
                 :style="`width: ${progress}%`"></div>
        </div>
    </div>
</div>

<!-- Styles CSS personnalisés -->
<style>
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animation-delay-200 {
    animation-delay: 0.2s;
    opacity: 0;
}

.animation-delay-400 {
    animation-delay: 0.4s;
    opacity: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .animate-fade-in-up {
        animation-duration: 0.6s;
    }
}
</style>

<!-- JavaScript Alpine.js Component -->
<script>
function modernSlider() {
    return {
        slides: [],
        currentSlide: 0,
        isPlaying: true,
        loading: true,
        error: false,
        progress: 0,
        autoplayInterval: null,
        progressInterval: null,
        
        async init() {
            await this.loadSlides();
            if (this.slides.length > 0) {
                this.startAutoplay();
            }
        },

        async loadSlides() {
            try {
                const response = await fetch('/api/slides');
                if (!response.ok) throw new Error('Erreur de chargement');
                
                this.slides = await response.json();
                this.loading = false;
                
                if (this.slides.length === 0) {
                    this.error = true;
                }
            } catch (error) {
                console.error('Erreur lors du chargement des slides:', error);
                this.loading = false;
                this.error = true;
            }
        },

        nextSlide() {
            if (this.slides.length === 0) return;
            this.currentSlide = (this.currentSlide + 1) % this.slides.length;
            this.resetProgress();
        },

        previousSlide() {
            if (this.slides.length === 0) return;
            this.currentSlide = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1;
            this.resetProgress();
        },

        goToSlide(index) {
            this.currentSlide = index;
            this.resetProgress();
        },

        startAutoplay() {
            if (this.slides.length <= 1) return;
            
            this.isPlaying = true;
            const duration = this.slides[this.currentSlide]?.duration || 5000;
            
            // Démarrer la barre de progression
            this.startProgress(duration);
            
            // Démarrer l'autoplay
            this.autoplayInterval = setTimeout(() => {
                this.nextSlide();
                this.startAutoplay();
            }, duration);
        },

        stopAutoplay() {
            this.isPlaying = false;
            if (this.autoplayInterval) {
                clearTimeout(this.autoplayInterval);
                this.autoplayInterval = null;
            }
            this.stopProgress();
        },

        toggleAutoplay() {
            if (this.isPlaying) {
                this.stopAutoplay();
            } else {
                this.startAutoplay();
            }
        },

        startProgress(duration) {
            this.progress = 0;
            this.progressInterval = setInterval(() => {
                this.progress += (100 / duration) * 100; // Update every 100ms
                if (this.progress >= 100) {
                    this.progress = 100;
                    this.stopProgress();
                }
            }, 100);
        },

        stopProgress() {
            if (this.progressInterval) {
                clearInterval(this.progressInterval);
                this.progressInterval = null;
            }
        },

        resetProgress() {
            this.stopProgress();
            if (this.isPlaying) {
                const duration = this.slides[this.currentSlide]?.duration || 5000;
                this.startProgress(duration);
            }
        },

        // Pause autoplay au survol
        pauseOnHover() {
            if (this.isPlaying) {
                this.stopAutoplay();
            }
        },

        // Reprendre autoplay après survol
        resumeAfterHover() {
            if (!this.isPlaying) {
                this.startAutoplay();
            }
        }
    }
}
</script>
<?php /**PATH C:\xampp\htdocs\wsite-enap\resources\views/components/modern-slider.blade.php ENDPATH**/ ?>