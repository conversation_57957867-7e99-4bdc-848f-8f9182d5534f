<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('quotes.request_quote') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <form action="{{ route('quotes.store') }}" method="POST" id="quoteForm" class="space-y-6">
                        @csrf

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Client Information -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900">{{ __('quotes.client_information') }}</h3>
                                
                                <div>
                                    <x-input-label for="client_name" :value="__('quotes.client_name')" />
                                    <x-text-input id="client_name" name="client_name" type="text" class="mt-1 block w-full" :value="old('client_name')" required autofocus />
                                    <x-input-error :messages="$errors->get('client_name')" class="mt-2" />
                                </div>

                                <div>
                                    <x-input-label for="client_email" :value="__('quotes.client_email')" />
                                    <x-text-input id="client_email" name="client_email" type="email" class="mt-1 block w-full" :value="old('client_email')" required />
                                    <x-input-error :messages="$errors->get('client_email')" class="mt-2" />
                                </div>

                                <div>
                                    <x-input-label for="client_phone" :value="__('quotes.client_phone')" />
                                    <x-text-input id="client_phone" name="client_phone" type="tel" class="mt-1 block w-full" :value="old('client_phone')" />
                                    <x-input-error :messages="$errors->get('client_phone')" class="mt-2" />
                                </div>

                                <div>
                                    <x-input-label for="company_name" :value="__('quotes.company_name')" />
                                    <x-text-input id="company_name" name="company_name" type="text" class="mt-1 block w-full" :value="old('company_name')" />
                                    <x-input-error :messages="$errors->get('company_name')" class="mt-2" />
                                </div>
                            </div>

                            <!-- Project Information -->
                            <div class="space-y-4">
                                <h3 class="text-lg font-medium text-gray-900">{{ __('quotes.project_information') }}</h3>

                                <div>
                                    <x-input-label for="project_description" :value="__('quotes.project_description')" />
                                    <textarea id="project_description" name="project_description" rows="4" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500" required>{{ old('project_description') }}</textarea>
                                    <x-input-error :messages="$errors->get('project_description')" class="mt-2" />
                                </div>

                                <div>
                                    <x-input-label for="surface_type" :value="__('quotes.surface_type')" />
                                    <select id="surface_type" name="surface_type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                        <option value="">{{ __('quotes.select_surface_type') }}</option>
                                        <option value="interior">{{ __('quotes.interior') }}</option>
                                        <option value="exterior">{{ __('quotes.exterior') }}</option>
                                        <option value="metal">{{ __('quotes.metal') }}</option>
                                        <option value="wood">{{ __('quotes.wood') }}</option>
                                    </select>
                                    <x-input-error :messages="$errors->get('surface_type')" class="mt-2" />
                                </div>

                                <div>
                                    <x-input-label for="surface_area" :value="__('quotes.surface_area')" />
                                    <div class="mt-1 relative rounded-md shadow-sm">
                                        <x-text-input id="surface_area" name="surface_area" type="number" step="0.01" min="0" class="block w-full pr-12" :value="old('surface_area')" required />
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500 sm:text-sm">m²</span>
                                        </div>
                                    </div>
                                    <x-input-error :messages="$errors->get('surface_area')" class="mt-2" />
                                </div>

                                <div>
                                    <x-input-label for="coats_number" :value="__('quotes.coats_number')" />
                                    <x-text-input id="coats_number" name="coats_number" type="number" min="1" class="mt-1 block w-full" :value="old('coats_number', 1)" required />
                                    <x-input-error :messages="$errors->get('coats_number')" class="mt-2" />
                                </div>
                            </div>
                        </div>

                        <!-- Products Selection -->
                        <div class="mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('quotes.select_products') }}</h3>
                            
                            <div id="products-container">
                                @foreach($products as $product)
                                    <div class="flex items-center space-x-4 py-4 border-t border-gray-200">                            <div class="flex-1">
                                            <label class="flex items-start">
                                                <input type="checkbox" name="selected_products[]" value="{{ $product->id }}" 
                                                    class="mt-1 rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-500 focus:ring-primary-500 product-checkbox"
                                                    @if($selectedProduct && $selectedProduct->id === $product->id) checked @endif>
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium text-gray-900">{{ $product->name }}</div>
                                                    <div class="text-sm text-gray-500">{{ $product->description }}</div>
                                                    <div class="text-sm font-medium text-primary-600">{{ number_format($product->price, 2) }} DA</div>
                                                </div>
                                            </label>
                                        </div>
                                        <div class="w-32">
                                            <x-text-input type="number" name="products[{{ $product->id }}][quantity]" class="product-quantity" min="0" step="1" value="0" disabled />
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- Total Estimation -->
                        <div class="mt-8 px-6 py-4 bg-gray-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-medium text-gray-900">{{ __('quotes.estimated_total') }}:</span>
                                <span class="text-2xl font-bold text-primary-600" id="estimated-total">0.00 DA</span>
                            </div>
                        </div>

                        <div class="flex justify-end mt-6">
                            <x-secondary-button type="button" onclick="window.history.back()" class="mr-3">
                                {{ __('quotes.cancel') }}
                            </x-secondary-button>
                            <x-primary-button>
                                {{ __('quotes.submit_request') }}
                            </x-primary-button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('quoteForm');
            const checkboxes = document.querySelectorAll('.product-checkbox');
            const quantityInputs = document.querySelectorAll('.product-quantity');
            
            // Enable quantity inputs for pre-selected products
            checkboxes.forEach((checkbox, index) => {
                if (checkbox.checked) {
                    const quantityInput = quantityInputs[index];
                    quantityInput.disabled = false;
                    quantityInput.value = 1;
                }
            });
            
            checkboxes.forEach((checkbox, index) => {
                checkbox.addEventListener('change', function() {
                    const quantityInput = quantityInputs[index];
                    quantityInput.disabled = !this.checked;
                    if (!this.checked) {
                        quantityInput.value = 0;
                    } else {
                        quantityInput.value = 1;
                    }
                    updateEstimatedTotal();
                });
            });

            quantityInputs.forEach(input => {
                input.addEventListener('change', updateEstimatedTotal);
            });

            function updateEstimatedTotal() {
                const formData = new FormData(form);
                
                fetch("{{ route('quotes.calculate-estimate') }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify(Object.fromEntries(formData))
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('estimated-total').textContent = 
                        new Intl.NumberFormat('fr-FR', { 
                            style: 'currency', 
                            currency: 'DZD',
                            minimumFractionDigits: 2
                        }).format(data.total);
                });
            }
        });
    </script>
    @endpush
</x-app-layout>
