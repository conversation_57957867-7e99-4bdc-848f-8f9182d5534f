@extends('layouts.app')

@section('title', 'Localisateur d\'unités ENAP')

@section('styles')
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
@endsection

@section('content')
<div class="bg-white py-8">
    <div class="container mx-auto px-4">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Trouvez l'unité ENAP la plus proche</h1>
            <p class="text-lg text-gray-600">Découvrez nos unités de production et de distribution à travers l'Algérie</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left sidebar with unit list -->
            <div class="lg:col-span-1 bg-white rounded-lg shadow-lg p-6">
                <div class="mb-4">
                    <label for="search" class="block text-sm font-medium text-gray-700">Rechercher par wilaya</label>
                    <input type="text" id="search" name="search" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>

                <div class="space-y-4 mt-6 max-h-[600px] overflow-y-auto" id="units-list">
                    @foreach($units as $unit)
                    <div class="unit-card p-4 border rounded-lg hover:bg-gray-50 cursor-pointer transition" 
                         data-lat="{{ $unit->latitude }}" 
                         data-lng="{{ $unit->longitude }}">
                        <h3 class="font-semibold text-gray-900">{{ $unit->name }}</h3>
                        <p class="text-sm text-gray-600">{{ $unit->full_address }}</p>
                        <div class="mt-2 flex items-center text-sm text-gray-500">
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span id="status-{{ $unit->id }}">Chargement...</span>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Right side map -->
            <div class="lg:col-span-2">
                <div id="map" class="h-[600px] rounded-lg shadow-lg"></div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize map
    const map = L.map('map').setView([36.7538, 3.0588], 6);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Add markers for each unit
    const markers = {};
    const units = document.querySelectorAll('.unit-card');
    units.forEach(unit => {
        const lat = parseFloat(unit.dataset.lat);
        const lng = parseFloat(unit.dataset.lng);
        const marker = L.marker([lat, lng]).addTo(map);
        markers[unit.dataset.id] = marker;

        // Click on unit card to focus map
        unit.addEventListener('click', () => {
            map.setView([lat, lng], 12);
        });

        // Check opening hours
        fetch(`/units/${unit.dataset.id}/opening-hours`)
            .then(response => response.json())
            .then(data => {
                const statusEl = document.getElementById(`status-${unit.dataset.id}`);
                statusEl.textContent = data.is_open ? 'Ouvert' : 'Fermé';
                statusEl.classList.add(data.is_open ? 'text-green-600' : 'text-red-600');
            });
    });

    // Search functionality
    const searchInput = document.getElementById('search');
    searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        units.forEach(unit => {
            const text = unit.textContent.toLowerCase();
            unit.style.display = text.includes(searchTerm) ? 'block' : 'none';
        });
    });

    // Geolocation
    if ("geolocation" in navigator) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const userLat = position.coords.latitude;
            const userLng = position.coords.longitude;

            // Find nearest units
            fetch(`/units/nearest?latitude=${userLat}&longitude=${userLng}`)
                .then(response => response.json())
                .then(data => {
                    // Update UI to highlight nearest units
                    data.units.forEach(unit => {
                        const unitEl = document.querySelector(`[data-id="${unit.id}"]`);
                        if (unitEl) {
                            unitEl.classList.add('border-blue-500');
                            // Add distance info
                            const distanceEl = document.createElement('p');
                            distanceEl.textContent = `${Math.round(unit.distance)}km de votre position`;
                            distanceEl.classList.add('text-sm', 'text-blue-600', 'mt-1');
                            unitEl.appendChild(distanceEl);
                        }
                    });
                });
        });
    }
});
</script>
@endsection
