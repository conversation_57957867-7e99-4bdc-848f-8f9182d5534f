<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Slide;
use App\Services\ImageOptimizationService;
use Illuminate\Support\Facades\Storage;

class OptimizeExistingSlideImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'slides:optimize-existing {--force : Force re-optimization of already optimized images}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Optimise les images existantes des slides pour améliorer la qualité et les performances';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🎨 Optimisation des images de slides existantes...');

        $force = $this->option('force');
        $imageOptimizer = new ImageOptimizationService();

        // Récupérer tous les slides avec des images
        $slides = Slide::whereNotNull('image_path')
            ->where('image_path', '!=', '')
            ->get();

        if ($slides->isEmpty()) {
            $this->warn('Aucun slide avec image trouvé.');
            return;
        }

        $this->info("📸 {$slides->count()} slides trouvés avec des images.");

        $progressBar = $this->output->createProgressBar($slides->count());
        $progressBar->start();

        $optimized = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($slides as $slide) {
            try {
                // Vérifier si l'image est déjà optimisée (sauf si --force)
                if (!$force && $slide->optimized_images && !empty($slide->optimized_images)) {
                    $skipped++;
                    $progressBar->advance();
                    continue;
                }

                // Vérifier si le fichier image existe
                if (!Storage::exists($slide->image_path)) {
                    $this->newLine();
                    $this->warn("⚠️  Image non trouvée: {$slide->image_path} (Slide: {$slide->title})");
                    $errors++;
                    $progressBar->advance();
                    continue;
                }

                // Créer un fichier temporaire pour l'optimisation
                $tempPath = $this->createTempFileFromStorage($slide->image_path);

                if (!$tempPath) {
                    $this->newLine();
                    $this->error("❌ Impossible de créer le fichier temporaire pour: {$slide->image_path}");
                    $errors++;
                    $progressBar->advance();
                    continue;
                }

                // Optimiser l'image
                $optimizedImages = $imageOptimizer->optimizeSliderImage($tempPath, 'slides');

                // Mettre à jour le slide
                $slide->update([
                    'image_path' => $optimizedImages['original']['path'],
                    'optimized_images' => $optimizedImages,
                    'image_srcset' => $imageOptimizer->generateSrcSet($optimizedImages)
                ]);

                // Nettoyer le fichier temporaire
                unlink($tempPath);

                $optimized++;

            } catch (\Exception $e) {
                $this->newLine();
                $this->error("❌ Erreur lors de l'optimisation du slide '{$slide->title}': " . $e->getMessage());
                $errors++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        // Résumé
        $this->info('✅ Optimisation terminée !');
        $this->table(
            ['Statut', 'Nombre'],
            [
                ['Optimisés', $optimized],
                ['Ignorés (déjà optimisés)', $skipped],
                ['Erreurs', $errors],
                ['Total', $slides->count()]
            ]
        );

        if ($optimized > 0) {
            $this->info("🚀 {$optimized} images ont été optimisées avec succès !");
        }

        if ($errors > 0) {
            $this->warn("⚠️  {$errors} erreurs rencontrées. Vérifiez les logs ci-dessus.");
        }
    }

    /**
     * Créer un fichier temporaire à partir d'un fichier de storage
     */
    private function createTempFileFromStorage(string $storagePath): ?string
    {
        try {
            $content = Storage::get($storagePath);
            $tempPath = tempnam(sys_get_temp_dir(), 'slide_optimize_');

            if (file_put_contents($tempPath, $content) === false) {
                return null;
            }

            return $tempPath;

        } catch (\Exception $e) {
            return null;
        }
    }
}
