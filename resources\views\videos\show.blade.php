@extends('layouts.app')

@section('content')
<div class="py-12 {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <div class="container mx-auto px-6">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <!-- Video Player -->
            <div class="aspect-video">
                <iframe 
                    class="w-full h-full"
                    src="{{ $video->video_url }}"
                    frameborder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen>
                </iframe>
            </div>

            <!-- Video Info -->
            <div class="p-6">
                <h1 class="text-3xl font-bold mb-4">{{ $video->getTranslation('title', app()->getLocale()) }}</h1>
                <div class="flex items-center text-sm text-gray-600 mb-6">
                    <span class="mr-4">{{ number_format($video->views) }} views</span>
                    <span class="mr-4">{{ $video->created_at->diffForHumans() }}</span>
                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        {{ __('videos.gallery.difficulty_levels.' . $video->difficulty_level) }}
                    </span>
                </div>
                
                <div class="prose max-w-none mb-8">
                    <p>{{ $video->getTranslation('description', app()->getLocale()) }}</p>
                </div>

                <!-- Products Used -->
                @if($products->isNotEmpty())
                <div class="mb-8">
                    <h2 class="text-xl font-semibold mb-4">{{ __('videos.video.products_used') }}</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($products as $product)
                        <a href="{{ route('products.show', $product) }}" 
                           class="flex items-center p-4 border rounded-lg hover:bg-gray-50">
                            <img src="{{ $product->thumbnail_url }}" 
                                 alt="{{ $product->getTranslation('name', app()->getLocale()) }}"
                                 class="w-16 h-16 object-cover rounded">
                            <div class="ms-4">
                                <h3 class="font-semibold">{{ $product->getTranslation('name', app()->getLocale()) }}</h3>
                                <p class="text-sm text-gray-600">{{ $product->category }}</p>
                            </div>
                        </a>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Application Steps -->
                @if($video->application_steps)
                <div class="mb-8">
                    <h2 class="text-xl font-semibold mb-4">{{ __('videos.video.steps') }}</h2>
                    <div class="space-y-4">
                        @foreach($video->getTranslation('application_steps', app()->getLocale()) as $index => $step)
                        <div class="flex">
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center">
                                {{ $index + 1 }}
                            </div>
                            <div class="ms-4">
                                <p>{{ $step }}</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Tools Needed -->
                @if($video->tools_needed)
                <div class="mb-8">
                    <h2 class="text-xl font-semibold mb-4">{{ __('videos.video.tools_needed') }}</h2>
                    <ul class="list-disc list-inside space-y-2">
                        @foreach($video->getTranslation('tools_needed', app()->getLocale()) as $tool)
                        <li>{{ $tool }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif

                <!-- Safety Notes -->
                @if($video->safety_notes)
                <div class="mb-8 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h2 class="text-xl font-semibold mb-4 text-yellow-800">{{ __('videos.video.safety') }}</h2>
                    <ul class="list-disc list-inside space-y-2 text-yellow-800">
                        @foreach($video->getTranslation('safety_notes', app()->getLocale()) as $note)
                        <li>{{ $note }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif
            </div>
        </div>

        <!-- Related Videos -->
        @if($relatedVideos->isNotEmpty())
        <div class="mt-8">
            <h2 class="text-2xl font-bold mb-6">{{ __('videos.video.related') }}</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($relatedVideos as $relatedVideo)
                <div class="bg-white rounded-lg shadow-lg overflow-hidden group">
                    <div class="relative">
                        <img src="{{ $relatedVideo->thumbnail_url }}" 
                             alt="{{ $relatedVideo->getTranslation('title', app()->getLocale()) }}"
                             class="w-full aspect-video object-cover">
                        <div class="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                            {{ gmdate("i:s", $relatedVideo->duration) }}
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold line-clamp-2">
                            {{ $relatedVideo->getTranslation('title', app()->getLocale()) }}
                        </h3>
                        <div class="text-sm text-gray-600 mt-2">
                            {{ number_format($relatedVideo->views) }} views
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
