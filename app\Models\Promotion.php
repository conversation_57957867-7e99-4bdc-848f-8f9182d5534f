<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Promotion extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'type',
        'discount_percentage',
        'discount_amount',
        'start_date',
        'end_date',
        'is_active',
        'image_url',
        'terms_conditions',
        'minimum_quantity',
        'maximum_discount',
        'code'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'maximum_discount' => 'decimal:2'
    ];

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'promotion_products');
    }

    public function isValid(): bool
    {
        return $this->is_active 
            && $this->start_date <= now() 
            && $this->end_date >= now();
    }

    public function isExpiringSoon(): bool
    {
        return $this->end_date <= now()->addDays(7);
    }

    public function getDaysRemainingAttribute(): int
    {
        return max(0, now()->diffInDays($this->end_date, false));
    }

    public function getDiscountTextAttribute(): string
    {
        if ($this->discount_percentage) {
            return "-{$this->discount_percentage}%";
        }
        
        if ($this->discount_amount) {
            return "-{$this->discount_amount} DA";
        }
        
        return '';
    }

    public function calculateDiscount(float $originalPrice, int $quantity = 1): array
    {
        $totalPrice = $originalPrice * $quantity;
        
        if ($this->minimum_quantity && $quantity < $this->minimum_quantity) {
            return [
                'eligible' => false,
                'reason' => 'minimum_quantity_not_met'
            ];
        }

        $discountAmount = 0;
        
        if ($this->discount_percentage) {
            $discountAmount = $totalPrice * ($this->discount_percentage / 100);
        } elseif ($this->discount_amount) {
            $discountAmount = $this->discount_amount * $quantity;
        }

        // Apply maximum discount limit if set
        if ($this->maximum_discount && $discountAmount > $this->maximum_discount) {
            $discountAmount = $this->maximum_discount;
        }

        return [
            'eligible' => true,
            'original_price' => $totalPrice,
            'discount_amount' => $discountAmount,
            'final_price' => $totalPrice - $discountAmount,
            'savings_percentage' => $totalPrice > 0 ? ($discountAmount / $totalPrice) * 100 : 0
        ];
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where('start_date', '<=', now())
                    ->where('end_date', '>=', now());
    }

    public function scopeExpiringSoon($query, int $days = 7)
    {
        return $query->where('end_date', '<=', now()->addDays($days))
                    ->where('end_date', '>=', now());
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }
}
