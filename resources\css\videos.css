/* Styles spécifiques pour la vidéothèque */

/* Grille des vidéos */
.video-grid {
    display: grid;
    gap: 1.5rem;
}

@media (min-width: 640px) {
    .video-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .video-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1280px) {
    .video-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Carte de vidéo */
.video-card {
    transition: transform 0.2s;
}

.video-card:hover {
    transform: translateY(-4px);
}

/* Overlay du lecteur */
.video-overlay {
    transition: opacity 0.2s;
}

/* Adaptation RTL */
[dir="rtl"] .video-play-icon {
    transform: scaleX(-1);
}

/* Durée de la vidéo */
.video-duration {
    bottom: 0.5rem;
    right: 0.5rem;
    background-color: rgba(0, 0, 0, 0.7);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

[dir="rtl"] .video-duration {
    right: auto;
    left: 0.5rem;
}

/* Étapes d'application */
.step-number {
    width: 2rem;
    height: 2rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #2563eb;
    color: white;
    font-weight: 600;
}

[dir="rtl"] .step-content {
    margin-right: 1rem;
    margin-left: 0;
}

/* Notes de sécurité */
.safety-note {
    background-color: #fef3c7;
    border: 1px solid #fcd34d;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

[dir="rtl"] .safety-icon {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* Produits utilisés */
.product-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    transition: background-color 0.2s;
}

.product-card:hover {
    background-color: #f9fafb;
}

[dir="rtl"] .product-image {
    margin-left: 1rem;
    margin-right: 0;
}

/* Contrôles de filtre */
.filter-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

@media (max-width: 640px) {
    .filter-controls {
        flex-direction: column;
    }
}

/* Placeholder pour les images en cours de chargement */
.thumbnail-placeholder {
    background: linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 50%, #f3f4f6 100%);
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}
