@extends('layouts.app')

@section('title', $unit->name . ' - ENAP')

@section('content')
<div class="bg-white py-8">
    <div class="container mx-auto px-4">
        <nav class="mb-8">
            <ol class="flex items-center space-x-2 text-gray-600">
                <li><a href="{{ route('home') }}" class="hover:text-blue-600">Accueil</a></li>
                <li><span class="mx-2">/</span></li>
                <li><a href="{{ route('units.index') }}" class="hover:text-blue-600">Unités de Production</a></li>
                <li><span class="mx-2">/</span></li>
                <li class="text-gray-900">{{ $unit->name }}</li>
            </ol>
        </nav>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Left Column - Unit Information -->
            <div>
                <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $unit->name }}</h1>
                <div class="prose max-w-none">
                    <p class="text-lg text-gray-600 mb-6">{{ $unit->description }}</p>

                    <!-- Contact Information -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Coordonnées</h2>
                        <div class="space-y-3">
                            <p class="flex items-center">
                                <svg class="h-5 w-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                {{ $unit->full_address }}
                            </p>
                            <p class="flex items-center">
                                <svg class="h-5 w-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                </svg>
                                <a href="tel:{{ $unit->phone }}" class="hover:text-blue-600">{{ $unit->phone }}</a>
                            </p>
                            <p class="flex items-center">
                                <svg class="h-5 w-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                </svg>
                                <a href="mailto:{{ $unit->email }}" class="hover:text-blue-600">{{ $unit->email }}</a>
                            </p>
                        </div>
                    </div>

                    <!-- Opening Hours -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Horaires d'ouverture</h2>
                        <div class="grid grid-cols-2 gap-4">
                            @foreach($unit->opening_hours as $day => $hours)
                            <div class="flex justify-between">
                                <span class="font-medium">{{ ucfirst($day) }}</span>
                                <span>{{ $hours }}</span>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- Specialties -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Spécialités</h2>
                        <ul class="list-disc list-inside space-y-2">
                            @foreach($unit->specialties as $specialty)
                            <li>{{ $specialty }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Right Column - Map and Zone -->
            <div>
                <!-- Map -->
                <div id="map" class="h-[400px] rounded-lg shadow-lg mb-6"></div>

                <!-- Distribution Zone -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Zone de Distribution</h2>
                    <ul class="grid grid-cols-2 gap-4">
                        @foreach($unit->distribution_zone as $wilaya)
                        <li class="flex items-center">
                            <svg class="h-5 w-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            {{ $wilaya }}
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const map = L.map('map').setView([{{ $unit->latitude }}, {{ $unit->longitude }}], 13);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    L.marker([{{ $unit->latitude }}, {{ $unit->longitude }}])
        .addTo(map)
        .bindPopup("{{ $unit->name }}")
        .openPopup();
});
</script>
@endsection

@section('styles')
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
@endsection
