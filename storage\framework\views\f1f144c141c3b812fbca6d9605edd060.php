

<?php $__env->startSection('content'); ?>
<div class="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">
    <!-- Hero Section avec Slider -->
    <div x-data="{ currentSlide: 0 }" class="relative">
        <div class="relative h-[600px] overflow-hidden">
            <!-- Slides -->
            <div class="absolute inset-0 transition-transform duration-500"
                 :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
                <!-- Slide 1 -->
                <div class="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-900 to-blue-600">
                    <div class="container mx-auto px-6 h-full flex items-center">
                        <div class="max-w-2xl text-white">
                            <h1 class="text-5xl font-bold mb-6"><?php echo e(__('home.hero.title')); ?></h1>
                            <p class="text-xl mb-8"><?php echo e(__('home.hero.subtitle')); ?></p>
                            <div class="flex flex-wrap gap-4">
                                <a href="<?php echo e(route('products.index')); ?>"
                                   class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                    <?php echo e(__('home.hero.cta.products')); ?>

                                </a>
                                <a href="<?php echo e(route('color-simulator')); ?>"
                                   class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                                    <?php echo e(__('home.hero.cta.simulator')); ?>

                                </a>
                                <a href="<?php echo e(route('promotions.index')); ?>"
                                   class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors">
                                    <?php echo e(__('home.hero.cta.promotions')); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Slide 2 -->
                <div class="absolute inset-0 w-full h-full bg-gradient-to-r from-green-900 to-green-600">
                    <div class="container mx-auto px-6 h-full flex items-center">
                        <div class="max-w-2xl text-white">
                            <h2 class="text-4xl font-bold mb-6"><?php echo e(__('home.solutions.title')); ?></h2>
                            <p class="text-xl mb-8"><?php echo e(__('home.solutions.subtitle')); ?></p>
                            <a href="<?php echo e(route('quotes.create')); ?>"
                               class="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                <?php echo e(__('home.solutions.cta')); ?>

                            </a>
                        </div>
                    </div>
                </div>
                <!-- Slide 3 - Galerie Produits ENAP -->
                <?php if(isset($products) && count($products) > 0): ?>
                <div class="absolute inset-0 w-full h-full bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
                    <div class="container mx-auto px-6 h-full flex items-center">
                        <div class="w-full">
                            <div class="text-center mb-8">
                                <h2 class="text-4xl font-bold text-white mb-4"><?php echo e(__('home.products_gallery.title')); ?></h2>
                                <p class="text-xl text-white/90"><?php echo e(__('home.products_gallery.subtitle')); ?></p>
                            </div>

                            <!-- Galerie de produits dans le slider -->
                            <div id="heroProductsGrid" class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 max-w-5xl mx-auto">
                                <?php $__currentLoopData = array_slice($products, 0, 12); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="group relative bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden hover:bg-white/20 transition-all duration-300 cursor-pointer transform hover:scale-105"
                                     data-hero-product-index="<?php echo e($index); ?>"
                                     onclick="openHomeProductModal('<?php echo e($product['name']); ?>', '<?php echo e($product['image']); ?>', '<?php echo e($product['description']); ?>')">
                                    <div class="aspect-square relative">
                                        <?php if($product['available']): ?>
                                            <img src="<?php echo e(asset('images/' . $product['image'])); ?>"
                                                 alt="<?php echo e($product['name']); ?>"
                                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                                 loading="lazy">
                                        <?php else: ?>
                                            <div class="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
                                                <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        <?php endif; ?>

                                        <!-- Overlay avec nom -->
                                        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                            <div class="absolute bottom-0 left-0 right-0 p-2">
                                                <h4 class="text-white font-bold text-xs text-center"><?php echo e($product['name']); ?></h4>
                                            </div>
                                        </div>

                                        <!-- Badge de disponibilité -->
                                        <?php if($product['available']): ?>
                                            <div class="absolute top-1 right-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                                                ✓
                                            </div>
                                        <?php else: ?>
                                            <div class="absolute top-1 right-1 bg-orange-500 text-white text-xs px-1 py-0.5 rounded">
                                                ⏳
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>

                            <!-- Bouton voir tous les produits -->
                            <div class="text-center mt-8">
                                <a href="<?php echo e(route('products.index')); ?>"
                                   class="inline-flex items-center bg-white text-purple-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg">
                                    <?php echo e(__('home.products_gallery.view_all')); ?>

                                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Contrôles du slider -->
            <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                <button @click="currentSlide = 0"
                        :class="{ 'bg-white': currentSlide === 0, 'bg-white/50': currentSlide !== 0 }"
                        class="w-3 h-3 rounded-full transition-colors"></button>
                <button @click="currentSlide = 1"
                        :class="{ 'bg-white': currentSlide === 1, 'bg-white/50': currentSlide !== 1 }"
                        class="w-3 h-3 rounded-full transition-colors"></button>
                <?php if(isset($products) && count($products) > 0): ?>
                <button @click="currentSlide = 2"
                        :class="{ 'bg-white': currentSlide === 2, 'bg-white/50': currentSlide !== 2 }"
                        class="w-3 h-3 rounded-full transition-colors"></button>
                <?php endif; ?>
            </div>

            <!-- Navigation par flèches -->
            <button @click="currentSlide = currentSlide === 0 ? <?php echo e(isset($products) && count($products) > 0 ? '2' : '1'); ?> : currentSlide - 1"
                    class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button @click="currentSlide = currentSlide === <?php echo e(isset($products) && count($products) > 0 ? '2' : '1'); ?> ? 0 : currentSlide + 1"
                    class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Section Chiffres Clés -->
    <div class="py-16 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">6</div>
                    <div class="text-gray-600"><?php echo e(__('home.key_figures.production_units')); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">40+</div>
                    <div class="text-gray-600"><?php echo e(__('home.key_figures.experience')); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">1000+</div>
                    <div class="text-gray-600"><?php echo e(__('home.key_figures.products')); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">5000+</div>
                    <div class="text-gray-600"><?php echo e(__('home.key_figures.clients')); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Gammes de Produits -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-4"><?php echo e(__('home.product_ranges.title')); ?></h2>
            <p class="text-gray-600 text-center mb-12"><?php echo e(__('home.product_ranges.subtitle')); ?></p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
                    <div class="relative">
                        <img src="<?php echo e($category->image_url); ?>" alt="<?php echo e($category->getTranslation('name', app()->getLocale())); ?>" 
                             class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                        <h3 class="absolute bottom-4 left-4 text-white text-xl font-semibold">
                            <?php echo e($category->getTranslation('name', app()->getLocale())); ?>

                        </h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4"><?php echo e($category->getTranslation('description', app()->getLocale())); ?></p>
                        <a href="<?php echo e(route('products.category', $category->id)); ?>" 
                           class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-800">
                            <?php echo e(__('home.product_ranges.cta')); ?>

                            <svg class="w-4 h-4 <?php echo e(app()->getLocale() === 'ar' ? 'mr-2 rotate-180' : 'ml-2'); ?>" 
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Section Services -->
    <div class="py-16 bg-gradient-to-br from-blue-900 to-blue-700 text-white">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-12"><?php echo e(__('home.services.title')); ?></h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center p-6 bg-white/10 rounded-lg backdrop-blur-sm">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2"><?php echo e(__('home.services.color_simulator.title')); ?></h3>
                    <p class="text-white/80"><?php echo e(__('home.services.color_simulator.description')); ?></p>
                </div>

                <div class="text-center p-6 bg-white/10 rounded-lg backdrop-blur-sm">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                    </div>
                    <h3 class="text-xl font-semibold mb-2"><?php echo e(__('home.services.quantity_calculator.title')); ?></h3>
                    <p class="text-white/80"><?php echo e(__('home.services.quantity_calculator.description')); ?></p>
                </div>

                <div class="text-center p-6 bg-white/10 rounded-lg backdrop-blur-sm">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2"><?php echo e(__('home.services.technical_support.title')); ?></h3>
                    <p class="text-white/80"><?php echo e(__('home.services.technical_support.description')); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Actualités -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <div class="flex justify-between items-center mb-12">
                <div>
                    <h2 class="text-3xl font-bold mb-4"><?php echo e(__('home.news.title')); ?></h2>
                    <p class="text-gray-600"><?php echo e(__('home.news.subtitle')); ?></p>
                </div>
                <a href="<?php echo e(route('blog.index')); ?>" 
                   class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-800">
                    <?php echo e(__('home.news.see_all')); ?>

                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                </a>
            </div>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <?php $__empty_1 = true; $__currentLoopData = $latestPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <article class="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
                    <a href="<?php echo e(route('blog.show', $post->slug)); ?>" class="block">
                        <div class="relative h-48">
                            <?php if($post->featured_image): ?>
                            <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" 
                                 alt="<?php echo e($post->title); ?>" 
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            <?php else: ?>
                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            <?php endif; ?>
                            <?php if($post->category): ?>
                            <span class="absolute top-4 left-4 bg-blue-600 text-white text-sm px-3 py-1 rounded-full">
                                <?php echo e($post->category->name); ?>

                            </span>
                            <?php endif; ?>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600">
                                <?php echo e($post->title); ?>

                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-3"><?php echo e($post->excerpt); ?></p>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-500"><?php echo e($post->formatted_date); ?></span>
                                <span class="text-blue-600 font-medium"><?php echo e(__('home.news.read_more')); ?></span>
                            </div>
                        </div>
                    </a>
                </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-3 text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
                    </svg>
                    <p class="text-gray-600 mb-4"><?php echo e(__('home.news.no_articles')); ?></p>
                    <p class="text-gray-500"><?php echo e(__('home.news.come_back_soon')); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>



    <!-- Section Localisateur d'Unités -->
    <div class="relative py-16 overflow-hidden">
        <div class="absolute inset-0">
            <img src="/images/map-bg.jpg" alt="Carte de l'Algérie" class="w-full h-full object-cover opacity-20">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-900/90 to-blue-600/90"></div>
        </div>
        
        <div class="relative container mx-auto px-6 text-white">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl font-bold mb-6"><?php echo e(__('home.find_unit.title')); ?></h2>
                    <p class="text-lg text-white/80 mb-8">
                        <?php echo e(__('home.find_unit.description')); ?>

                    </p>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Oued-Smar et Cheraga (Alger)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Lakhdaria (Bouira)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Oran et Sig (Mascara)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Souk Ahras</span>
                        </li>
                    </ul>
                    <a href="<?php echo e(route('production-units')); ?>" 
                       class="inline-flex items-center bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        <?php echo e(__('home.find_unit.cta')); ?>

                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                        </svg>
                    </a>
                </div>
                <div class="relative">
                    <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden shadow-2xl">
                        <div id="home-map" class="w-full h-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Calculateur de Peinture -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <img src="/images/paint-calculator.jpg" alt="Calculateur de peinture" 
                         class="rounded-xl shadow-xl transform -rotate-2">
                </div>
                <div>
                    <span class="bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-1 rounded-full"><?php echo e(__('home.paint_calculator.free_tool')); ?></span>
                    <h2 class="text-3xl font-bold mt-4 mb-6"><?php echo e(__('home.paint_calculator.title')); ?></h2>
                    <p class="text-lg text-gray-600 mb-8">
                        <?php echo e(__('home.paint_calculator.description')); ?>

                    </p>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.precise_calculation')); ?></span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.surface_type')); ?></span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.recommendations')); ?></span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.budget_estimation')); ?></span>
                        </li>
                    </ul>
                    <a href="<?php echo e(route('calculator.index')); ?>" 
                       class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        <?php echo e(__('home.paint_calculator.cta')); ?>

                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour affichage produit sur la page d'accueil -->
    <div id="homeProductModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Header du modal -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="homeModalTitle">Produit ENAP</h3>
                    <button onclick="closeHomeProductModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Contenu du modal -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Image -->
                    <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                        <img id="homeModalImage" src="" alt="" class="w-full h-full object-cover">
                    </div>

                    <!-- Informations -->
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-xl font-semibold text-gray-900" id="homeModalProductName"></h4>
                            <p class="text-gray-600 mt-2" id="homeModalDescription"></p>
                        </div>

                        <div class="border-t pt-4">
                            <h5 class="font-medium text-gray-900 mb-2"><?php echo e(__('home.products_gallery.features')); ?></h5>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• <?php echo e(__('home.products_gallery.high_quality')); ?></li>
                                <li>• <?php echo e(__('home.products_gallery.easy_application')); ?></li>
                                <li>• <?php echo e(__('home.products_gallery.durable_finish')); ?></li>
                                <li>• <?php echo e(__('home.products_gallery.eco_friendly')); ?></li>
                            </ul>
                        </div>

                        <div class="border-t pt-4">
                            <div class="flex space-x-3">
                                <a href="<?php echo e(route('products.index')); ?>" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-center">
                                    <?php echo e(__('home.products_gallery.view_products')); ?>

                                </a>
                                <a href="<?php echo e(route('quotes.create')); ?>" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-center">
                                    <?php echo e(__('home.products_gallery.request_quote')); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        // Variables pour le carrousel de la page d'accueil
        let homeCarouselInterval;
        let homeIsPlaying = true;
        let homeCurrentIndex = 0;
        let homeCurrentProduct = null;

        // Fonctions du modal produit
        function openHomeProductModal(name, image, description) {
            homeCurrentProduct = { name, image, description };

            document.getElementById('homeModalTitle').textContent = name;
            document.getElementById('homeModalProductName').textContent = name;
            document.getElementById('homeModalDescription').textContent = description;
            document.getElementById('homeModalImage').src = '<?php echo e(asset("images/")); ?>/' + image;
            document.getElementById('homeModalImage').alt = name;

            document.getElementById('homeProductModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeHomeProductModal() {
            document.getElementById('homeProductModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            homeCurrentProduct = null;
        }

        // Fonctions du carrousel
        function createHomeCarousel() {
            const container = document.getElementById('homeProductsGrid');
            if (!container) return;

            const products = container.querySelectorAll('[data-product-index]');
            if (products.length === 0) return;

            const prevBtn = document.getElementById('homePrevBtn');
            const nextBtn = document.getElementById('homeNextBtn');
            const playPauseBtn = document.getElementById('homePlayPauseBtn');
            const playIcon = document.getElementById('homePlayIcon');
            const pauseIcon = document.getElementById('homePauseIcon');

            function highlightProduct(index) {
                products.forEach((product, i) => {
                    if (i === index) {
                        product.style.transform = 'scale(1.05) translateY(-8px)';
                        product.style.zIndex = '10';
                        product.style.boxShadow = '0 20px 40px rgba(59, 130, 246, 0.3)';
                        product.style.border = '3px solid #3b82f6';
                    } else {
                        product.style.transform = 'scale(1) translateY(0)';
                        product.style.zIndex = '1';
                        product.style.boxShadow = '';
                        product.style.border = '';
                    }
                });
            }

            function nextProduct() {
                homeCurrentIndex = (homeCurrentIndex + 1) % products.length;
                highlightProduct(homeCurrentIndex);
            }

            function prevProduct() {
                homeCurrentIndex = homeCurrentIndex === 0 ? products.length - 1 : homeCurrentIndex - 1;
                highlightProduct(homeCurrentIndex);
            }

            function startHomeCarousel() {
                if (homeCarouselInterval) clearInterval(homeCarouselInterval);
                homeCarouselInterval = setInterval(nextProduct, 4000);
                homeIsPlaying = true;
                playIcon.classList.add('hidden');
                pauseIcon.classList.remove('hidden');
            }

            function stopHomeCarousel() {
                if (homeCarouselInterval) clearInterval(homeCarouselInterval);
                homeIsPlaying = false;
                playIcon.classList.remove('hidden');
                pauseIcon.classList.add('hidden');
            }

            // Event listeners
            if (prevBtn) {
                prevBtn.addEventListener('click', () => {
                    prevProduct();
                    if (homeIsPlaying) {
                        stopHomeCarousel();
                        setTimeout(startHomeCarousel, 6000);
                    }
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', () => {
                    nextProduct();
                    if (homeIsPlaying) {
                        stopHomeCarousel();
                        setTimeout(startHomeCarousel, 6000);
                    }
                });
            }

            if (playPauseBtn) {
                playPauseBtn.addEventListener('click', () => {
                    if (homeIsPlaying) {
                        stopHomeCarousel();
                    } else {
                        startHomeCarousel();
                    }
                });
            }

            // Pause au survol
            container.addEventListener('mouseenter', () => {
                if (homeIsPlaying && homeCarouselInterval) {
                    clearInterval(homeCarouselInterval);
                }
            });

            container.addEventListener('mouseleave', () => {
                if (homeIsPlaying) {
                    startHomeCarousel();
                }
            });

            // Démarrer le carrousel
            highlightProduct(0);
            startHomeCarousel();
        }

        // Autplay du slider principal
        setInterval(() => {
            const slider = document.querySelector('[x-data]').__x.$data;
            const maxSlides = 3; // 3 slides : Accueil, Solutions, Produits
            slider.currentSlide = (slider.currentSlide + 1) % maxSlides;
        }, 6000);

        // Initialisation après chargement
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(createHomeCarousel, 1000);

            // Fermer le modal en cliquant à l'extérieur
            document.addEventListener('click', function(e) {
                const modal = document.getElementById('homeProductModal');
                if (e.target === modal) {
                    closeHomeProductModal();
                }
            });

            // Fermer le modal avec Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('homeProductModal').classList.contains('hidden')) {
                    closeHomeProductModal();
                }
            });
        });
    </script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map
        const map = L.map('home-map').setView([36.7538, 3.0588], 6);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add markers for ENAP units
        const units = [
            { name: 'ENAP Oued-Smar', lat: 36.7167, lng: 3.1667 },
            { name: 'ENAP Cheraga', lat: 36.7667, lng: 2.9500 },
            { name: 'ENAP Lakhdaria', lat: 36.5647, lng: 3.5933 },
            { name: 'ENAP Oran', lat: 35.6969, lng: -0.6331 },
            { name: 'ENAP Sig', lat: 35.5281, lng: -0.1886 },
            { name: 'ENAP Souk Ahras', lat: 36.2864, lng: 7.9511 }
        ];

        units.forEach(unit => {
            L.marker([unit.lat, unit.lng])
                .bindPopup(unit.name)
                .addTo(map);
        });
    });
    </script>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
    .aspect-w-4 {
        position: relative;
        padding-bottom: 75%;
    }
    .aspect-w-4 > * {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }
    </style>
    <?php $__env->stopPush(); ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\wsite-enap\resources\views/home.blade.php ENDPATH**/ ?>