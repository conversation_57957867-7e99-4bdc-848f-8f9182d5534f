

<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/home-enhancements.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">
    <!-- Hero Section avec Slider Photo par Photo -->
    <?php echo $__env->make('components.single-photo-slider', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>



    <!-- Section Présentation ENAP -->
    <div class="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
        <!-- Éléments décoratifs -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-10 w-32 h-32 bg-blue-200 rounded-full opacity-20 animate-pulse"></div>
            <div class="absolute bottom-20 right-10 w-40 h-40 bg-purple-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full opacity-10 animate-spin" style="animation-duration: 20s;"></div>
        </div>

        <div class="container mx-auto px-6 relative z-10">
            <!-- En-tête de section -->
            <div class="text-center mb-16">
                <span class="inline-block bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-2 rounded-full mb-4">
                    🎨 Leader Algérien depuis 1967
                </span>
                <h2 class="text-5xl font-bold text-gray-900 mb-6">Excellence ENAP</h2>
                <p class="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                    Découvrez l'univers ENAP, pionnier algérien dans la production de peintures et revêtements organiques de haute qualité.
                    Plus de 55 ans d'innovation au service de vos projets.
                </p>
            </div>

            <!-- Chiffres clés avec animations -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <div class="text-center group">
                    <div class="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-white/50">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                            </svg>
                        </div>
                        <div class="text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform">6</div>
                        <div class="text-gray-700 font-semibold text-lg">Unités de Production</div>
                        <div class="text-gray-500 text-sm mt-2">À travers l'Algérie</div>
                    </div>
                </div>

                <div class="text-center group">
                    <div class="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-white/50">
                        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="text-6xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform">55+</div>
                        <div class="text-gray-700 font-semibold text-lg">Années d'Expérience</div>
                        <div class="text-gray-500 text-sm mt-2">Depuis 1967</div>
                    </div>
                </div>

                <div class="text-center group">
                    <div class="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-white/50">
                        <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div class="text-6xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform">1000+</div>
                        <div class="text-gray-700 font-semibold text-lg">Produits</div>
                        <div class="text-gray-500 text-sm mt-2">Gamme complète</div>
                    </div>
                </div>

                <div class="text-center group">
                    <div class="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-white/50">
                        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div class="text-6xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform">10K+</div>
                        <div class="text-gray-700 font-semibold text-lg">Clients Satisfaits</div>
                        <div class="text-gray-500 text-sm mt-2">Particuliers & Pros</div>
                    </div>
                </div>
            </div>

            <!-- Boutons d'action -->
            <div class="text-center">
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <a href="<?php echo e(route('products.index')); ?>"
                       class="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        Découvrir nos Produits
                    </a>
                    <a href="<?php echo e(route('color-simulator')); ?>"
                       class="inline-flex items-center bg-white text-gray-700 px-8 py-4 rounded-xl font-semibold hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl border border-gray-200">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h4a2 2 0 002-2V9a2 2 0 00-2-2H7a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                        </svg>
                        Simulateur de Couleurs
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Gammes de Produits Vedettes -->
    <div class="py-20 bg-white relative overflow-hidden">
        <!-- Éléments décoratifs -->
        <div class="absolute inset-0">
            <div class="absolute top-10 right-10 w-64 h-64 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full opacity-30 blur-3xl"></div>
            <div class="absolute bottom-10 left-10 w-48 h-48 bg-gradient-to-br from-green-100 to-blue-100 rounded-full opacity-30 blur-3xl"></div>
        </div>

        <div class="container mx-auto px-6 relative z-10">
            <!-- En-tête de section -->
            <div class="text-center mb-16">
                <span class="inline-block bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 text-sm font-semibold px-4 py-2 rounded-full mb-4">
                    🎨 Nos Produits Vedettes
                </span>
                <h2 class="text-5xl font-bold text-gray-900 mb-6">Gammes ENAP</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Découvrez notre sélection de peintures premium, chacune conçue pour répondre à vos besoins spécifiques
                    avec la qualité et l'innovation qui font la réputation d'ENAP.
                </p>
            </div>

            <!-- Grille de produits vedettes -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <!-- ACRYPHOB -->
                <div class="group relative bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden border border-gray-100">
                    <div class="relative h-64 overflow-hidden">
                        <img src="<?php echo e(asset('images/slides/acryphob.jpg')); ?>"
                             alt="ACRYPHOB - Peinture acrylique"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                        <div class="absolute top-4 left-4">
                            <span class="bg-blue-600 text-white text-xs font-semibold px-3 py-1 rounded-full">
                                PREMIUM
                            </span>
                        </div>
                        <div class="absolute bottom-4 left-4 right-4">
                            <h3 class="text-white text-2xl font-bold mb-2">ACRYPHOB</h3>
                            <p class="text-white/90 text-sm">Peinture acrylique haute performance</p>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4 leading-relaxed">
                            Excellence en peinture acrylique pour tous vos projets de décoration intérieure et extérieure.
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                <span class="text-xs text-gray-500 ml-2">+50 couleurs</span>
                            </div>
                            <a href="<?php echo e(route('products.index')); ?>"
                               class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                                Découvrir
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- BLANCRYL -->
                <div class="group relative bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden border border-gray-100">
                    <div class="relative h-64 overflow-hidden">
                        <img src="<?php echo e(asset('images/slides/blancryl.jpg')); ?>"
                             alt="BLANCRYL - Peinture blanche premium"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                        <div class="absolute top-4 left-4">
                            <span class="bg-green-600 text-white text-xs font-semibold px-3 py-1 rounded-full">
                                ECO+
                            </span>
                        </div>
                        <div class="absolute bottom-4 left-4 right-4">
                            <h3 class="text-white text-2xl font-bold mb-2">BLANCRYL</h3>
                            <p class="text-white/90 text-sm">Peinture blanche premium</p>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4 leading-relaxed">
                            Innovation technologique et performance exceptionnelle pour un blanc éclatant et durable.
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-white border-2 border-gray-300 rounded-full"></div>
                                <div class="w-3 h-3 bg-gray-100 border-2 border-gray-300 rounded-full"></div>
                                <span class="text-xs text-gray-500 ml-2">Blanc pur</span>
                            </div>
                            <a href="<?php echo e(route('products.index')); ?>"
                               class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                                Découvrir
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- DECOSOIE -->
                <div class="group relative bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 overflow-hidden border border-gray-100">
                    <div class="relative h-64 overflow-hidden">
                        <img src="<?php echo e(asset('images/slides/decosoie.jpg')); ?>"
                             alt="DECOSOIE - Finition soyeuse"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                        <div class="absolute top-4 left-4">
                            <span class="bg-purple-600 text-white text-xs font-semibold px-3 py-1 rounded-full">
                                LUXE
                            </span>
                        </div>
                        <div class="absolute bottom-4 left-4 right-4">
                            <h3 class="text-white text-2xl font-bold mb-2">DECOSOIE</h3>
                            <p class="text-white/90 text-sm">Finition soyeuse élégante</p>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4 leading-relaxed">
                            Élégance et raffinement avec une finition soyeuse pour un rendu décoratif exceptionnel.
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-pink-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-indigo-500 rounded-full"></div>
                                <span class="text-xs text-gray-500 ml-2">Effet soie</span>
                            </div>
                            <a href="<?php echo e(route('products.index')); ?>"
                               class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                                Découvrir
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bouton voir toute la gamme -->
            <div class="text-center">
                <a href="<?php echo e(route('products.index')); ?>"
                   class="inline-flex items-center bg-gradient-to-r from-gray-900 to-gray-700 text-white px-10 py-4 rounded-xl font-semibold hover:from-gray-800 hover:to-gray-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    Voir Toute la Gamme ENAP
                    <span class="ml-2 bg-white/20 text-xs px-2 py-1 rounded-full">1000+ produits</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Section Services et Outils -->
    <div class="py-20 bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900 text-white relative overflow-hidden">
        <!-- Éléments décoratifs -->
        <div class="absolute inset-0">
            <div class="absolute top-20 left-20 w-40 h-40 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
            <div class="absolute bottom-20 right-20 w-32 h-32 bg-purple-300/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-spin" style="animation-duration: 30s;"></div>
        </div>

        <div class="container mx-auto px-6 relative z-10">
            <!-- En-tête de section -->
            <div class="text-center mb-16">
                <span class="inline-block bg-white/20 backdrop-blur-sm text-white text-sm font-semibold px-4 py-2 rounded-full mb-4">
                    🛠️ Outils et Services
                </span>
                <h2 class="text-5xl font-bold mb-6">Solutions Digitales ENAP</h2>
                <p class="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
                    Découvrez nos outils innovants conçus pour vous accompagner dans tous vos projets de peinture,
                    de la conception à la réalisation.
                </p>
            </div>

            <!-- Grille des services -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <!-- Simulateur de Couleurs -->
                <div class="group relative bg-white/10 backdrop-blur-sm rounded-3xl p-8 hover:bg-white/20 transition-all duration-500 transform hover:-translate-y-2 border border-white/20">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="relative z-10">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h4a2 2 0 002-2V9a2 2 0 00-2-2H7a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-center">Simulateur de Couleurs</h3>
                        <p class="text-white/80 text-center mb-6 leading-relaxed">
                            Visualisez vos projets en 3D avec notre simulateur interactif. Testez des milliers de combinaisons de couleurs.
                        </p>
                        <div class="text-center">
                            <a href="<?php echo e(route('color-simulator')); ?>"
                               class="inline-flex items-center bg-white text-blue-900 px-6 py-3 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                                Essayer Maintenant
                                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Calculateur de Quantité -->
                <div class="group relative bg-white/10 backdrop-blur-sm rounded-3xl p-8 hover:bg-white/20 transition-all duration-500 transform hover:-translate-y-2 border border-white/20">
                    <div class="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="relative z-10">
                        <div class="w-20 h-20 bg-gradient-to-br from-green-400 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-center">Calculateur de Quantité</h3>
                        <p class="text-white/80 text-center mb-6 leading-relaxed">
                            Calculez précisément la quantité de peinture nécessaire pour votre projet et optimisez votre budget.
                        </p>
                        <div class="text-center">
                            <a href="<?php echo e(route('calculator.index')); ?>"
                               class="inline-flex items-center bg-white text-green-900 px-6 py-3 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                                Calculer Maintenant
                                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Support Technique -->
                <div class="group relative bg-white/10 backdrop-blur-sm rounded-3xl p-8 hover:bg-white/20 transition-all duration-500 transform hover:-translate-y-2 border border-white/20">
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div class="relative z-10">
                        <div class="w-20 h-20 bg-gradient-to-br from-purple-400 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-bold mb-4 text-center">Support Technique</h3>
                        <p class="text-white/80 text-center mb-6 leading-relaxed">
                            Bénéficiez de l'expertise de nos techniciens pour tous vos projets. Conseils personnalisés garantis.
                        </p>
                        <div class="text-center">
                            <a href="<?php echo e(route('contact')); ?>"
                               class="inline-flex items-center bg-white text-purple-900 px-6 py-3 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105">
                                Nous Contacter
                                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section avantages -->
            <div class="text-center">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="flex flex-col items-center">
                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h4 class="font-semibold mb-2">Rapide</h4>
                        <p class="text-white/80 text-sm">Résultats instantanés</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h4 class="font-semibold mb-2">Précis</h4>
                        <p class="text-white/80 text-sm">Calculs professionnels</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h4 class="font-semibold mb-2">Mobile</h4>
                        <p class="text-white/80 text-sm">Accessible partout</p>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <h4 class="font-semibold mb-2">Gratuit</h4>
                        <p class="text-white/80 text-sm">100% gratuit</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Témoignages Clients -->
    <div class="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        <!-- Éléments décoratifs -->
        <div class="absolute inset-0">
            <div class="absolute top-10 left-10 w-72 h-72 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full opacity-30 blur-3xl"></div>
            <div class="absolute bottom-10 right-10 w-64 h-64 bg-gradient-to-br from-green-100 to-blue-100 rounded-full opacity-30 blur-3xl"></div>
        </div>

        <div class="container mx-auto px-6 relative z-10">
            <!-- En-tête de section -->
            <div class="text-center mb-16">
                <span class="inline-block bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 text-sm font-semibold px-4 py-2 rounded-full mb-4">
                    💬 Témoignages Clients
                </span>
                <h2 class="text-5xl font-bold text-gray-900 mb-6">Ils Nous Font Confiance</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Découvrez les retours de nos clients satisfaits qui ont choisi ENAP pour leurs projets de peinture.
                </p>
            </div>

            <!-- Grille de témoignages -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <!-- Témoignage 1 -->
                <div class="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 p-8 border border-gray-100">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                            AM
                        </div>
                        <div class="ml-4">
                            <h4 class="font-bold text-gray-900">Ahmed Mansouri</h4>
                            <p class="text-gray-600 text-sm">Architecte - Alger</p>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                    <blockquote class="text-gray-700 leading-relaxed mb-4">
                        "ACRYPHOB a transformé mes projets. La qualité est exceptionnelle et la durabilité impressionnante.
                        Mes clients sont toujours satisfaits du rendu final."
                    </blockquote>
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Client vérifié
                    </div>
                </div>

                <!-- Témoignage 2 -->
                <div class="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 p-8 border border-gray-100">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                            FB
                        </div>
                        <div class="ml-4">
                            <h4 class="font-bold text-gray-900">Fatima Benali</h4>
                            <p class="text-gray-600 text-sm">Décoratrice - Oran</p>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                    <blockquote class="text-gray-700 leading-relaxed mb-4">
                        "DECOSOIE apporte cette touche d'élégance que mes clients recherchent.
                        La finition soyeuse est parfaite pour les intérieurs haut de gamme."
                    </blockquote>
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Client vérifié
                    </div>
                </div>

                <!-- Témoignage 3 -->
                <div class="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 p-8 border border-gray-100">
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white font-bold text-xl">
                            KH
                        </div>
                        <div class="ml-4">
                            <h4 class="font-bold text-gray-900">Karim Hadj</h4>
                            <p class="text-gray-600 text-sm">Entrepreneur - Constantine</p>
                        </div>
                    </div>
                    <div class="flex mb-4">
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                    <blockquote class="text-gray-700 leading-relaxed mb-4">
                        "KATIFA est devenue ma référence pour tous mes chantiers professionnels.
                        Rapport qualité-prix imbattable et résistance exceptionnelle."
                    </blockquote>
                    <div class="flex items-center text-sm text-gray-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Client vérifié
                    </div>
                </div>
            </div>

            <!-- Statistiques de satisfaction -->
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 text-white text-center">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <div>
                        <div class="text-4xl font-bold mb-2">98%</div>
                        <div class="text-white/90">Clients Satisfaits</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold mb-2">4.9/5</div>
                        <div class="text-white/90">Note Moyenne</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold mb-2">15K+</div>
                        <div class="text-white/90">Projets Réalisés</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold mb-2">24/7</div>
                        <div class="text-white/90">Support Client</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Actualités -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <div class="flex justify-between items-center mb-12">
                <div>
                    <h2 class="text-3xl font-bold mb-4"><?php echo e(__('home.news.title')); ?></h2>
                    <p class="text-gray-600"><?php echo e(__('home.news.subtitle')); ?></p>
                </div>
                <a href="<?php echo e(route('blog.index')); ?>" 
                   class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-800">
                    <?php echo e(__('home.news.see_all')); ?>

                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                </a>
            </div>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <?php $__empty_1 = true; $__currentLoopData = $latestPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <article class="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
                    <a href="<?php echo e(route('blog.show', $post->slug)); ?>" class="block">
                        <div class="relative h-48">
                            <?php if($post->featured_image): ?>
                            <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" 
                                 alt="<?php echo e($post->title); ?>" 
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            <?php else: ?>
                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            <?php endif; ?>
                            <?php if($post->category): ?>
                            <span class="absolute top-4 left-4 bg-blue-600 text-white text-sm px-3 py-1 rounded-full">
                                <?php echo e($post->category->name); ?>

                            </span>
                            <?php endif; ?>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600">
                                <?php echo e($post->title); ?>

                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-3"><?php echo e($post->excerpt); ?></p>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-500"><?php echo e($post->formatted_date); ?></span>
                                <span class="text-blue-600 font-medium"><?php echo e(__('home.news.read_more')); ?></span>
                            </div>
                        </div>
                    </a>
                </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-3 text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
                    </svg>
                    <p class="text-gray-600 mb-4"><?php echo e(__('home.news.no_articles')); ?></p>
                    <p class="text-gray-500"><?php echo e(__('home.news.come_back_soon')); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>



    <!-- Section Localisateur d'Unités -->
    <div class="relative py-16 overflow-hidden">
        <div class="absolute inset-0">
            <img src="/images/map-bg.jpg" alt="Carte de l'Algérie" class="w-full h-full object-cover opacity-20">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-900/90 to-blue-600/90"></div>
        </div>
        
        <div class="relative container mx-auto px-6 text-white">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl font-bold mb-6"><?php echo e(__('home.find_unit.title')); ?></h2>
                    <p class="text-lg text-white/80 mb-8">
                        <?php echo e(__('home.find_unit.description')); ?>

                    </p>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Oued-Smar et Cheraga (Alger)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Lakhdaria (Bouira)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Oran et Sig (Mascara)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Souk Ahras</span>
                        </li>
                    </ul>
                    <a href="<?php echo e(route('production-units')); ?>" 
                       class="inline-flex items-center bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        <?php echo e(__('home.find_unit.cta')); ?>

                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                        </svg>
                    </a>
                </div>
                <div class="relative">
                    <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden shadow-2xl">
                        <div id="home-map" class="w-full h-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Calculateur de Peinture -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <img src="/images/paint-calculator.jpg" alt="Calculateur de peinture" 
                         class="rounded-xl shadow-xl transform -rotate-2">
                </div>
                <div>
                    <span class="bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-1 rounded-full"><?php echo e(__('home.paint_calculator.free_tool')); ?></span>
                    <h2 class="text-3xl font-bold mt-4 mb-6"><?php echo e(__('home.paint_calculator.title')); ?></h2>
                    <p class="text-lg text-gray-600 mb-8">
                        <?php echo e(__('home.paint_calculator.description')); ?>

                    </p>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.precise_calculation')); ?></span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.surface_type')); ?></span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.recommendations')); ?></span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.budget_estimation')); ?></span>
                        </li>
                    </ul>
                    <a href="<?php echo e(route('calculator.index')); ?>" 
                       class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        <?php echo e(__('home.paint_calculator.cta')); ?>

                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour affichage produit sur la page d'accueil -->
    <div id="homeProductModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Header du modal -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="homeModalTitle">Produit ENAP</h3>
                    <button onclick="closeHomeProductModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Contenu du modal -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Image -->
                    <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                        <img id="homeModalImage" src="" alt="" class="w-full h-full object-cover">
                    </div>

                    <!-- Informations -->
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-xl font-semibold text-gray-900" id="homeModalProductName"></h4>
                            <p class="text-gray-600 mt-2" id="homeModalDescription"></p>
                        </div>

                        <div class="border-t pt-4">
                            <h5 class="font-medium text-gray-900 mb-2"><?php echo e(__('home.products_gallery.features')); ?></h5>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• <?php echo e(__('home.products_gallery.high_quality')); ?></li>
                                <li>• <?php echo e(__('home.products_gallery.easy_application')); ?></li>
                                <li>• <?php echo e(__('home.products_gallery.durable_finish')); ?></li>
                                <li>• <?php echo e(__('home.products_gallery.eco_friendly')); ?></li>
                            </ul>
                        </div>

                        <div class="border-t pt-4">
                            <div class="flex space-x-3">
                                <a href="<?php echo e(route('products.index')); ?>" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-center">
                                    <?php echo e(__('home.products_gallery.view_products')); ?>

                                </a>
                                <a href="<?php echo e(route('quotes.create')); ?>" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-center">
                                    <?php echo e(__('home.products_gallery.request_quote')); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        // Variables pour le carrousel de la page d'accueil
        let homeCarouselInterval;
        let homeIsPlaying = true;
        let homeCurrentIndex = 0;
        let homeCurrentProduct = null;

        // Fonctions du modal produit
        function openHomeProductModal(name, image, description) {
            homeCurrentProduct = { name, image, description };

            document.getElementById('homeModalTitle').textContent = name;
            document.getElementById('homeModalProductName').textContent = name;
            document.getElementById('homeModalDescription').textContent = description;
            document.getElementById('homeModalImage').src = '<?php echo e(asset("images/")); ?>/' + image;
            document.getElementById('homeModalImage').alt = name;

            document.getElementById('homeProductModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeHomeProductModal() {
            document.getElementById('homeProductModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            homeCurrentProduct = null;
        }

        // Fonctions du carrousel
        function createHomeCarousel() {
            const container = document.getElementById('homeProductsGrid');
            if (!container) return;

            const products = container.querySelectorAll('[data-product-index]');
            if (products.length === 0) return;

            const prevBtn = document.getElementById('homePrevBtn');
            const nextBtn = document.getElementById('homeNextBtn');
            const playPauseBtn = document.getElementById('homePlayPauseBtn');
            const playIcon = document.getElementById('homePlayIcon');
            const pauseIcon = document.getElementById('homePauseIcon');

            function highlightProduct(index) {
                products.forEach((product, i) => {
                    if (i === index) {
                        product.style.transform = 'scale(1.05) translateY(-8px)';
                        product.style.zIndex = '10';
                        product.style.boxShadow = '0 20px 40px rgba(59, 130, 246, 0.3)';
                        product.style.border = '3px solid #3b82f6';
                    } else {
                        product.style.transform = 'scale(1) translateY(0)';
                        product.style.zIndex = '1';
                        product.style.boxShadow = '';
                        product.style.border = '';
                    }
                });
            }

            function nextProduct() {
                homeCurrentIndex = (homeCurrentIndex + 1) % products.length;
                highlightProduct(homeCurrentIndex);
            }

            function prevProduct() {
                homeCurrentIndex = homeCurrentIndex === 0 ? products.length - 1 : homeCurrentIndex - 1;
                highlightProduct(homeCurrentIndex);
            }

            function startHomeCarousel() {
                if (homeCarouselInterval) clearInterval(homeCarouselInterval);
                homeCarouselInterval = setInterval(nextProduct, 4000);
                homeIsPlaying = true;
                playIcon.classList.add('hidden');
                pauseIcon.classList.remove('hidden');
            }

            function stopHomeCarousel() {
                if (homeCarouselInterval) clearInterval(homeCarouselInterval);
                homeIsPlaying = false;
                playIcon.classList.remove('hidden');
                pauseIcon.classList.add('hidden');
            }

            // Event listeners
            if (prevBtn) {
                prevBtn.addEventListener('click', () => {
                    prevProduct();
                    if (homeIsPlaying) {
                        stopHomeCarousel();
                        setTimeout(startHomeCarousel, 6000);
                    }
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', () => {
                    nextProduct();
                    if (homeIsPlaying) {
                        stopHomeCarousel();
                        setTimeout(startHomeCarousel, 6000);
                    }
                });
            }

            if (playPauseBtn) {
                playPauseBtn.addEventListener('click', () => {
                    if (homeIsPlaying) {
                        stopHomeCarousel();
                    } else {
                        startHomeCarousel();
                    }
                });
            }

            // Pause au survol
            container.addEventListener('mouseenter', () => {
                if (homeIsPlaying && homeCarouselInterval) {
                    clearInterval(homeCarouselInterval);
                }
            });

            container.addEventListener('mouseleave', () => {
                if (homeIsPlaying) {
                    startHomeCarousel();
                }
            });

            // Démarrer le carrousel
            highlightProduct(0);
            startHomeCarousel();
        }

        // Autplay du slider principal
        setInterval(() => {
            const slider = document.querySelector('[x-data]').__x.$data;
            const maxSlides = 3; // 3 slides : Accueil, Solutions, Produits
            slider.currentSlide = (slider.currentSlide + 1) % maxSlides;
        }, 6000);

        // Initialisation après chargement
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(createHomeCarousel, 1000);

            // Fermer le modal en cliquant à l'extérieur
            document.addEventListener('click', function(e) {
                const modal = document.getElementById('homeProductModal');
                if (e.target === modal) {
                    closeHomeProductModal();
                }
            });

            // Fermer le modal avec Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('homeProductModal').classList.contains('hidden')) {
                    closeHomeProductModal();
                }
            });
        });
    </script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map
        const map = L.map('home-map').setView([36.7538, 3.0588], 6);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add markers for ENAP units
        const units = [
            { name: 'ENAP Oued-Smar', lat: 36.7167, lng: 3.1667 },
            { name: 'ENAP Cheraga', lat: 36.7667, lng: 2.9500 },
            { name: 'ENAP Lakhdaria', lat: 36.5647, lng: 3.5933 },
            { name: 'ENAP Oran', lat: 35.6969, lng: -0.6331 },
            { name: 'ENAP Sig', lat: 35.5281, lng: -0.1886 },
            { name: 'ENAP Souk Ahras', lat: 36.2864, lng: 7.9511 }
        ];

        units.forEach(unit => {
            L.marker([unit.lat, unit.lng])
                .bindPopup(unit.name)
                .addTo(map);
        });
    });
    </script>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
    .aspect-w-4 {
        position: relative;
        padding-bottom: 75%;
    }
    .aspect-w-4 > * {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }
    </style>
    <?php $__env->stopPush(); ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="<?php echo e(asset('js/home-enhancements.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\wsite-enap\resources\views/home.blade.php ENDPATH**/ ?>