

<?php $__env->startSection('content'); ?>
<div class="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">
    <!-- Hero Section avec Slider -->
    <div x-data="{ currentSlide: 0 }" class="relative">
        <div class="relative h-[600px] overflow-hidden">
            <!-- Slides -->
            <div class="absolute inset-0 transition-transform duration-500" 
                 :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
                <!-- Slide 1 -->
                <div class="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-900 to-blue-600">
                    <div class="container mx-auto px-6 h-full flex items-center">
                        <div class="max-w-2xl text-white">
                            <h1 class="text-5xl font-bold mb-6"><?php echo e(__('home.hero.title')); ?></h1>
                            <p class="text-xl mb-8"><?php echo e(__('home.hero.subtitle')); ?></p>
                            <div class="flex flex-wrap gap-4">
                                <a href="<?php echo e(route('products.index')); ?>"
                                   class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                    <?php echo e(__('home.hero.cta.products')); ?>

                                </a>
                                <a href="<?php echo e(route('color-simulator')); ?>"
                                   class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                                    <?php echo e(__('home.hero.cta.simulator')); ?>

                                </a>
                                <a href="<?php echo e(route('promotions.index')); ?>"
                                   class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition-colors">
                                    <?php echo e(__('home.hero.cta.promotions')); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Slide 2 -->
                <div class="absolute inset-0 w-full h-full bg-gradient-to-r from-green-900 to-green-600">
                    <div class="container mx-auto px-6 h-full flex items-center">
                        <div class="max-w-2xl text-white">
                            <h2 class="text-4xl font-bold mb-6"><?php echo e(__('home.solutions.title')); ?></h2>
                            <p class="text-xl mb-8"><?php echo e(__('home.solutions.subtitle')); ?></p>                            <a href="<?php echo e(route('quotes.create')); ?>" 
                               class="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                <?php echo e(__('home.solutions.cta')); ?>

                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contrôles du slider -->
            <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                <button @click="currentSlide = 0" 
                        :class="{ 'bg-white': currentSlide === 0, 'bg-white/50': currentSlide !== 0 }"
                        class="w-3 h-3 rounded-full transition-colors"></button>
                <button @click="currentSlide = 1" 
                        :class="{ 'bg-white': currentSlide === 1, 'bg-white/50': currentSlide !== 1 }"
                        class="w-3 h-3 rounded-full transition-colors"></button>
            </div>
        </div>
    </div>

    <!-- Section Chiffres Clés -->
    <div class="py-16 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">6</div>
                    <div class="text-gray-600"><?php echo e(__('home.key_figures.production_units')); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">40+</div>
                    <div class="text-gray-600"><?php echo e(__('home.key_figures.experience')); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">1000+</div>
                    <div class="text-gray-600"><?php echo e(__('home.key_figures.products')); ?></div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-blue-600 mb-2">5000+</div>
                    <div class="text-gray-600"><?php echo e(__('home.key_figures.clients')); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Gammes de Produits -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-4"><?php echo e(__('home.product_ranges.title')); ?></h2>
            <p class="text-gray-600 text-center mb-12"><?php echo e(__('home.product_ranges.subtitle')); ?></p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
                    <div class="relative">
                        <img src="<?php echo e($category->image_url); ?>" alt="<?php echo e($category->getTranslation('name', app()->getLocale())); ?>" 
                             class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                        <h3 class="absolute bottom-4 left-4 text-white text-xl font-semibold">
                            <?php echo e($category->getTranslation('name', app()->getLocale())); ?>

                        </h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4"><?php echo e($category->getTranslation('description', app()->getLocale())); ?></p>
                        <a href="<?php echo e(route('products.category', $category->id)); ?>" 
                           class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-800">
                            <?php echo e(__('home.product_ranges.cta')); ?>

                            <svg class="w-4 h-4 <?php echo e(app()->getLocale() === 'ar' ? 'mr-2 rotate-180' : 'ml-2'); ?>" 
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>

    <!-- Section Services -->
    <div class="py-16 bg-gradient-to-br from-blue-900 to-blue-700 text-white">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-12"><?php echo e(__('home.services.title')); ?></h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center p-6 bg-white/10 rounded-lg backdrop-blur-sm">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2"><?php echo e(__('home.services.color_simulator.title')); ?></h3>
                    <p class="text-white/80"><?php echo e(__('home.services.color_simulator.description')); ?></p>
                </div>

                <div class="text-center p-6 bg-white/10 rounded-lg backdrop-blur-sm">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                    </div>
                    <h3 class="text-xl font-semibold mb-2"><?php echo e(__('home.services.quantity_calculator.title')); ?></h3>
                    <p class="text-white/80"><?php echo e(__('home.services.quantity_calculator.description')); ?></p>
                </div>

                <div class="text-center p-6 bg-white/10 rounded-lg backdrop-blur-sm">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2"><?php echo e(__('home.services.technical_support.title')); ?></h3>
                    <p class="text-white/80"><?php echo e(__('home.services.technical_support.description')); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Actualités -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <div class="flex justify-between items-center mb-12">
                <div>
                    <h2 class="text-3xl font-bold mb-4"><?php echo e(__('home.news.title')); ?></h2>
                    <p class="text-gray-600"><?php echo e(__('home.news.subtitle')); ?></p>
                </div>
                <a href="<?php echo e(route('blog.index')); ?>" 
                   class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-800">
                    <?php echo e(__('home.news.see_all')); ?>

                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                </a>
            </div>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <?php $__empty_1 = true; $__currentLoopData = $latestPosts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <article class="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
                    <a href="<?php echo e(route('blog.show', $post->slug)); ?>" class="block">
                        <div class="relative h-48">
                            <?php if($post->featured_image): ?>
                            <img src="<?php echo e(asset('storage/' . $post->featured_image)); ?>" 
                                 alt="<?php echo e($post->title); ?>" 
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            <?php else: ?>
                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            <?php endif; ?>
                            <?php if($post->category): ?>
                            <span class="absolute top-4 left-4 bg-blue-600 text-white text-sm px-3 py-1 rounded-full">
                                <?php echo e($post->category->name); ?>

                            </span>
                            <?php endif; ?>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600">
                                <?php echo e($post->title); ?>

                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-3"><?php echo e($post->excerpt); ?></p>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-500"><?php echo e($post->formatted_date); ?></span>
                                <span class="text-blue-600 font-medium"><?php echo e(__('home.news.read_more')); ?></span>
                            </div>
                        </div>
                    </a>
                </article>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-3 text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
                    </svg>
                    <p class="text-gray-600 mb-4"><?php echo e(__('home.news.no_articles')); ?></p>
                    <p class="text-gray-500"><?php echo e(__('home.news.come_back_soon')); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Section Localisateur d'Unités -->
    <div class="relative py-16 overflow-hidden">
        <div class="absolute inset-0">
            <img src="/images/map-bg.jpg" alt="Carte de l'Algérie" class="w-full h-full object-cover opacity-20">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-900/90 to-blue-600/90"></div>
        </div>
        
        <div class="relative container mx-auto px-6 text-white">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl font-bold mb-6"><?php echo e(__('home.find_unit.title')); ?></h2>
                    <p class="text-lg text-white/80 mb-8">
                        <?php echo e(__('home.find_unit.description')); ?>

                    </p>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Oued-Smar et Cheraga (Alger)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Lakhdaria (Bouira)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Oran et Sig (Mascara)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Souk Ahras</span>
                        </li>
                    </ul>
                    <a href="<?php echo e(route('production-units')); ?>" 
                       class="inline-flex items-center bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        <?php echo e(__('home.find_unit.cta')); ?>

                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                        </svg>
                    </a>
                </div>
                <div class="relative">
                    <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden shadow-2xl">
                        <div id="home-map" class="w-full h-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Calculateur de Peinture -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <img src="/images/paint-calculator.jpg" alt="Calculateur de peinture" 
                         class="rounded-xl shadow-xl transform -rotate-2">
                </div>
                <div>
                    <span class="bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-1 rounded-full"><?php echo e(__('home.paint_calculator.free_tool')); ?></span>
                    <h2 class="text-3xl font-bold mt-4 mb-6"><?php echo e(__('home.paint_calculator.title')); ?></h2>
                    <p class="text-lg text-gray-600 mb-8">
                        <?php echo e(__('home.paint_calculator.description')); ?>

                    </p>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.precise_calculation')); ?></span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.surface_type')); ?></span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.recommendations')); ?></span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span><?php echo e(__('home.paint_calculator.benefits.budget_estimation')); ?></span>
                        </li>
                    </ul>
                    <a href="<?php echo e(route('calculator.index')); ?>" 
                       class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        <?php echo e(__('home.paint_calculator.cta')); ?>

                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        // Autplay du slider
        setInterval(() => {
            const slider = document.querySelector('[x-data]').__x.$data;
            slider.currentSlide = (slider.currentSlide + 1) % 2;
        }, 5000);
    </script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map
        const map = L.map('home-map').setView([36.7538, 3.0588], 6);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add markers for ENAP units
        const units = [
            { name: 'ENAP Oued-Smar', lat: 36.7167, lng: 3.1667 },
            { name: 'ENAP Cheraga', lat: 36.7667, lng: 2.9500 },
            { name: 'ENAP Lakhdaria', lat: 36.5647, lng: 3.5933 },
            { name: 'ENAP Oran', lat: 35.6969, lng: -0.6331 },
            { name: 'ENAP Sig', lat: 35.5281, lng: -0.1886 },
            { name: 'ENAP Souk Ahras', lat: 36.2864, lng: 7.9511 }
        ];

        units.forEach(unit => {
            L.marker([unit.lat, unit.lng])
                .bindPopup(unit.name)
                .addTo(map);
        });
    });
    </script>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('styles'); ?>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
    .aspect-w-4 {
        position: relative;
        padding-bottom: 75%;
    }
    .aspect-w-4 > * {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }
    </style>
    <?php $__env->stopPush(); ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\wsite-enap\resources\views/home.blade.php ENDPATH**/ ?>