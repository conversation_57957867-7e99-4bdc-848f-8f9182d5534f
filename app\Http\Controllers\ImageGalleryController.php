<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class ImageGalleryController extends Controller
{
    public function index()
    {
        $imagesPath = public_path('images');
        $images = [];
        
        if (File::exists($imagesPath)) {
            $files = File::files($imagesPath);
            
            foreach ($files as $file) {
                $filename = $file->getFilename();
                $extension = $file->getExtension();
                
                // Filtrer seulement les images
                if (in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'])) {
                    $images[] = [
                        'filename' => $filename,
                        'path' => 'images/' . $filename,
                        'url' => asset('images/' . $filename),
                        'size' => $file->getSize(),
                        'modified' => $file->getMTime(),
                        'extension' => $extension,
                    ];
                }
            }
        }
        
        // Trier par date de modification (plus récent en premier)
        usort($images, function($a, $b) {
            return $b['modified'] - $a['modified'];
        });
        
        return view('dashboard.gallery.index', compact('images'));
    }

    public function upload(Request $request)
    {
        $request->validate([
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
        ]);

        $uploadedFiles = [];
        
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $file) {
                $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                
                // Optimiser l'image avant de la sauvegarder
                $image = Image::make($file);
                
                // Redimensionner si trop grande (max 1920px de largeur)
                if ($image->width() > 1920) {
                    $image->resize(1920, null, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });
                }
                
                // Sauvegarder avec compression
                $image->save(public_path('images/' . $filename), 85);
                
                $uploadedFiles[] = [
                    'filename' => $filename,
                    'original_name' => $file->getClientOriginalName(),
                    'url' => asset('images/' . $filename),
                ];
            }
        }

        return response()->json([
            'success' => true,
            'message' => count($uploadedFiles) . ' image(s) uploadée(s) avec succès',
            'files' => $uploadedFiles,
        ]);
    }

    public function delete(Request $request)
    {
        $request->validate([
            'filename' => 'required|string',
        ]);

        $filename = $request->filename;
        $filePath = public_path('images/' . $filename);
        
        if (File::exists($filePath)) {
            File::delete($filePath);
            
            return response()->json([
                'success' => true,
                'message' => 'Image supprimée avec succès',
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'Image non trouvée',
        ], 404);
    }

    public function rename(Request $request)
    {
        $request->validate([
            'old_filename' => 'required|string',
            'new_filename' => 'required|string|regex:/^[a-zA-Z0-9._-]+$/',
        ]);

        $oldPath = public_path('images/' . $request->old_filename);
        $newPath = public_path('images/' . $request->new_filename);
        
        if (!File::exists($oldPath)) {
            return response()->json([
                'success' => false,
                'message' => 'Image non trouvée',
            ], 404);
        }
        
        if (File::exists($newPath)) {
            return response()->json([
                'success' => false,
                'message' => 'Une image avec ce nom existe déjà',
            ], 409);
        }
        
        File::move($oldPath, $newPath);
        
        return response()->json([
            'success' => true,
            'message' => 'Image renommée avec succès',
            'new_url' => asset('images/' . $request->new_filename),
        ]);
    }

    public function optimize(Request $request)
    {
        $request->validate([
            'filename' => 'required|string',
            'quality' => 'integer|min:10|max:100',
            'max_width' => 'integer|min:100|max:3000',
        ]);

        $filename = $request->filename;
        $filePath = public_path('images/' . $filename);
        
        if (!File::exists($filePath)) {
            return response()->json([
                'success' => false,
                'message' => 'Image non trouvée',
            ], 404);
        }

        $quality = $request->quality ?? 85;
        $maxWidth = $request->max_width ?? 1920;
        
        // Créer une sauvegarde
        $backupPath = public_path('images/backup_' . $filename);
        File::copy($filePath, $backupPath);
        
        try {
            $image = Image::make($filePath);
            
            // Redimensionner si nécessaire
            if ($image->width() > $maxWidth) {
                $image->resize($maxWidth, null, function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });
            }
            
            // Sauvegarder avec la nouvelle qualité
            $image->save($filePath, $quality);
            
            // Supprimer la sauvegarde
            File::delete($backupPath);
            
            return response()->json([
                'success' => true,
                'message' => 'Image optimisée avec succès',
                'new_size' => File::size($filePath),
            ]);
            
        } catch (\Exception $e) {
            // Restaurer la sauvegarde en cas d'erreur
            if (File::exists($backupPath)) {
                File::move($backupPath, $filePath);
            }
            
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'optimisation: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function getImageInfo(Request $request)
    {
        $request->validate([
            'filename' => 'required|string',
        ]);

        $filename = $request->filename;
        $filePath = public_path('images/' . $filename);
        
        if (!File::exists($filePath)) {
            return response()->json([
                'success' => false,
                'message' => 'Image non trouvée',
            ], 404);
        }

        $imageInfo = getimagesize($filePath);
        
        return response()->json([
            'success' => true,
            'info' => [
                'filename' => $filename,
                'size' => File::size($filePath),
                'width' => $imageInfo[0] ?? null,
                'height' => $imageInfo[1] ?? null,
                'mime_type' => $imageInfo['mime'] ?? null,
                'url' => asset('images/' . $filename),
                'modified' => File::lastModified($filePath),
            ],
        ]);
    }

    public function createThumbnail(Request $request)
    {
        $request->validate([
            'filename' => 'required|string',
            'width' => 'integer|min:50|max:500',
            'height' => 'integer|min:50|max:500',
        ]);

        $filename = $request->filename;
        $filePath = public_path('images/' . $filename);
        
        if (!File::exists($filePath)) {
            return response()->json([
                'success' => false,
                'message' => 'Image non trouvée',
            ], 404);
        }

        $width = $request->width ?? 150;
        $height = $request->height ?? 150;
        
        $thumbnailName = 'thumb_' . $width . 'x' . $height . '_' . $filename;
        $thumbnailPath = public_path('images/thumbnails/' . $thumbnailName);
        
        // Créer le dossier thumbnails s'il n'existe pas
        if (!File::exists(public_path('images/thumbnails'))) {
            File::makeDirectory(public_path('images/thumbnails'), 0755, true);
        }
        
        try {
            $image = Image::make($filePath);
            $image->fit($width, $height);
            $image->save($thumbnailPath, 85);
            
            return response()->json([
                'success' => true,
                'message' => 'Miniature créée avec succès',
                'thumbnail_url' => asset('images/thumbnails/' . $thumbnailName),
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de la création de la miniature: ' . $e->getMessage(),
            ], 500);
        }
    }
}
