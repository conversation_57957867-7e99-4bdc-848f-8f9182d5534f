@extends('layouts.app')

@section('content')
<div class="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <!-- Hero Section avec Slider Moderne -->
    @include('components.modern-slider')

    <!-- Ancien slider commenté pour référence -->
    <!--
    <div x-data="{ currentSlide: 0 }" class="relative -mt-32">
        <div class="relative h-screen overflow-hidden">
            <!-- Slides -->
            <div class="absolute inset-0 transition-transform duration-500"
                 :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
                <!-- Slide 1 -->
                <div class="absolute inset-0 w-full h-full bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900 relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 opacity-10">
                        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="paint" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="%23ffffff" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23paint)"/></svg>');"></div>
                    </div>

                    <!-- Floating Elements -->
                    <div class="absolute top-20 right-20 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
                    <div class="absolute bottom-32 left-20 w-24 h-24 bg-purple-400/20 rounded-full blur-lg animate-pulse delay-1000"></div>

                    <div class="container mx-auto px-6 h-full flex items-center relative z-10">
                        <div class="max-w-3xl text-white animate-fade-in">
                            <div class="mb-6">
                                <span class="inline-block bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full text-sm font-medium mb-4">
                                    🎨 {{ __('home.hero.badge') }}
                                </span>
                            </div>
                            <h1 class="text-6xl lg:text-7xl font-bold mb-6 leading-tight">
                                <span class="gradient-text">{{ __('home.hero.title') }}</span>
                            </h1>
                            <p class="text-xl lg:text-2xl mb-8 text-blue-100 leading-relaxed">{{ __('home.hero.subtitle') }}</p>
                            <div class="flex flex-wrap gap-4">
                                <a href="{{ route('products.index') }}"
                                   class="btn-primary hover-lift">
                                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    {{ __('home.hero.cta.products') }}
                                </a>
                                <a href="{{ route('color-simulator') }}"
                                   class="glass-effect text-white px-8 py-4 rounded-xl font-semibold hover:bg-white/30 transition-all hover-lift">
                                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h4a2 2 0 002-2V9a2 2 0 00-2-2H7a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                                    </svg>
                                    {{ __('home.hero.cta.simulator') }}
                                </a>
                                <a href="{{ route('promotions.index') }}"
                                   class="bg-gradient-to-r from-red-500 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-red-600 hover:to-pink-700 transition-all hover-lift shadow-lg">
                                    <svg class="w-5 h-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    {{ __('home.hero.cta.promotions') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Slide 2 -->
                <div class="absolute inset-0 w-full h-full bg-gradient-to-r from-green-900 to-green-600">
                    <div class="container mx-auto px-6 h-full flex items-center">
                        <div class="max-w-2xl text-white">
                            <h2 class="text-4xl font-bold mb-6">{{ __('home.solutions.title') }}</h2>
                            <p class="text-xl mb-8">{{ __('home.solutions.subtitle') }}</p>
                            <a href="{{ route('quotes.create') }}"
                               class="bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                                {{ __('home.solutions.cta') }}
                            </a>
                        </div>
                    </div>
                </div>
                <!-- Slide 3 - Galerie Produits ENAP -->
                @if(isset($products) && count($products) > 0)
                <div class="absolute inset-0 w-full h-full bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
                    <div class="container mx-auto px-6 h-full flex items-center">
                        <div class="w-full">
                            <div class="text-center mb-8">
                                <h2 class="text-4xl font-bold text-white mb-4">{{ __('home.products_gallery.title') }}</h2>
                                <p class="text-xl text-white/90">{{ __('home.products_gallery.subtitle') }}</p>
                            </div>

                            <!-- Galerie de produits dans le slider -->
                            <div id="heroProductsGrid" class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 max-w-5xl mx-auto">
                                @foreach(array_slice($products, 0, 12) as $index => $product)
                                <div class="group relative bg-white/10 backdrop-blur-sm rounded-xl overflow-hidden hover:bg-white/20 transition-all duration-300 cursor-pointer transform hover:scale-105"
                                     data-hero-product-index="{{ $index }}"
                                     onclick="openHomeProductModal('{{ $product['name'] }}', '{{ $product['image'] }}', '{{ $product['description'] }}')">
                                    <div class="aspect-square relative">
                                        @if($product['available'])
                                            <img src="{{ asset('images/' . $product['image']) }}"
                                                 alt="{{ $product['name'] }}"
                                                 class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                                 loading="lazy">
                                        @else
                                            <div class="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
                                                <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        @endif

                                        <!-- Overlay avec nom -->
                                        <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                            <div class="absolute bottom-0 left-0 right-0 p-2">
                                                <h4 class="text-white font-bold text-xs text-center">{{ $product['name'] }}</h4>
                                            </div>
                                        </div>

                                        <!-- Badge de disponibilité -->
                                        @if($product['available'])
                                            <div class="absolute top-1 right-1 bg-green-500 text-white text-xs px-1 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                                                ✓
                                            </div>
                                        @else
                                            <div class="absolute top-1 right-1 bg-orange-500 text-white text-xs px-1 py-0.5 rounded">
                                                ⏳
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                @endforeach
                            </div>

                            <!-- Bouton voir tous les produits -->
                            <div class="text-center mt-8">
                                <a href="{{ route('products.index') }}"
                                   class="inline-flex items-center bg-white text-purple-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors shadow-lg">
                                    {{ __('home.products_gallery.view_all') }}
                                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Contrôles du slider -->
            <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                <button @click="currentSlide = 0"
                        :class="{ 'bg-white': currentSlide === 0, 'bg-white/50': currentSlide !== 0 }"
                        class="w-3 h-3 rounded-full transition-colors"></button>
                <button @click="currentSlide = 1"
                        :class="{ 'bg-white': currentSlide === 1, 'bg-white/50': currentSlide !== 1 }"
                        class="w-3 h-3 rounded-full transition-colors"></button>
                @if(isset($products) && count($products) > 0)
                <button @click="currentSlide = 2"
                        :class="{ 'bg-white': currentSlide === 2, 'bg-white/50': currentSlide !== 2 }"
                        class="w-3 h-3 rounded-full transition-colors"></button>
                @endif
            </div>

            <!-- Navigation par flèches -->
            <button @click="currentSlide = currentSlide === 0 ? {{ isset($products) && count($products) > 0 ? '2' : '1' }} : currentSlide - 1"
                    class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <button @click="currentSlide = currentSlide === {{ isset($products) && count($products) > 0 ? '2' : '1' }} ? 0 : currentSlide + 1"
                    class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- Section Chiffres Clés -->
    <div class="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute top-0 left-0 w-full h-full">
            <div class="absolute top-10 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-pulse"></div>
            <div class="absolute bottom-10 right-10 w-32 h-32 bg-purple-200 rounded-full opacity-20 animate-pulse delay-1000"></div>
        </div>

        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold text-gray-900 mb-4">{{ __('home.key_figures.title') }}</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">{{ __('home.key_figures.subtitle') }}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center group">
                    <div class="glass-card p-8 hover-lift">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                            </svg>
                        </div>
                        <div class="text-5xl font-bold gradient-text mb-2 group-hover:scale-110 transition-transform">6</div>
                        <div class="text-gray-600 font-medium">{{ __('home.key_figures.production_units') }}</div>
                    </div>
                </div>

                <div class="text-center group">
                    <div class="glass-card p-8 hover-lift">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="text-5xl font-bold gradient-text mb-2 group-hover:scale-110 transition-transform">40+</div>
                        <div class="text-gray-600 font-medium">{{ __('home.key_figures.experience') }}</div>
                    </div>
                </div>

                <div class="text-center group">
                    <div class="glass-card p-8 hover-lift">
                        <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div class="text-5xl font-bold gradient-text mb-2 group-hover:scale-110 transition-transform">1000+</div>
                        <div class="text-gray-600 font-medium">{{ __('home.key_figures.products') }}</div>
                    </div>
                </div>

                <div class="text-center group">
                    <div class="glass-card p-8 hover-lift">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                        </div>
                        <div class="text-5xl font-bold gradient-text mb-2 group-hover:scale-110 transition-transform">5000+</div>
                        <div class="text-gray-600 font-medium">{{ __('home.key_figures.clients') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Gammes de Produits -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-4">{{ __('home.product_ranges.title') }}</h2>
            <p class="text-gray-600 text-center mb-12">{{ __('home.product_ranges.subtitle') }}</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($categories as $category)
                <div class="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
                    <div class="relative">
                        <img src="{{ $category->image_url }}" alt="{{ $category->getTranslation('name', app()->getLocale()) }}" 
                             class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                        <h3 class="absolute bottom-4 left-4 text-white text-xl font-semibold">
                            {{ $category->getTranslation('name', app()->getLocale()) }}
                        </h3>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">{{ $category->getTranslation('description', app()->getLocale()) }}</p>
                        <a href="{{ route('products.category', $category->id) }}" 
                           class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-800">
                            {{ __('home.product_ranges.cta') }}
                            <svg class="w-4 h-4 {{ app()->getLocale() === 'ar' ? 'mr-2 rotate-180' : 'ml-2' }}" 
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Section Services -->
    <div class="py-16 bg-gradient-to-br from-blue-900 to-blue-700 text-white">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl font-bold text-center mb-12">{{ __('home.services.title') }}</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center p-6 bg-white/10 rounded-lg backdrop-blur-sm">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">{{ __('home.services.color_simulator.title') }}</h3>
                    <p class="text-white/80">{{ __('home.services.color_simulator.description') }}</p>
                </div>

                <div class="text-center p-6 bg-white/10 rounded-lg backdrop-blur-sm">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">{{ __('home.services.quantity_calculator.title') }}</h3>
                    <p class="text-white/80">{{ __('home.services.quantity_calculator.description') }}</p>
                </div>

                <div class="text-center p-6 bg-white/10 rounded-lg backdrop-blur-sm">
                    <div class="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-2">{{ __('home.services.technical_support.title') }}</h3>
                    <p class="text-white/80">{{ __('home.services.technical_support.description') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Actualités -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <div class="flex justify-between items-center mb-12">
                <div>
                    <h2 class="text-3xl font-bold mb-4">{{ __('home.news.title') }}</h2>
                    <p class="text-gray-600">{{ __('home.news.subtitle') }}</p>
                </div>
                <a href="{{ route('blog.index') }}" 
                   class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-800">
                    {{ __('home.news.see_all') }}
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                </a>
            </div>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                @forelse($latestPosts as $post)
                <article class="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
                    <a href="{{ route('blog.show', $post->slug) }}" class="block">
                        <div class="relative h-48">
                            @if($post->featured_image)
                            <img src="{{ asset('storage/' . $post->featured_image) }}" 
                                 alt="{{ $post->title }}" 
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                            @else
                            <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                            @endif
                            @if($post->category)
                            <span class="absolute top-4 left-4 bg-blue-600 text-white text-sm px-3 py-1 rounded-full">
                                {{ $post->category->name }}
                            </span>
                            @endif
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600">
                                {{ $post->title }}
                            </h3>
                            <p class="text-gray-600 mb-4 line-clamp-3">{{ $post->excerpt }}</p>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-500">{{ $post->formatted_date }}</span>
                                <span class="text-blue-600 font-medium">{{ __('home.news.read_more') }}</span>
                            </div>
                        </div>
                    </a>
                </article>
                @empty
                <div class="col-span-3 text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
                    </svg>
                    <p class="text-gray-600 mb-4">{{ __('home.news.no_articles') }}</p>
                    <p class="text-gray-500">{{ __('home.news.come_back_soon') }}</p>
                </div>
                @endforelse
            </div>
        </div>
    </div>



    <!-- Section Localisateur d'Unités -->
    <div class="relative py-16 overflow-hidden">
        <div class="absolute inset-0">
            <img src="/images/map-bg.jpg" alt="Carte de l'Algérie" class="w-full h-full object-cover opacity-20">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-900/90 to-blue-600/90"></div>
        </div>
        
        <div class="relative container mx-auto px-6 text-white">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl font-bold mb-6">{{ __('home.find_unit.title') }}</h2>
                    <p class="text-lg text-white/80 mb-8">
                        {{ __('home.find_unit.description') }}
                    </p>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Oued-Smar et Cheraga (Alger)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Lakhdaria (Bouira)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Oran et Sig (Mascara)</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-6 h-6 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>Souk Ahras</span>
                        </li>
                    </ul>
                    <a href="{{ route('production-units') }}" 
                       class="inline-flex items-center bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        {{ __('home.find_unit.cta') }}
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                        </svg>
                    </a>
                </div>
                <div class="relative">
                    <div class="aspect-w-4 aspect-h-3 rounded-xl overflow-hidden shadow-2xl">
                        <div id="home-map" class="w-full h-full"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Calculateur de Peinture -->
    <div class="py-16">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <img src="/images/paint-calculator.jpg" alt="Calculateur de peinture" 
                         class="rounded-xl shadow-xl transform -rotate-2">
                </div>
                <div>
                    <span class="bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-1 rounded-full">{{ __('home.paint_calculator.free_tool') }}</span>
                    <h2 class="text-3xl font-bold mt-4 mb-6">{{ __('home.paint_calculator.title') }}</h2>
                    <p class="text-lg text-gray-600 mb-8">
                        {{ __('home.paint_calculator.description') }}
                    </p>
                    <ul class="space-y-4 mb-8">
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>{{ __('home.paint_calculator.benefits.precise_calculation') }}</span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>{{ __('home.paint_calculator.benefits.surface_type') }}</span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>{{ __('home.paint_calculator.benefits.recommendations') }}</span>
                        </li>
                        <li class="flex items-start">
                            <svg class="w-6 h-6 mr-3 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                            <span>{{ __('home.paint_calculator.benefits.budget_estimation') }}</span>
                        </li>
                    </ul>
                    <a href="{{ route('calculator.index') }}" 
                       class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                        {{ __('home.paint_calculator.cta') }}
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour affichage produit sur la page d'accueil -->
    <div id="homeProductModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- Header du modal -->
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="homeModalTitle">Produit ENAP</h3>
                    <button onclick="closeHomeProductModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Contenu du modal -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Image -->
                    <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                        <img id="homeModalImage" src="" alt="" class="w-full h-full object-cover">
                    </div>

                    <!-- Informations -->
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-xl font-semibold text-gray-900" id="homeModalProductName"></h4>
                            <p class="text-gray-600 mt-2" id="homeModalDescription"></p>
                        </div>

                        <div class="border-t pt-4">
                            <h5 class="font-medium text-gray-900 mb-2">{{ __('home.products_gallery.features') }}</h5>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• {{ __('home.products_gallery.high_quality') }}</li>
                                <li>• {{ __('home.products_gallery.easy_application') }}</li>
                                <li>• {{ __('home.products_gallery.durable_finish') }}</li>
                                <li>• {{ __('home.products_gallery.eco_friendly') }}</li>
                            </ul>
                        </div>

                        <div class="border-t pt-4">
                            <div class="flex space-x-3">
                                <a href="{{ route('products.index') }}" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-center">
                                    {{ __('home.products_gallery.view_products') }}
                                </a>
                                <a href="{{ route('quotes.create') }}" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors text-center">
                                    {{ __('home.products_gallery.request_quote') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Variables pour le carrousel de la page d'accueil
        let homeCarouselInterval;
        let homeIsPlaying = true;
        let homeCurrentIndex = 0;
        let homeCurrentProduct = null;

        // Fonctions du modal produit
        function openHomeProductModal(name, image, description) {
            homeCurrentProduct = { name, image, description };

            document.getElementById('homeModalTitle').textContent = name;
            document.getElementById('homeModalProductName').textContent = name;
            document.getElementById('homeModalDescription').textContent = description;
            document.getElementById('homeModalImage').src = '{{ asset("images/") }}/' + image;
            document.getElementById('homeModalImage').alt = name;

            document.getElementById('homeProductModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeHomeProductModal() {
            document.getElementById('homeProductModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            homeCurrentProduct = null;
        }

        // Fonctions du carrousel
        function createHomeCarousel() {
            const container = document.getElementById('homeProductsGrid');
            if (!container) return;

            const products = container.querySelectorAll('[data-product-index]');
            if (products.length === 0) return;

            const prevBtn = document.getElementById('homePrevBtn');
            const nextBtn = document.getElementById('homeNextBtn');
            const playPauseBtn = document.getElementById('homePlayPauseBtn');
            const playIcon = document.getElementById('homePlayIcon');
            const pauseIcon = document.getElementById('homePauseIcon');

            function highlightProduct(index) {
                products.forEach((product, i) => {
                    if (i === index) {
                        product.style.transform = 'scale(1.05) translateY(-8px)';
                        product.style.zIndex = '10';
                        product.style.boxShadow = '0 20px 40px rgba(59, 130, 246, 0.3)';
                        product.style.border = '3px solid #3b82f6';
                    } else {
                        product.style.transform = 'scale(1) translateY(0)';
                        product.style.zIndex = '1';
                        product.style.boxShadow = '';
                        product.style.border = '';
                    }
                });
            }

            function nextProduct() {
                homeCurrentIndex = (homeCurrentIndex + 1) % products.length;
                highlightProduct(homeCurrentIndex);
            }

            function prevProduct() {
                homeCurrentIndex = homeCurrentIndex === 0 ? products.length - 1 : homeCurrentIndex - 1;
                highlightProduct(homeCurrentIndex);
            }

            function startHomeCarousel() {
                if (homeCarouselInterval) clearInterval(homeCarouselInterval);
                homeCarouselInterval = setInterval(nextProduct, 4000);
                homeIsPlaying = true;
                playIcon.classList.add('hidden');
                pauseIcon.classList.remove('hidden');
            }

            function stopHomeCarousel() {
                if (homeCarouselInterval) clearInterval(homeCarouselInterval);
                homeIsPlaying = false;
                playIcon.classList.remove('hidden');
                pauseIcon.classList.add('hidden');
            }

            // Event listeners
            if (prevBtn) {
                prevBtn.addEventListener('click', () => {
                    prevProduct();
                    if (homeIsPlaying) {
                        stopHomeCarousel();
                        setTimeout(startHomeCarousel, 6000);
                    }
                });
            }

            if (nextBtn) {
                nextBtn.addEventListener('click', () => {
                    nextProduct();
                    if (homeIsPlaying) {
                        stopHomeCarousel();
                        setTimeout(startHomeCarousel, 6000);
                    }
                });
            }

            if (playPauseBtn) {
                playPauseBtn.addEventListener('click', () => {
                    if (homeIsPlaying) {
                        stopHomeCarousel();
                    } else {
                        startHomeCarousel();
                    }
                });
            }

            // Pause au survol
            container.addEventListener('mouseenter', () => {
                if (homeIsPlaying && homeCarouselInterval) {
                    clearInterval(homeCarouselInterval);
                }
            });

            container.addEventListener('mouseleave', () => {
                if (homeIsPlaying) {
                    startHomeCarousel();
                }
            });

            // Démarrer le carrousel
            highlightProduct(0);
            startHomeCarousel();
        }

        // Autplay du slider principal
        setInterval(() => {
            const slider = document.querySelector('[x-data]').__x.$data;
            const maxSlides = 3; // 3 slides : Accueil, Solutions, Produits
            slider.currentSlide = (slider.currentSlide + 1) % maxSlides;
        }, 6000);

        // Initialisation après chargement
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(createHomeCarousel, 1000);

            // Fermer le modal en cliquant à l'extérieur
            document.addEventListener('click', function(e) {
                const modal = document.getElementById('homeProductModal');
                if (e.target === modal) {
                    closeHomeProductModal();
                }
            });

            // Fermer le modal avec Escape
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('homeProductModal').classList.contains('hidden')) {
                    closeHomeProductModal();
                }
            });
        });
    </script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map
        const map = L.map('home-map').setView([36.7538, 3.0588], 6);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Add markers for ENAP units
        const units = [
            { name: 'ENAP Oued-Smar', lat: 36.7167, lng: 3.1667 },
            { name: 'ENAP Cheraga', lat: 36.7667, lng: 2.9500 },
            { name: 'ENAP Lakhdaria', lat: 36.5647, lng: 3.5933 },
            { name: 'ENAP Oran', lat: 35.6969, lng: -0.6331 },
            { name: 'ENAP Sig', lat: 35.5281, lng: -0.1886 },
            { name: 'ENAP Souk Ahras', lat: 36.2864, lng: 7.9511 }
        ];

        units.forEach(unit => {
            L.marker([unit.lat, unit.lng])
                .bindPopup(unit.name)
                .addTo(map);
        });
    });
    </script>
    @endpush

    @push('styles')
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
    .aspect-w-4 {
        position: relative;
        padding-bottom: 75%;
    }
    .aspect-w-4 > * {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
    }
    </style>
    @endpush
</div>
@endsection
