(function() {
    class ColorSimulator {
        constructor() {
            this.canvas = document.getElementById('simulator-canvas');
            this.ctx = this.canvas.getContext('2d');
            this.currentRoom = null;
            this.selectedColor = null;
            this.currentHarmony = null;
            this.paintedAreas = [];
            this.undoStack = [];
            this.redoStack = [];
            
            this.initialize();
        }

        initialize() {
            this.setupEventListeners();
            this.loadInitialRoom();
        }

        setupEventListeners() {
            // Room template selection
            document.querySelectorAll('.room-template').forEach(template => {
                template.addEventListener('click', (e) => this.changeRoom(e.target.closest('.room-template').dataset.roomId));
            });

            // Color selection
            document.querySelectorAll('.color-palette .color').forEach(color => {
                color.addEventListener('click', (e) => this.selectColor(e.target.dataset.color));
            });

            // Canvas painting
            this.canvas.addEventListener('mousedown', this.startPainting.bind(this));
            this.canvas.addEventListener('mousemove', this.paint.bind(this));
            this.canvas.addEventListener('mouseup', this.stopPainting.bind(this));
            this.canvas.addEventListener('mouseleave', this.stopPainting.bind(this));

            // Toolbar actions
            document.getElementById('undo-btn').addEventListener('click', () => this.undo());
            document.getElementById('redo-btn').addEventListener('click', () => this.redo());
            document.getElementById('save-btn').addEventListener('click', () => this.saveSimulation());
            document.getElementById('export-btn').addEventListener('click', () => this.exportSimulation());

            // Update buttons state when stack changes
            this.updateUndoRedoButtons();
        }

        async loadInitialRoom() {
            try {
                const response = await fetch('/api/rooms/default');
                const data = await response.json();
                await this.loadRoom(data.room);
            } catch (error) {
                console.error('Error loading initial room:', error);
                this.showNotification('error', 'Error loading initial room');
            }
        }

        async changeRoom(roomId) {
            try {
                const response = await fetch(`/api/rooms/${roomId}`);
                const data = await response.json();
                await this.loadRoom(data.room);

                // Update selected state in UI
                document.querySelectorAll('.room-template').forEach(el => {
                    el.classList.toggle('selected', el.dataset.roomId === roomId);
                });
            } catch (error) {
                console.error('Error changing room:', error);
                this.showNotification('error', 'Error loading room');
            }
        }

        async loadRoom(roomData) {
            return new Promise((resolve, reject) => {
                const image = new Image();
                image.onload = () => {
                    // Set canvas size to match image size while maintaining aspect ratio
                    const containerWidth = this.canvas.parentElement.clientWidth;
                    const containerHeight = this.canvas.parentElement.clientHeight;
                    const scale = Math.min(
                        containerWidth / image.width,
                        containerHeight / image.height
                    );

                    this.canvas.width = image.width * scale;
                    this.canvas.height = image.height * scale;
                    this.ctx.drawImage(image, 0, 0, this.canvas.width, this.canvas.height);
                    
                    this.currentRoom = roomData;
                    this.paintedAreas = [];
                    this.undoStack = [];
                    this.redoStack = [];
                    this.updateUndoRedoButtons();
                    resolve();
                };
                image.onerror = reject;
                image.src = roomData.image_url;
            });
        }

        selectColor(color) {
            this.selectedColor = color;
            
            // Update UI to show selected color
            document.querySelectorAll('.color-palette .color').forEach(el => {
                el.classList.toggle('selected', el.dataset.color === color);
            });

            // Load color harmonies
            this.loadColorHarmonies(color);

            // Load product suggestions
            this.loadProductSuggestions(color);
        }

        async loadColorHarmonies(color) {
            try {
                const response = await fetch(`/api/harmonies/${encodeURIComponent(color)}`);
                const data = await response.json();
                
                const container = document.querySelector('.harmonies-container');
                container.innerHTML = data.harmonies.map(harmony => `
                    <div class="harmony">
                        <h3>${harmony.name}</h3>
                        <div class="harmony-colors">
                            ${harmony.colors.map(c => `
                                <div class="harmony-color" style="background-color: ${c.hex}"></div>
                            `).join('')}
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Error loading harmonies:', error);
            }
        }

        async loadProductSuggestions(color) {
            try {
                const response = await fetch(`/api/products/suggestions/${encodeURIComponent(color)}`);
                const data = await response.json();
                
                const container = document.querySelector('.suggestions-container');
                container.innerHTML = data.products.map(product => `
                    <div class="product">
                        <img src="${product.images[0]?.url || '/images/default-product.jpg'}" alt="${product.translations[0]?.name || 'Product'}">
                        <div class="product-info">
                            <h4>${product.translations[0]?.name || 'Product'}</h4>
                            <p>${product.translations[0]?.description || ''}</p>
                        </div>
                    </div>
                `).join('');
            } catch (error) {
                console.error('Error loading product suggestions:', error);
            }
        }

        startPainting(e) {
            if (!this.selectedColor) {
                this.showNotification('error', 'Please select a color first');
                return;
            }
            
            const rect = this.canvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) * (this.canvas.width / rect.width);
            const y = (e.clientY - rect.top) * (this.canvas.height / rect.height);
            
            // Save current canvas state for undo
            this.undoStack.push(this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height));
            this.redoStack = [];
            this.updateUndoRedoButtons();
            
            this.isPainting = true;
            this.paint(e);
        }

        paint(e) {
            if (!this.isPainting) return;
            
            const rect = this.canvas.getBoundingClientRect();
            const x = (e.clientX - rect.left) * (this.canvas.width / rect.width);
            const y = (e.clientY - rect.top) * (this.canvas.height / rect.height);
            
            this.floodFill(Math.floor(x), Math.floor(y), this.selectedColor);
        }

        stopPainting() {
            this.isPainting = false;
        }

        floodFill(startX, startY, fillColor) {
            const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
            const pixels = imageData.data;
            
            const startPos = (startY * this.canvas.width + startX) * 4;
            const startR = pixels[startPos];
            const startG = pixels[startPos + 1];
            const startB = pixels[startPos + 2];
            
            const fillColorRGB = this.hexToRgb(fillColor);
            if (!fillColorRGB) return;
            
            const pixelsToCheck = [[startX, startY]];
            const checked = new Set();
            
            while (pixelsToCheck.length > 0) {
                const [x, y] = pixelsToCheck.pop();
                if (x < 0 || x >= this.canvas.width || y < 0 || y >= this.canvas.height) continue;
                
                const pos = (y * this.canvas.width + x) * 4;
                const key = `${x},${y}`;
                
                if (checked.has(key)) continue;
                checked.add(key);
                
                if (this.matchesColor(pixels, pos, startR, startG, startB)) {
                    pixels[pos] = fillColorRGB.r;
                    pixels[pos + 1] = fillColorRGB.g;
                    pixels[pos + 2] = fillColorRGB.b;
                    pixels[pos + 3] = 255; // Full opacity
                    
                    // Add adjacent pixels
                    pixelsToCheck.push([x - 1, y], [x + 1, y], [x, y - 1], [x, y + 1]);
                }
            }
            
            this.ctx.putImageData(imageData, 0, 0);
            
            // Save painted area for future reference
            this.paintedAreas.push({
                x: startX,
                y: startY,
                color: fillColor
            });
        }

        matchesColor(pixels, pos, r, g, b) {
            const tolerance = 32;
            return Math.abs(pixels[pos] - r) <= tolerance &&
                   Math.abs(pixels[pos + 1] - g) <= tolerance &&
                   Math.abs(pixels[pos + 2] - b) <= tolerance;
        }

        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }

        undo() {
            if (this.undoStack.length > 0) {
                const previousState = this.undoStack.pop();
                this.redoStack.push(this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height));
                this.ctx.putImageData(previousState, 0, 0);
                this.updateUndoRedoButtons();
            }
        }

        redo() {
            if (this.redoStack.length > 0) {
                const nextState = this.redoStack.pop();
                this.undoStack.push(this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height));
                this.ctx.putImageData(nextState, 0, 0);
                this.updateUndoRedoButtons();
            }
        }

        updateUndoRedoButtons() {
            document.getElementById('undo-btn').disabled = this.undoStack.length === 0;
            document.getElementById('redo-btn').disabled = this.redoStack.length === 0;
        }

        async saveSimulation() {
            if (!this.currentRoom || this.paintedAreas.length === 0) {
                this.showNotification('error', 'Nothing to save');
                return;
            }

            try {
                const simulationData = {
                    room_id: this.currentRoom.id,
                    image: this.canvas.toDataURL(),
                    painted_areas: this.paintedAreas,
                    color_harmony: this.currentHarmony
                };

                const response = await fetch('/api/simulations', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify(simulationData)
                });

                const result = await response.json();
                if (result.success) {
                    this.showNotification('success', 'Simulation saved successfully');
                } else {
                    throw new Error('Failed to save simulation');
                }
            } catch (error) {
                console.error('Error saving simulation:', error);
                this.showNotification('error', 'Error saving simulation');
            }
        }

        exportSimulation() {
            if (!this.currentRoom || this.paintedAreas.length === 0) {
                this.showNotification('error', 'Nothing to export');
                return;
            }

            const link = document.createElement('a');
            const timestamp = new Date().toISOString().replace(/[:\.]/g, '-');
            link.download = `room-simulation-${timestamp}.png`;
            link.href = this.canvas.toDataURL();
            link.click();
        }

        showNotification(type, message) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    }

    // Initialize the simulator when the DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
        window.colorSimulator = new ColorSimulator();
    });
})();
