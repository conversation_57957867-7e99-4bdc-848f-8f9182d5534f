@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">{{ __('dashboard.welcome') }}, {{ Auth::user()->name }}!</h1>
            <p class="mt-2 text-gray-600">{{ __('dashboard.overview') }}</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">{{ __('dashboard.projects') }}</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['projects'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">{{ __('dashboard.favorites') }}</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['favorites'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">{{ __('dashboard.quotes') }}</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['quotes'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">{{ __('dashboard.events') }}</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['events'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Projects -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">{{ __('dashboard.recent_projects') }}</h3>
                </div>
                <div class="p-6">
                    @if($recentProjects->count() > 0)
                        <div class="space-y-4">
                            @foreach($recentProjects as $project)
                                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ $project->name }}</h4>
                                        <p class="text-sm text-gray-500">{{ $project->type }}</p>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $project->status_badge }}">
                                            {{ __('projects.status.' . $project->status) }}
                                        </span>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-500">{{ $project->updated_at->diffForHumans() }}</p>
                                        @if($project->surface_area)
                                            <p class="text-sm text-gray-900">{{ $project->surface_area }} m²</p>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        <div class="mt-4">
                            <a href="{{ route('dashboard.projects') }}" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                                {{ __('dashboard.view_all_projects') }} →
                            </a>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">{{ __('dashboard.no_projects') }}</h3>
                            <p class="mt-1 text-sm text-gray-500">{{ __('dashboard.create_first_project') }}</p>
                            <div class="mt-6">
                                <a href="{{ route('dashboard.projects.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    {{ __('dashboard.new_project') }}
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Quotes -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">{{ __('dashboard.recent_quotes') }}</h3>
                </div>
                <div class="p-6">
                    @if($recentQuotes->count() > 0)
                        <div class="space-y-4">
                            @foreach($recentQuotes as $quote)
                                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                    <div>
                                        <h4 class="font-medium text-gray-900">{{ $quote->project_name }}</h4>
                                        <p class="text-sm text-gray-500">{{ $quote->project_type }}</p>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $quote->status_badge }}">
                                            {{ __('quotes.status.' . $quote->status) }}
                                        </span>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-500">{{ $quote->created_at->diffForHumans() }}</p>
                                        @if($quote->surface_area)
                                            <p class="text-sm text-gray-900">{{ $quote->surface_area }} m²</p>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        <div class="mt-4">
                            <a href="{{ route('quotes.index') }}" class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                                {{ __('dashboard.view_all_quotes') }} →
                            </a>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">{{ __('dashboard.no_quotes') }}</h3>
                            <p class="mt-1 text-sm text-gray-500">{{ __('dashboard.request_first_quote') }}</p>
                            <div class="mt-6">
                                <a href="{{ route('quotes.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    {{ __('dashboard.new_quote') }}
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ __('dashboard.quick_actions') }}</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <a href="{{ route('dashboard.projects.create') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="h-8 w-8 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span class="font-medium text-gray-900">{{ __('dashboard.new_project') }}</span>
                    </a>

                    <a href="{{ route('quotes.create') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="h-8 w-8 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="font-medium text-gray-900">{{ __('dashboard.request_quote') }}</span>
                    </a>

                    <a href="{{ route('color-chart.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="h-8 w-8 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                        </svg>
                        <span class="font-medium text-gray-900">{{ __('dashboard.explore_colors') }}</span>
                    </a>

                    <a href="{{ route('events.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="h-8 w-8 text-orange-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span class="font-medium text-gray-900">{{ __('dashboard.browse_events') }}</span>
                    </a>
                </div>

                <!-- Deuxième ligne d'actions -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
                    <a href="{{ route('dashboard.gallery.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="h-8 w-8 text-pink-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span class="font-medium text-gray-900">{{ __('dashboard.image_gallery') }}</span>
                    </a>

                    <a href="{{ route('technical-sheets.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="h-8 w-8 text-indigo-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span class="font-medium text-gray-900">{{ __('navigation.technical_sheets') }}</span>
                    </a>

                    <a href="{{ route('videos.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="h-8 w-8 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        <span class="font-medium text-gray-900">{{ __('navigation.applications') }}</span>
                    </a>

                    <a href="{{ route('calculator.index') }}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <svg class="h-8 w-8 text-teal-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        <span class="font-medium text-gray-900">{{ __('navigation.calculator') }}</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Galerie Produits ENAP -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ __('dashboard.enap_products_gallery') }}</h3>
                <p class="text-sm text-gray-600">{{ __('dashboard.discover_our_products') }}</p>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
                    @php
                    $products = [
                        ['name' => 'Acryphob', 'image' => 'acryphob.jpg', 'description' => 'Peinture acrylique hydrofuge'],
                        ['name' => 'Arris', 'image' => 'arris.jpg', 'description' => 'Peinture décorative texturée'],
                        ['name' => 'Blancryl', 'image' => 'blancryl.jpg', 'description' => 'Peinture acrylique blanche'],
                        ['name' => 'Dcosat', 'image' => 'dcosat.jpg', 'description' => 'Peinture satinée décorative'],
                        ['name' => 'Decoperle', 'image' => 'decoperle.jpg', 'description' => 'Peinture effet perlé'],
                        ['name' => 'Decopisine', 'image' => 'decopisine.jpg', 'description' => 'Peinture pour piscines'],
                        ['name' => 'Decosoie', 'image' => 'decosoie.jpg', 'description' => 'Peinture effet soie'],
                        ['name' => 'Katifa', 'image' => 'katifa.jpg', 'description' => 'Peinture veloutée'],
                        ['name' => 'Les Dunes', 'image' => 'lesdune.jpg', 'description' => 'Peinture effet sable'],
                        ['name' => 'Poudre', 'image' => 'ppoudre.jpg', 'description' => 'Peinture en poudre'],
                        ['name' => 'Ref', 'image' => 'ref.jpg', 'description' => 'Peinture de référence'],
                        ['name' => 'Tenysil', 'image' => 'tenysil.jpg', 'description' => 'Peinture silicone'],
                        ['name' => 'Terreal', 'image' => 'terreal.jpg', 'description' => 'Peinture terre cuite']
                    ];
                    @endphp

                    @foreach($products as $product)
                    <div class="group relative bg-gray-50 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer"
                         onclick="openProductModal('{{ $product['name'] }}', '{{ $product['image'] }}', '{{ $product['description'] }}')">
                        <div class="aspect-square">
                            <img src="{{ asset('images/' . $product['image']) }}"
                                 alt="{{ $product['name'] }}"
                                 class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                 loading="lazy">
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div class="absolute bottom-0 left-0 right-0 p-3">
                                <h4 class="text-white font-medium text-sm">{{ $product['name'] }}</h4>
                                <p class="text-white/80 text-xs mt-1">{{ $product['description'] }}</p>
                            </div>
                        </div>
                        <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <button class="bg-white/90 hover:bg-white text-gray-800 p-1.5 rounded-full shadow-lg">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="mt-6 text-center">
                    <a href="{{ route('products.index') }}" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                        {{ __('dashboard.view_all_products') }}
                        <svg class="ml-2 -mr-1 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour affichage produit -->
<div id="productModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Header du modal -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Produit ENAP</h3>
                <button onclick="closeProductModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Contenu du modal -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Image -->
                <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                    <img id="modalImage" src="" alt="" class="w-full h-full object-cover">
                </div>

                <!-- Informations -->
                <div class="space-y-4">
                    <div>
                        <h4 class="text-xl font-semibold text-gray-900" id="modalProductName"></h4>
                        <p class="text-gray-600 mt-2" id="modalDescription"></p>
                    </div>

                    <div class="border-t pt-4">
                        <h5 class="font-medium text-gray-900 mb-2">{{ __('dashboard.product_features') }}</h5>
                        <ul class="text-sm text-gray-600 space-y-1" id="modalFeatures">
                            <li>• {{ __('dashboard.high_quality_formula') }}</li>
                            <li>• {{ __('dashboard.easy_application') }}</li>
                            <li>• {{ __('dashboard.durable_finish') }}</li>
                            <li>• {{ __('dashboard.eco_friendly') }}</li>
                        </ul>
                    </div>

                    <div class="border-t pt-4">
                        <div class="flex space-x-3">
                            <button onclick="addToFavorites()" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                {{ __('dashboard.add_to_favorites') }}
                            </button>
                            <button onclick="requestQuote()" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                {{ __('dashboard.request_quote') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentProduct = null;

function openProductModal(name, image, description) {
    currentProduct = { name, image, description };

    document.getElementById('modalTitle').textContent = name;
    document.getElementById('modalProductName').textContent = name;
    document.getElementById('modalDescription').textContent = description;
    document.getElementById('modalImage').src = '{{ asset("images/") }}/' + image;
    document.getElementById('modalImage').alt = name;

    document.getElementById('productModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeProductModal() {
    document.getElementById('productModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    currentProduct = null;
}

function addToFavorites() {
    if (!currentProduct) return;

    // Simulation d'ajout aux favoris
    alert('{{ __("dashboard.product_added_to_favorites") }}: ' + currentProduct.name);

    // Ici vous pouvez ajouter l'appel AJAX pour ajouter aux favoris
    // fetch('/dashboard/favorites/toggle', { ... })
}

function requestQuote() {
    if (!currentProduct) return;

    // Redirection vers la page de demande de devis avec le produit pré-sélectionné
    window.location.href = '{{ route("quotes.create") }}?product=' + encodeURIComponent(currentProduct.name);
}

// Fermer le modal en cliquant à l'extérieur
document.getElementById('productModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeProductModal();
    }
});

// Fermer le modal avec la touche Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('productModal').classList.contains('hidden')) {
        closeProductModal();
    }
});
</script>

@endsection
