<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Slide;
use App\Models\SlideTranslation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class SlideController extends Controller
{
    /**
     * Afficher la liste des slides
     */
    public function index()
    {
        $slides = Slide::with('translations')
            ->ordered()
            ->paginate(20);

        return view('admin.slides.index', compact('slides'));
    }

    /**
     * Afficher le formulaire de création
     */
    public function create()
    {
        $locales = ['fr', 'ar']; // Langues supportées
        return view('admin.slides.create', compact('locales'));
    }

    /**
     * Enregistrer un nouveau slide
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // 5MB max
            'image_alt' => 'nullable|string|max:255',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|url|max:500',
            'button_target' => 'required|in:_self,_blank',
            'is_active' => 'boolean',
            'background_color' => 'nullable|string|max:7',
            'text_color' => 'required|string|max:7',
            'text_position' => 'required|in:left,center,right',
            'animation_type' => 'required|in:fade,slide,zoom',
            'duration' => 'required|integer|min:1000|max:10000',
            
            // Traductions
            'translations' => 'array',
            'translations.*.locale' => 'required|string|in:fr,ar',
            'translations.*.title' => 'required|string|max:255',
            'translations.*.description' => 'nullable|string',
            'translations.*.button_text' => 'nullable|string|max:100',
        ]);

        // Upload et traitement de l'image
        $imagePath = $this->handleImageUpload($request->file('image'));

        // Créer le slide
        $slide = Slide::create([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'image_path' => $imagePath,
            'image_alt' => $validated['image_alt'],
            'button_text' => $validated['button_text'],
            'button_url' => $validated['button_url'],
            'button_target' => $validated['button_target'],
            'is_active' => $request->boolean('is_active'),
            'background_color' => $validated['background_color'],
            'text_color' => $validated['text_color'],
            'text_position' => $validated['text_position'],
            'animation_type' => $validated['animation_type'],
            'duration' => $validated['duration'],
            'order' => Slide::getNextOrder(),
        ]);

        // Créer les traductions
        if (isset($validated['translations'])) {
            foreach ($validated['translations'] as $translation) {
                SlideTranslation::create([
                    'slide_id' => $slide->id,
                    'locale' => $translation['locale'],
                    'title' => $translation['title'],
                    'description' => $translation['description'] ?? null,
                    'button_text' => $translation['button_text'] ?? null,
                ]);
            }
        }

        return redirect()->route('admin.slides.index')
            ->with('success', 'Slide créé avec succès.');
    }

    /**
     * Afficher un slide
     */
    public function show(Slide $slide)
    {
        $slide->load('translations');
        return view('admin.slides.show', compact('slide'));
    }

    /**
     * Afficher le formulaire d'édition
     */
    public function edit(Slide $slide)
    {
        $slide->load('translations');
        $locales = ['fr', 'ar'];
        return view('admin.slides.edit', compact('slide', 'locales'));
    }

    /**
     * Mettre à jour un slide
     */
    public function update(Request $request, Slide $slide)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
            'image_alt' => 'nullable|string|max:255',
            'button_text' => 'nullable|string|max:100',
            'button_url' => 'nullable|url|max:500',
            'button_target' => 'required|in:_self,_blank',
            'is_active' => 'boolean',
            'background_color' => 'nullable|string|max:7',
            'text_color' => 'required|string|max:7',
            'text_position' => 'required|in:left,center,right',
            'animation_type' => 'required|in:fade,slide,zoom',
            'duration' => 'required|integer|min:1000|max:10000',
            
            // Traductions
            'translations' => 'array',
            'translations.*.locale' => 'required|string|in:fr,ar',
            'translations.*.title' => 'required|string|max:255',
            'translations.*.description' => 'nullable|string',
            'translations.*.button_text' => 'nullable|string|max:100',
        ]);

        // Traitement de l'image si une nouvelle est uploadée
        if ($request->hasFile('image')) {
            // Supprimer l'ancienne image
            if ($slide->image_path && !filter_var($slide->image_path, FILTER_VALIDATE_URL)) {
                Storage::delete($slide->image_path);
            }
            $validated['image_path'] = $this->handleImageUpload($request->file('image'));
        }

        // Mettre à jour le slide
        $slide->update([
            'title' => $validated['title'],
            'description' => $validated['description'],
            'image_path' => $validated['image_path'] ?? $slide->image_path,
            'image_alt' => $validated['image_alt'],
            'button_text' => $validated['button_text'],
            'button_url' => $validated['button_url'],
            'button_target' => $validated['button_target'],
            'is_active' => $request->boolean('is_active'),
            'background_color' => $validated['background_color'],
            'text_color' => $validated['text_color'],
            'text_position' => $validated['text_position'],
            'animation_type' => $validated['animation_type'],
            'duration' => $validated['duration'],
        ]);

        // Mettre à jour les traductions
        if (isset($validated['translations'])) {
            // Supprimer les anciennes traductions
            $slide->translations()->delete();
            
            // Créer les nouvelles traductions
            foreach ($validated['translations'] as $translation) {
                SlideTranslation::create([
                    'slide_id' => $slide->id,
                    'locale' => $translation['locale'],
                    'title' => $translation['title'],
                    'description' => $translation['description'] ?? null,
                    'button_text' => $translation['button_text'] ?? null,
                ]);
            }
        }

        return redirect()->route('admin.slides.index')
            ->with('success', 'Slide mis à jour avec succès.');
    }

    /**
     * Supprimer un slide
     */
    public function destroy(Slide $slide)
    {
        $slide->delete();
        
        return redirect()->route('admin.slides.index')
            ->with('success', 'Slide supprimé avec succès.');
    }

    /**
     * Réorganiser les slides
     */
    public function reorder(Request $request)
    {
        $request->validate([
            'slides' => 'required|array',
            'slides.*' => 'integer|exists:slides,id'
        ]);

        Slide::reorder($request->slides);

        return response()->json(['success' => true]);
    }

    /**
     * Basculer le statut actif/inactif
     */
    public function toggle(Slide $slide)
    {
        $slide->update(['is_active' => !$slide->is_active]);
        
        return response()->json([
            'success' => true,
            'is_active' => $slide->is_active
        ]);
    }

    /**
     * Dupliquer un slide
     */
    public function duplicate(Slide $slide)
    {
        $newSlide = $slide->duplicate();
        
        return redirect()->route('admin.slides.edit', $newSlide)
            ->with('success', 'Slide dupliqué avec succès.');
    }

    /**
     * Traiter l'upload et l'optimisation de l'image
     */
    private function handleImageUpload($file)
    {
        $filename = Str::random(40) . '.' . $file->getClientOriginalExtension();
        $path = 'slides/' . $filename;

        // Optimiser l'image
        $image = Image::make($file)
            ->resize(1920, 1080, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            })
            ->encode('jpg', 85);

        // Sauvegarder l'image
        Storage::put($path, $image);

        return $path;
    }
}
