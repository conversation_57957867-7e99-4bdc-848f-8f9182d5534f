<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\BlogCategory;
use App\Models\BlogPost;
use App\Models\BlogTag;
use App\Models\User;
use Illuminate\Support\Str;
use Carbon\Carbon;

class BlogSeeder extends Seeder
{
    public function run()
    {
        // Create sample categories
        $categories = [
            ['name' => 'Conseils & Astuces', 'slug' => 'conseils-astuces'],
            ['name' => 'Actualités', 'slug' => 'actualites'],
            ['name' => 'Guides Techniques', 'slug' => 'guides-techniques'],
            ['name' => 'Innovation', 'slug' => 'innovation']
        ];

        foreach ($categories as $category) {
            BlogCategory::create([
                'name' => $category['name'],
                'slug' => $category['slug'],
                'description' => 'Articles liés à ' . $category['name']
            ]);
        }

        // Create sample tags
        $tags = [
            ['name' => 'Peinture Extérieure', 'slug' => 'peinture-exterieure'],
            ['name' => 'Peinture Intérieure', 'slug' => 'peinture-interieure'],
            ['name' => 'Écologie', 'slug' => 'ecologie'],
            ['name' => 'Tendances', 'slug' => 'tendances'],
            ['name' => 'DIY', 'slug' => 'diy'],
            ['name' => 'Professionnel', 'slug' => 'professionnel']
        ];

        foreach ($tags as $tag) {
            BlogTag::create([
                'name' => $tag['name'],
                'slug' => $tag['slug']
            ]);
        }

        // Get the first user as author
        $author = User::first();

        // Create sample posts
        $posts = [
            [
                'title' => 'Comment bien choisir sa peinture extérieure',
                'slug' => 'comment-bien-choisir-sa-peinture-exterieure',
                'excerpt' => 'Découvrez les critères essentiels pour choisir la peinture extérieure adaptée à vos besoins.',
                'content' => "Les peintures extérieures doivent répondre à des exigences spécifiques pour assurer une protection optimale et durable...",
                'category_id' => 1,
                'tags' => [1, 6]
            ],
            [
                'title' => 'Les tendances couleurs 2025',
                'slug' => 'tendances-couleurs-2025',
                'excerpt' => 'Quelles sont les couleurs qui marqueront l\'année 2025 ? Notre équipe dévoile les tendances à venir.',
                'content' => "L'année 2025 sera marquée par un retour aux couleurs naturelles et apaisantes...",
                'category_id' => 2,
                'tags' => [4]
            ],
            [
                'title' => 'Guide complet : Application de la peinture époxy',
                'slug' => 'guide-complet-application-peinture-epoxy',
                'excerpt' => 'Tout ce que vous devez savoir sur l\'application professionnelle de la peinture époxy.',
                'content' => "La peinture époxy est une solution durable et résistante pour les sols industriels...",
                'category_id' => 3,
                'tags' => [6]
            ]
        ];

        foreach ($posts as $post) {
            $newPost = BlogPost::create([
                'title' => $post['title'],
                'slug' => $post['slug'],
                'excerpt' => $post['excerpt'],
                'content' => $post['content'],
                'category_id' => $post['category_id'],
                'author_id' => $author->id,
                'status' => 'published',
                'published_at' => Carbon::now(),
                'meta_title' => $post['title'] . ' | ENAP',
                'meta_description' => $post['excerpt']
            ]);

            $newPost->tags()->attach($post['tags']);
        }
    }
}
