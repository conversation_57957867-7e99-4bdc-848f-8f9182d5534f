<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\EventRegistration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class EventController extends Controller
{
    public function index(Request $request)
    {
        $type = $request->get('type', 'all');
        $location = $request->get('location', 'all');
        $month = $request->get('month', 'all');

        $query = Event::where('is_published', true)
            ->where('end_date', '>=', now());

        // Apply filters
        if ($type !== 'all') {
            $query->where('type', $type);
        }

        if ($location !== 'all') {
            $query->where('location', $location);
        }

        if ($month !== 'all') {
            $query->whereMonth('start_date', $month);
        }

        $events = $query->orderBy('start_date')->paginate(12);
        
        // Get filter options
        $types = Event::distinct('type')->pluck('type');
        $locations = Event::distinct('location')->pluck('location');
        
        // Get featured events
        $featuredEvents = Event::where('is_featured', true)
            ->where('is_published', true)
            ->where('end_date', '>=', now())
            ->orderBy('start_date')
            ->take(3)
            ->get();

        return view('events.index', compact('events', 'featuredEvents', 'types', 'locations', 'type', 'location', 'month'));
    }

    public function show(Event $event)
    {
        if (!$event->is_published) {
            abort(404);
        }

        $event->load(['speakers', 'agenda']);
        
        // Check if user is registered
        $isRegistered = false;
        if (Auth::check()) {
            $isRegistered = EventRegistration::where('event_id', $event->id)
                ->where('user_id', Auth::id())
                ->exists();
        }

        // Get related events
        $relatedEvents = Event::where('id', '!=', $event->id)
            ->where('type', $event->type)
            ->where('is_published', true)
            ->where('end_date', '>=', now())
            ->orderBy('start_date')
            ->take(3)
            ->get();

        // Increment view count
        $event->increment('view_count');

        return view('events.show', compact('event', 'isRegistered', 'relatedEvents'));
    }

    public function register(Request $request, Event $event)
    {
        if (!$event->is_published || $event->end_date < now()) {
            return response()->json(['error' => 'Event is not available for registration'], 400);
        }

        if ($event->max_participants && $event->registrations()->count() >= $event->max_participants) {
            return response()->json(['error' => 'Event is full'], 400);
        }

        $validated = $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'company' => 'nullable|string|max:255',
            'position' => 'nullable|string|max:255',
            'special_requirements' => 'nullable|string|max:1000'
        ]);

        // Check if already registered
        $existingRegistration = EventRegistration::where('event_id', $event->id)
            ->where('email', $validated['email'])
            ->first();

        if ($existingRegistration) {
            return response()->json(['error' => 'Already registered for this event'], 400);
        }

        // Create registration
        $registration = EventRegistration::create([
            'event_id' => $event->id,
            'user_id' => Auth::id(),
            'first_name' => $validated['first_name'],
            'last_name' => $validated['last_name'],
            'email' => $validated['email'],
            'phone' => $validated['phone'],
            'company' => $validated['company'],
            'position' => $validated['position'],
            'special_requirements' => $validated['special_requirements'],
            'status' => 'confirmed',
            'registration_number' => $this->generateRegistrationNumber()
        ]);

        // Send confirmation email
        try {
            Mail::to($registration->email)->send(new \App\Mail\EventRegistrationConfirmation($registration));
        } catch (\Exception $e) {
            \Log::error('Failed to send registration confirmation email: ' . $e->getMessage());
        }

        return response()->json([
            'success' => true,
            'message' => __('events.registration.success'),
            'registration_number' => $registration->registration_number
        ]);
    }

    public function cancel(Request $request, Event $event)
    {
        $validated = $request->validate([
            'registration_number' => 'required|string',
            'email' => 'required|email'
        ]);

        $registration = EventRegistration::where('event_id', $event->id)
            ->where('registration_number', $validated['registration_number'])
            ->where('email', $validated['email'])
            ->first();

        if (!$registration) {
            return response()->json(['error' => 'Registration not found'], 404);
        }

        if ($registration->status === 'cancelled') {
            return response()->json(['error' => 'Registration already cancelled'], 400);
        }

        // Check cancellation deadline
        if ($event->start_date->subDays(2) < now()) {
            return response()->json(['error' => 'Cancellation deadline has passed'], 400);
        }

        $registration->update(['status' => 'cancelled']);

        // Send cancellation confirmation email
        try {
            Mail::to($registration->email)->send(new \App\Mail\EventCancellationConfirmation($registration));
        } catch (\Exception $e) {
            \Log::error('Failed to send cancellation confirmation email: ' . $e->getMessage());
        }

        return response()->json([
            'success' => true,
            'message' => __('events.cancellation.success')
        ]);
    }

    public function calendar()
    {
        $events = Event::where('is_published', true)
            ->where('end_date', '>=', now())
            ->orderBy('start_date')
            ->get()
            ->map(function ($event) {
                return [
                    'id' => $event->id,
                    'title' => $event->title,
                    'start' => $event->start_date->format('Y-m-d H:i:s'),
                    'end' => $event->end_date->format('Y-m-d H:i:s'),
                    'url' => route('events.show', $event),
                    'color' => $this->getEventColor($event->type),
                    'location' => $event->location,
                    'type' => $event->type
                ];
            });

        return view('events.calendar', compact('events'));
    }

    public function icalendar(Event $event)
    {
        $ical = "BEGIN:VCALENDAR\r\n";
        $ical .= "VERSION:2.0\r\n";
        $ical .= "PRODID:-//ENAP//Events//EN\r\n";
        $ical .= "BEGIN:VEVENT\r\n";
        $ical .= "UID:" . $event->id . "@enap.dz\r\n";
        $ical .= "DTSTAMP:" . now()->format('Ymd\THis\Z') . "\r\n";
        $ical .= "DTSTART:" . $event->start_date->format('Ymd\THis\Z') . "\r\n";
        $ical .= "DTEND:" . $event->end_date->format('Ymd\THis\Z') . "\r\n";
        $ical .= "SUMMARY:" . $event->title . "\r\n";
        $ical .= "DESCRIPTION:" . strip_tags($event->description) . "\r\n";
        $ical .= "LOCATION:" . $event->location . "\r\n";
        $ical .= "URL:" . route('events.show', $event) . "\r\n";
        $ical .= "END:VEVENT\r\n";
        $ical .= "END:VCALENDAR\r\n";

        return response($ical)
            ->header('Content-Type', 'text/calendar')
            ->header('Content-Disposition', 'attachment; filename="' . $event->slug . '.ics"');
    }

    public function newsletter(Request $request)
    {
        $validated = $request->validate([
            'email' => 'required|email|unique:event_newsletter_subscriptions,email'
        ]);

        \DB::table('event_newsletter_subscriptions')->insert([
            'email' => $validated['email'],
            'subscribed_at' => now(),
            'is_active' => true
        ]);

        return response()->json([
            'success' => true,
            'message' => __('events.newsletter.subscribed')
        ]);
    }

    private function generateRegistrationNumber()
    {
        return 'REG-' . strtoupper(uniqid());
    }

    private function getEventColor($type)
    {
        $colors = [
            'formation' => '#3B82F6',
            'conference' => '#10B981',
            'workshop' => '#F59E0B',
            'webinar' => '#8B5CF6',
            'exhibition' => '#EF4444',
            'networking' => '#06B6D4'
        ];

        return $colors[$type] ?? '#6B7280';
    }
}
