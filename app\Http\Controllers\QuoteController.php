<?php

namespace App\Http\Controllers;

use App\Models\Quote;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use Barryvdh\DomPDF\Facade\Pdf;

class QuoteController extends Controller
{
    public function create(Request $request)
    {
        $products = Product::where('is_active', true)->get();
        $selectedProduct = null;
        
        if ($request->has('product_id')) {
            $selectedProduct = Product::find($request->product_id);
        }
        
        return view('quotes.create', compact('products', 'selectedProduct'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'client_name' => 'required|string|max:255',
            'client_email' => 'required|email',
            'client_phone' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'project_description' => 'required|string',
            'surface_type' => 'required|string',
            'surface_area' => 'required|numeric|min:0',
            'coats_number' => 'required|integer|min:1',
            'products' => 'required|array',
            'products.*.product_id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|numeric|min:0'
        ]);

        $quote = new Quote($validated);
        $quote->reference_number = Quote::generateReferenceNumber();
        $quote->status = 'pending';
        $quote->valid_until = now()->addDays(30);
          if (Auth::check()) {
            $quote->user_id = Auth::id();
        }

        $quote->save();

        // Calculer le montant total
        $quote->total_amount = $quote->calculateTotalAmount();
        $quote->save();

        return redirect()->route('quotes.show', $quote)
            ->with('success', 'Votre demande de devis a été envoyée avec succès.');
    }

    public function show(Quote $quote)
    {
        $quote->load('user');
        return view('quotes.show', compact('quote'));
    }

    public function download(Quote $quote)
    {
        // Générer un PDF du devis
        $pdf = Pdf::loadView('quotes.pdf', compact('quote'));
        
        $filename = Str::slug($quote->reference_number) . '.pdf';
        return $pdf->download($filename);
    }
}
