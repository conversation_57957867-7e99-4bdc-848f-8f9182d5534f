<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('technical_sheets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->json('title');
            $table->json('description')->nullable();
            $table->string('reference_number')->unique();
            $table->string('version');
            $table->date('publication_date');
            $table->string('pdf_url');
            $table->string('model_3d_url')->nullable();
            $table->json('technical_specs');
            $table->json('application_steps')->nullable();
            $table->json('safety_instructions')->nullable();
            $table->json('storage_conditions')->nullable();
            $table->boolean('is_published')->default(true);
            $table->integer('downloads_count')->default(0);
            $table->timestamps();
            
            $table->index('reference_number');
            $table->index('publication_date');
        });

        Schema::create('technical_sheet_categories', function (Blueprint $table) {
            $table->id();
            $table->json('name');
            $table->string('slug')->unique();
            $table->json('description')->nullable();
            $table->timestamps();
        });

        Schema::table('technical_sheets', function (Blueprint $table) {
            $table->foreignId('category_id')
                  ->after('product_id')
                  ->constrained('technical_sheet_categories')
                  ->onDelete('restrict');
        });
    }

    public function down()
    {
        Schema::dropIfExists('technical_sheets');
        Schema::dropIfExists('technical_sheet_categories');
    }
};
