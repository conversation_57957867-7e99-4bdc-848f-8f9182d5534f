<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\RoomTemplate;
use App\Models\ColorHarmony;

class ColorSimulatorSeeder extends Seeder
{
    public function run()
    {
        // Create room templates
        $rooms = [
            [
                'fr' => [
                    'name' => 'Salon moderne',
                    'description' => 'Un salon contemporain avec de grandes fenêtres'
                ],
                'ar' => [
                    'name' => 'غرفة معيشة عصرية',
                    'description' => 'غرفة معيشة معاصرة مع نوافذ كبيرة'
                ],
                'image_path' => 'rooms/living-room-modern.jpg',
                'room_type' => 'living_room',
                'mask_areas' => [
                    ['name' => 'walls', 'points' => [[0,0], [100,0], [100,100], [0,100]]],
                    ['name' => 'accent_wall', 'points' => [[80,0], [100,0], [100,100], [80,100]]]
                ]
            ],
            [
                'fr' => [
                    'name' => 'Chambre principale',
                    'description' => 'Une chambre spacieuse avec un grand lit'
                ],
                'ar' => [
                    'name' => 'غرفة نوم رئيسية',
                    'description' => 'غرفة نوم واسعة مع سرير كبير'
                ],
                'image_path' => 'rooms/bedroom-master.jpg',
                'room_type' => 'bedroom',
                'mask_areas' => [
                    ['name' => 'walls', 'points' => [[0,0], [100,0], [100,100], [0,100]]],
                    ['name' => 'feature_wall', 'points' => [[0,0], [50,0], [50,100], [0,100]]]
                ]
            ],
            [
                'fr' => [
                    'name' => 'Cuisine ouverte',
                    'description' => 'Une cuisine moderne avec îlot central'
                ],
                'ar' => [
                    'name' => 'مطبخ مفتوح',
                    'description' => 'مطبخ حديث مع جزيرة مركزية'
                ],
                'image_path' => 'rooms/kitchen-open.jpg',
                'room_type' => 'kitchen',
                'mask_areas' => [
                    ['name' => 'walls', 'points' => [[0,0], [100,0], [100,80], [0,80]]],
                    ['name' => 'backsplash', 'points' => [[0,40], [100,40], [100,60], [0,60]]]
                ]
            ]
        ];

        foreach ($rooms as $room) {
            RoomTemplate::create([
                'name' => $room['fr']['name'],
                'image_path' => $room['image_path'],
                'room_type' => $room['room_type'],
                'mask_areas' => $room['mask_areas'],
                'is_active' => true
            ])->setTranslations([
                'fr' => [
                    'name' => $room['fr']['name'],
                    'description' => $room['fr']['description']
                ],
                'ar' => [
                    'name' => $room['ar']['name'],
                    'description' => $room['ar']['description']
                ]
            ]);
        }

        // Create color harmonies
        $harmonies = [
            [
                'fr' => [
                    'name' => 'Harmonie Naturelle',
                    'description' => 'Une combinaison apaisante de verts et de bruns'
                ],
                'ar' => [
                    'name' => 'تناسق طبيعي',
                    'description' => 'مزيج هادئ من الأخضر والبني'
                ],
                'type' => 'analogous',
                'colors' => [1, 2, 3], // IDs from color_palettes table
                'is_featured' => true
            ],
            [
                'fr' => [
                    'name' => 'Contraste Moderne',
                    'description' => 'Un contraste saisissant de couleurs complémentaires'
                ],
                'ar' => [
                    'name' => 'تباين عصري',
                    'description' => 'تباين لافت للألوان المتكاملة'
                ],
                'type' => 'complementary',
                'colors' => [4, 5], // IDs from color_palettes table
                'is_featured' => true
            ],
            [
                'fr' => [
                    'name' => 'Tons Neutres',
                    'description' => 'Une palette élégante de gris et de beiges'
                ],
                'ar' => [
                    'name' => 'درجات محايدة',
                    'description' => 'لوحة أنيقة من الرمادي والبيج'
                ],
                'type' => 'monochromatic',
                'colors' => [6, 7, 8], // IDs from color_palettes table
                'is_featured' => true
            ]
        ];

        foreach ($harmonies as $harmony) {
            ColorHarmony::create([
                'name' => $harmony['fr']['name'],
                'type' => $harmony['type'],
                'colors' => $harmony['colors'],
                'is_featured' => $harmony['is_featured']
            ])->setTranslations([
                'fr' => [
                    'name' => $harmony['fr']['name'],
                    'description' => $harmony['fr']['description']
                ],
                'ar' => [
                    'name' => $harmony['ar']['name'],
                    'description' => $harmony['ar']['description']
                ]
            ]);
        }
    }
}
