<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Translatable\HasTranslations;

class Video extends Model
{
    use HasFactory, HasTranslations;

    protected $fillable = [
        'title',
        'description',
        'thumbnail_url',
        'video_url',
        'category',
        'difficulty_level',
        'duration',
        'views',
        'product_ids',
        'tools_needed',
        'safety_notes',
        'application_steps'
    ];

    protected $translatable = [
        'title',
        'description',
        'tools_needed',
        'safety_notes',
        'application_steps'
    ];

    protected $casts = [
        'product_ids' => 'array',
        'tools_needed' => 'array',
        'safety_notes' => 'array',
        'application_steps' => 'array'
    ];

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }
}
