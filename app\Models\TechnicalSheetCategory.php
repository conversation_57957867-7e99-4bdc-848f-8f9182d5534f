<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Translatable\HasTranslations;

class TechnicalSheetCategory extends Model
{
    use HasFactory, HasTranslations;

    protected $fillable = [
        'name',
        'slug',
        'description'
    ];

    protected $translatable = [
        'name',
        'description'
    ];

    public function technicalSheets()
    {
        return $this->hasMany(TechnicalSheet::class, 'category_id');
    }
}
