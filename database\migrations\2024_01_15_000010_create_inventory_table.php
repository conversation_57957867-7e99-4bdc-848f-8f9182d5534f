<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('inventory', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('location_id')->constrained('production_units')->onDelete('cascade');
            $table->integer('quantity_available')->default(0);
            $table->integer('quantity_reserved')->default(0); // Reserved for orders
            $table->integer('minimum_stock')->default(0); // Alert threshold
            $table->integer('maximum_stock')->default(0); // Maximum capacity
            $table->decimal('unit_cost', 10, 2)->nullable(); // Cost price
            $table->string('batch_number')->nullable();
            $table->date('expiry_date')->nullable();
            $table->enum('status', ['active', 'discontinued', 'out_of_stock'])->default('active');
            $table->timestamps();

            $table->unique(['product_id', 'location_id', 'batch_number']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('inventory');
    }
};
