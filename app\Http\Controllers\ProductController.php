<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

class ProductController extends Controller
{
    public function index(Request $request)
    {
        $query = Product::query()->with(['category', 'colors']);

        // Filtrage par catégorie
        if ($request->has('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Filtrage par surface
        if ($request->has('surface_type')) {
            $query->where('surface_type', $request->surface_type);
        }

        // Recherche par nom
        if ($request->has('search')) {
            $query->where('name', 'LIKE', '%' . $request->search . '%')
                  ->orWhere('description', 'LIKE', '%' . $request->search . '%');
        }

        $products = $query->paginate(12);
        $categories = ProductCategory::all();

        return view('products.index', compact('products', 'categories'));
    }

    public function show(Product $product)
    {
        $product->load(['category', 'colors']);
        $relatedProducts = Product::where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->limit(4)
            ->get();

        return view('products.show', compact('product', 'relatedProducts'));
    }

    public function calculateQuantity(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'surface_area' => 'required|numeric|min:0',
            'coats_number' => 'required|integer|min:1'
        ]);

        $product = Product::findOrFail($request->product_id);
        $quantity = $product->calculateQuantityNeeded(
            $request->surface_area,
            $request->coats_number
        );

        return response()->json([
            'quantity' => $quantity,
            'unit' => $product->unit,
            'coverage_rate' => $product->coverage_rate
        ]);
    }
}
