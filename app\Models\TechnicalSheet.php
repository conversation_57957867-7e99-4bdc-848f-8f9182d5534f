<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Translatable\HasTranslations;

class TechnicalSheet extends Model
{
    use HasFactory, HasTranslations;

    protected $fillable = [
        'product_id',
        'category_id',
        'title',
        'description',
        'reference_number',
        'version',
        'publication_date',
        'pdf_url',
        'model_3d_url',
        'technical_specs',
        'application_steps',
        'safety_instructions',
        'storage_conditions',
        'is_published',
        'downloads_count'
    ];

    protected $translatable = [
        'title',
        'description',
        'technical_specs',
        'application_steps',
        'safety_instructions',
        'storage_conditions'
    ];

    protected $casts = [
        'publication_date' => 'date',
        'technical_specs' => 'array',
        'application_steps' => 'array',
        'safety_instructions' => 'array',
        'storage_conditions' => 'array',
        'is_published' => 'boolean',
        'downloads_count' => 'integer'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function category()
    {
        return $this->belongsTo(TechnicalSheetCategory::class);
    }

    public function incrementDownloads()
    {
        return $this->increment('downloads_count');
    }

    public function getSpecificationsByGroup($group)
    {
        $specs = $this->technical_specs;
        return $specs[$group] ?? [];
    }

    public function hasModel3D()
    {
        return !is_null($this->model_3d_url);
    }
}
