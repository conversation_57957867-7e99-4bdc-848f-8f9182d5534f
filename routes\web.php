<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductionUnitController;
use App\Http\Controllers\QuoteController;
use App\Http\Controllers\ColorSimulatorController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\UnitLocatorController;
use App\Http\Controllers\PaintCalculatorController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\VideoController;
use App\Http\Controllers\TechnicalSheetController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\UserProjectController;
use App\Http\Controllers\FavoriteController;
use App\Http\Controllers\SupportChatController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\RecommendationController;

// Route d'accueil
Route::get('/', function () {
    $categories = \App\Models\ProductCategory::with('products')->get();
    $latestPosts = \App\Models\BlogPost::query()
        ->when(true, function ($query) {
            return $query->with(['category', 'author'])
                ->where('status', 'published')
                ->where('published_at', '<=', now())
                ->latest('published_at')
                ->take(3);
        })
        ->get();
    return view('home', compact('categories', 'latestPosts'));
})->name('home');

// Routes des produits
Route::prefix('products')->group(function () {
    Route::get('/', [ProductController::class, 'index'])->name('products.index');
    Route::get('/category/{category}', [ProductController::class, 'category'])->name('products.category');
    Route::get('/{product}', [ProductController::class, 'show'])->name('products.show');
});

// Routes des unités de production
Route::get('/production-units', [UnitLocatorController::class, 'index'])->name('production-units');

// Routes des devis
Route::middleware(['auth'])->group(function () {
    Route::prefix('quotes')->name('quotes.')->group(function () {
        Route::get('/', [QuoteController::class, 'index'])->name('index');
        Route::get('/create', [QuoteController::class, 'create'])->name('create');
        Route::post('/', [QuoteController::class, 'store'])->name('store');
        Route::get('/{quote}', [QuoteController::class, 'show'])->name('show');
        Route::get('/{quote}/download', [QuoteController::class, 'download'])->name('download');
        Route::post('/calculate-estimate', [QuoteController::class, 'calculateEstimate'])->name('calculate-estimate');
    });
});

// Routes du simulateur de couleurs
Route::prefix('color-simulator')->group(function () {
    Route::get('/', [ColorSimulatorController::class, 'index'])->name('color-simulator');
    Route::post('/simulate', [ColorSimulatorController::class, 'simulate'])->name('color-simulator.simulate');
    Route::get('/harmonies/{id}', [ColorSimulatorController::class, 'getHarmonies'])->name('color-simulator.harmonies');
    Route::get('/rooms', [ColorSimulatorController::class, 'getRooms'])->name('color-simulator.rooms');
});

// Color Simulator API Routes
Route::prefix('api')->group(function () {
    Route::get('/rooms/default', [ColorSimulatorController::class, 'getDefaultRoom']);
    Route::get('/rooms/{id}', [ColorSimulatorController::class, 'getRoom']);
    Route::post('/simulations', [ColorSimulatorController::class, 'saveSimulation']);
    Route::get('/harmonies/{colorHex}', [ColorSimulatorController::class, 'getColorHarmonies']);
    Route::get('/products/suggestions/{colorHex}', [ColorSimulatorController::class, 'getProductSuggestions']);
});

// Routes du panier
Route::prefix('cart')->group(function () {
    Route::get('/', [CartController::class, 'index'])->name('cart.index');
    Route::post('/add', [CartController::class, 'add'])->name('cart.add');
    Route::post('/update', [CartController::class, 'update'])->name('cart.update');
    Route::post('/remove', [CartController::class, 'remove'])->name('cart.remove');
    Route::post('/clear', [CartController::class, 'clear'])->name('cart.clear');
});

// Routes du checkout
Route::middleware(['auth'])->group(function () {
    Route::get('/checkout', [CheckoutController::class, 'index'])->name('checkout.index');
    Route::post('/checkout', [CheckoutController::class, 'store'])->name('checkout.store');
    Route::get('/checkout/confirmation/{order}', [CheckoutController::class, 'confirmation'])
        ->name('checkout.confirmation');
});

// Routes du localisateur d'unités
Route::prefix('units')->group(function () {
    Route::get('/', [UnitLocatorController::class, 'index'])->name('units.index');
    Route::get('/nearest', [UnitLocatorController::class, 'findNearest'])->name('units.nearest');
    Route::get('/{unit}', [UnitLocatorController::class, 'show'])->name('units.show');
    Route::get('/{unit}/opening-hours', [UnitLocatorController::class, 'openingHours'])->name('units.hours');
});

// Routes du calculateur de peinture
Route::prefix('calculator')->group(function () {
    Route::get('/', [PaintCalculatorController::class, 'index'])->name('calculator.index');
    Route::post('/calculate', [PaintCalculatorController::class, 'calculate'])->name('calculator.calculate');
});

// Routes du blog
Route::prefix('blog')->group(function () {
    Route::get('/', [BlogController::class, 'index'])->name('blog.index');
    Route::get('/category/{slug}', [BlogController::class, 'category'])->name('blog.category');
    Route::get('/tag/{tag}', [BlogController::class, 'tag'])->name('blog.tag');
    Route::get('/{slug}', [BlogController::class, 'show'])->name('blog.show');
});

// Page Notre Entreprise
Route::get('/about', function () {
    return view('about.index');
})->name('about');

// Routes Implantations
Route::prefix('locations')->name('locations.')->group(function () {
    Route::get('/', [\App\Http\Controllers\LocationController::class, 'index'])->name('index');
    Route::get('/api', [\App\Http\Controllers\LocationController::class, 'api'])->name('api');
    Route::get('/{slug}', [\App\Http\Controllers\LocationController::class, 'show'])->name('show');
});

// Page Documentation
Route::get('/documentation', function () {
    return view('documentation.index');
})->name('documentation');

// Routes Promotions
Route::prefix('promotions')->name('promotions.')->group(function () {
    Route::get('/', [\App\Http\Controllers\PromotionController::class, 'index'])->name('index');
    Route::get('/{promotion}', [\App\Http\Controllers\PromotionController::class, 'show'])->name('show');
    Route::post('/{promotion}/apply', [\App\Http\Controllers\PromotionController::class, 'apply'])->name('apply');
    Route::post('/newsletter', [\App\Http\Controllers\PromotionController::class, 'newsletter'])->name('newsletter');
});

// Routes de Recherche
Route::get('/search', [\App\Http\Controllers\SearchController::class, 'index'])->name('search.index');
Route::get('/api/search/suggestions', [\App\Http\Controllers\SearchController::class, 'suggestions'])->name('search.suggestions');

// Routes Événements et Formations
Route::prefix('events')->name('events.')->group(function () {
    Route::get('/', [\App\Http\Controllers\EventController::class, 'index'])->name('index');
    Route::get('/calendar', [\App\Http\Controllers\EventController::class, 'calendar'])->name('calendar');
    Route::get('/{event}', [\App\Http\Controllers\EventController::class, 'show'])->name('show');
    Route::post('/{event}/register', [\App\Http\Controllers\EventController::class, 'register'])->name('register');
    Route::post('/{event}/cancel', [\App\Http\Controllers\EventController::class, 'cancel'])->name('cancel');
    Route::get('/{event}/icalendar', [\App\Http\Controllers\EventController::class, 'icalendar'])->name('icalendar');
    Route::post('/newsletter', [\App\Http\Controllers\EventController::class, 'newsletter'])->name('newsletter');
});

// Routes Nuanciers Avancés
Route::prefix('color-chart')->name('color-chart.')->group(function () {
    Route::get('/', [\App\Http\Controllers\ColorChartController::class, 'index'])->name('index');
    Route::get('/{color}', [\App\Http\Controllers\ColorChartController::class, 'show'])->name('show');
    Route::get('/api/harmonies', [\App\Http\Controllers\ColorChartController::class, 'harmonies'])->name('harmonies');
    Route::post('/api/compare', [\App\Http\Controllers\ColorChartController::class, 'compare'])->name('compare');
    Route::post('/api/export', [\App\Http\Controllers\ColorChartController::class, 'export'])->name('export');
    Route::get('/api/trending', [\App\Http\Controllers\ColorChartController::class, 'trending'])->name('trending');
    Route::get('/api/search', [\App\Http\Controllers\ColorChartController::class, 'search'])->name('search');
});

// Routes d'administration
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    // Administration du blog
    Route::prefix('blog')->name('blog.')->group(function () {
        // Articles
        Route::resource('posts', \App\Http\Controllers\Admin\BlogController::class);
        
        // Catégories
        Route::resource('categories', \App\Http\Controllers\Admin\BlogCategoryController::class);
        
        // Tags
        Route::resource('tags', \App\Http\Controllers\Admin\BlogTagController::class);
    });
});

// Language Switch Route
Route::get('/language/{locale}', [LanguageController::class, 'switch'])->name('language.switch');

// Video Library Routes
Route::get('/videos', [VideoController::class, 'index'])->name('videos.index');
Route::get('/videos/{video}', [VideoController::class, 'show'])->name('videos.show');

// Technical Sheets Routes
Route::prefix('technical-sheets')->name('technical-sheets.')->group(function () {
    Route::get('/', [TechnicalSheetController::class, 'index'])->name('index');
    Route::get('/{technicalSheet}', [TechnicalSheetController::class, 'show'])->name('show');
    Route::get('/{technicalSheet}/download', [TechnicalSheetController::class, 'download'])->name('download');
    Route::get('/{technicalSheet}/3d', [TechnicalSheetController::class, 'view3d'])->name('view3d');
    Route::post('/{technicalSheet}/email', [TechnicalSheetController::class, 'email'])->name('email');
    Route::get('/{technicalSheet}/qr-code', [TechnicalSheetController::class, 'qrCode'])->name('qr-code');
});

// Dashboard Routes (requires authentication)
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');
    Route::get('/dashboard/profile', [DashboardController::class, 'profile'])->name('dashboard.profile');
    Route::put('/dashboard/profile', [DashboardController::class, 'updateProfile'])->name('dashboard.profile.update');

    // User Projects
    Route::prefix('dashboard/projects')->name('dashboard.projects.')->group(function () {
        Route::get('/', [UserProjectController::class, 'index'])->name('index');
        Route::get('/create', [UserProjectController::class, 'create'])->name('create');
        Route::post('/', [UserProjectController::class, 'store'])->name('store');
        Route::get('/{project}', [UserProjectController::class, 'show'])->name('show');
        Route::get('/{project}/edit', [UserProjectController::class, 'edit'])->name('edit');
        Route::put('/{project}', [UserProjectController::class, 'update'])->name('update');
        Route::delete('/{project}', [UserProjectController::class, 'destroy'])->name('destroy');
        Route::delete('/{project}/images/{index}', [UserProjectController::class, 'removeImage'])->name('remove-image');
        Route::post('/calculate-cost', [UserProjectController::class, 'calculateCost'])->name('calculate-cost');
    });

    // User Favorites
    Route::prefix('dashboard/favorites')->name('dashboard.favorites.')->group(function () {
        Route::get('/', [FavoriteController::class, 'index'])->name('index');
        Route::post('/toggle', [FavoriteController::class, 'toggle'])->name('toggle');
        Route::delete('/{favorite}', [FavoriteController::class, 'destroy'])->name('destroy');
    });

    // Support Chat
    Route::prefix('support')->name('support.')->group(function () {
        Route::get('/', [SupportChatController::class, 'index'])->name('index');
        Route::get('/create', [SupportChatController::class, 'create'])->name('create');
        Route::post('/', [SupportChatController::class, 'store'])->name('store');
        Route::get('/{chat}', [SupportChatController::class, 'show'])->name('show');
        Route::post('/{chat}/messages', [SupportChatController::class, 'sendMessage'])->name('send-message');
    });

    // Notifications
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [NotificationController::class, 'index'])->name('index');
        Route::post('/{notification}/read', [NotificationController::class, 'markAsRead'])->name('read');
        Route::post('/read-all', [NotificationController::class, 'markAllAsRead'])->name('read-all');
    });

    // Recommendations
    Route::get('/recommendations', [RecommendationController::class, 'index'])->name('recommendations.index');
});
