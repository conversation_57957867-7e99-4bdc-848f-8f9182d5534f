<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductionUnitController;
use App\Http\Controllers\QuoteController;
use App\Http\Controllers\ColorSimulatorController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\UnitLocatorController;
use App\Http\Controllers\PaintCalculatorController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\VideoController;
use App\Http\Controllers\TechnicalSheetController;

// Route d'accueil
Route::get('/', function () {
    $categories = \App\Models\ProductCategory::with('products')->get();
    $latestPosts = \App\Models\BlogPost::query()
        ->when(true, function ($query) {
            return $query->with(['category', 'author'])
                ->where('status', 'published')
                ->where('published_at', '<=', now())
                ->latest('published_at')
                ->take(3);
        })
        ->get();
    return view('home', compact('categories', 'latestPosts'));
})->name('home');

// Routes des produits
Route::prefix('products')->group(function () {
    Route::get('/', [ProductController::class, 'index'])->name('products.index');
    Route::get('/category/{category}', [ProductController::class, 'category'])->name('products.category');
    Route::get('/{product}', [ProductController::class, 'show'])->name('products.show');
});

// Routes des unités de production
Route::get('/production-units', [UnitLocatorController::class, 'index'])->name('production-units');

// Routes des devis
Route::middleware(['auth'])->group(function () {
    Route::prefix('quotes')->name('quotes.')->group(function () {
        Route::get('/', [QuoteController::class, 'index'])->name('index');
        Route::get('/create', [QuoteController::class, 'create'])->name('create');
        Route::post('/', [QuoteController::class, 'store'])->name('store');
        Route::get('/{quote}', [QuoteController::class, 'show'])->name('show');
        Route::get('/{quote}/download', [QuoteController::class, 'download'])->name('download');
        Route::post('/calculate-estimate', [QuoteController::class, 'calculateEstimate'])->name('calculate-estimate');
    });
});

// Routes du simulateur de couleurs
Route::prefix('color-simulator')->group(function () {
    Route::get('/', [ColorSimulatorController::class, 'index'])->name('color-simulator');
    Route::post('/simulate', [ColorSimulatorController::class, 'simulate'])->name('color-simulator.simulate');
    Route::get('/harmonies/{id}', [ColorSimulatorController::class, 'getHarmonies'])->name('color-simulator.harmonies');
    Route::get('/rooms', [ColorSimulatorController::class, 'getRooms'])->name('color-simulator.rooms');
});

// Color Simulator API Routes
Route::prefix('api')->group(function () {
    Route::get('/rooms/default', [ColorSimulatorController::class, 'getDefaultRoom']);
    Route::get('/rooms/{id}', [ColorSimulatorController::class, 'getRoom']);
    Route::post('/simulations', [ColorSimulatorController::class, 'saveSimulation']);
    Route::get('/harmonies/{colorHex}', [ColorSimulatorController::class, 'getColorHarmonies']);
    Route::get('/products/suggestions/{colorHex}', [ColorSimulatorController::class, 'getProductSuggestions']);
});

// Routes du panier
Route::prefix('cart')->group(function () {
    Route::get('/', [CartController::class, 'index'])->name('cart.index');
    Route::post('/add', [CartController::class, 'add'])->name('cart.add');
    Route::post('/update', [CartController::class, 'update'])->name('cart.update');
    Route::post('/remove', [CartController::class, 'remove'])->name('cart.remove');
    Route::post('/clear', [CartController::class, 'clear'])->name('cart.clear');
});

// Routes du checkout
Route::middleware(['auth'])->group(function () {
    Route::get('/checkout', [CheckoutController::class, 'index'])->name('checkout.index');
    Route::post('/checkout', [CheckoutController::class, 'store'])->name('checkout.store');
    Route::get('/checkout/confirmation/{order}', [CheckoutController::class, 'confirmation'])
        ->name('checkout.confirmation');
});

// Routes du localisateur d'unités
Route::prefix('units')->group(function () {
    Route::get('/', [UnitLocatorController::class, 'index'])->name('units.index');
    Route::get('/nearest', [UnitLocatorController::class, 'findNearest'])->name('units.nearest');
    Route::get('/{unit}', [UnitLocatorController::class, 'show'])->name('units.show');
    Route::get('/{unit}/opening-hours', [UnitLocatorController::class, 'openingHours'])->name('units.hours');
});

// Routes du calculateur de peinture
Route::prefix('calculator')->group(function () {
    Route::get('/', [PaintCalculatorController::class, 'index'])->name('calculator.index');
    Route::post('/calculate', [PaintCalculatorController::class, 'calculate'])->name('calculator.calculate');
});

// Routes du blog
Route::prefix('blog')->group(function () {
    Route::get('/', [BlogController::class, 'index'])->name('blog.index');
    Route::get('/category/{slug}', [BlogController::class, 'category'])->name('blog.category');
    Route::get('/tag/{tag}', [BlogController::class, 'tag'])->name('blog.tag');
    Route::get('/{slug}', [BlogController::class, 'show'])->name('blog.show');
});

// Page Notre Entreprise
Route::get('/about', function () {
    return view('about.index');
})->name('about');

// Page Documentation
Route::get('/documentation', function () {
    return view('documentation.index');
})->name('documentation');

// Routes d'administration
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    // Administration du blog
    Route::prefix('blog')->name('blog.')->group(function () {
        // Articles
        Route::resource('posts', \App\Http\Controllers\Admin\BlogController::class);
        
        // Catégories
        Route::resource('categories', \App\Http\Controllers\Admin\BlogCategoryController::class);
        
        // Tags
        Route::resource('tags', \App\Http\Controllers\Admin\BlogTagController::class);
    });
});

// Language Switch Route
Route::get('/language/{locale}', [LanguageController::class, 'switch'])->name('language.switch');

// Video Library Routes
Route::get('/videos', [VideoController::class, 'index'])->name('videos.index');
Route::get('/videos/{video}', [VideoController::class, 'show'])->name('videos.show');

// Technical Sheets Routes
Route::prefix('technical-sheets')->name('technical-sheets.')->group(function () {
    Route::get('/', [TechnicalSheetController::class, 'index'])->name('index');
    Route::get('/{technicalSheet}', [TechnicalSheetController::class, 'show'])->name('show');
    Route::get('/{technicalSheet}/download', [TechnicalSheetController::class, 'download'])->name('download');
    Route::get('/{technicalSheet}/3d', [TechnicalSheetController::class, 'view3d'])->name('view3d');
    Route::post('/{technicalSheet}/email', [TechnicalSheetController::class, 'email'])->name('email');
    Route::get('/{technicalSheet}/qr-code', [TechnicalSheetController::class, 'qrCode'])->name('qr-code');
});
