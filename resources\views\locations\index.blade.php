@extends('layouts.app')

@section('content')
<div class="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-blue-900 to-blue-600 text-white py-20">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-5xl font-bold mb-6">{{ __('locations.hero.title') }}</h1>
                <p class="text-xl mb-8">{{ __('locations.hero.subtitle') }}</p>
                <div class="flex justify-center space-x-8 text-center">
                    <div>
                        <div class="text-4xl font-bold">{{ collect($locations)->where('type', 'production')->count() }}</div>
                        <div class="text-blue-200">{{ __('locations.hero.production_units') }}</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold">{{ collect($locations)->where('type', 'commercial')->count() }}</div>
                        <div class="text-blue-200">{{ __('locations.hero.commercial_units') }}</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold">{{ collect($locations)->sum('employees') }}</div>
                        <div class="text-blue-200">{{ __('locations.hero.total_employees') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Interactive Map Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">{{ __('locations.map.title') }}</h2>
                    <p class="text-gray-600">{{ __('locations.map.subtitle') }}</p>
                </div>
                
                <!-- Map Container -->
                <div class="bg-gray-100 rounded-lg overflow-hidden shadow-lg">
                    <div id="locations-map" class="w-full h-96"></div>
                </div>
                
                <!-- Map Legend -->
                <div class="mt-6 flex justify-center">
                    <div class="bg-white rounded-lg shadow-lg p-4 flex items-center space-x-6">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-600 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-700">{{ __('locations.legend.production') }}</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-600 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-700">{{ __('locations.legend.commercial') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Locations Grid -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">{{ __('locations.list.title') }}</h2>
                    <p class="text-gray-600">{{ __('locations.list.subtitle') }}</p>
                </div>
                
                <!-- Filter Tabs -->
                <div class="flex justify-center mb-8">
                    <div class="bg-white rounded-lg shadow-lg p-1 flex">
                        <button onclick="filterLocations('all')" 
                                class="filter-btn active px-6 py-2 rounded-md font-semibold transition duration-300">
                            {{ __('locations.filters.all') }}
                        </button>
                        <button onclick="filterLocations('production')" 
                                class="filter-btn px-6 py-2 rounded-md font-semibold transition duration-300">
                            {{ __('locations.filters.production') }}
                        </button>
                        <button onclick="filterLocations('commercial')" 
                                class="filter-btn px-6 py-2 rounded-md font-semibold transition duration-300">
                            {{ __('locations.filters.commercial') }}
                        </button>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="locations-grid">
                    @foreach($locations as $location)
                    <div class="location-card bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300" 
                         data-type="{{ $location['type'] }}">
                        @if($location['image'])
                            <img src="{{ $location['image'] }}" alt="{{ $location['name'] }}" class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                                </svg>
                            </div>
                        @endif
                        
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-xl font-semibold text-gray-900">{{ $location['name'] }}</h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                           {{ $location['type'] === 'production' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                                    {{ __('locations.types.' . $location['type']) }}
                                </span>
                            </div>
                            
                            <p class="text-gray-600 mb-4 line-clamp-2">{{ $location['description'] }}</p>
                            
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {{ $location['address'] }}
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    {{ $location['phone'] }}
                                </div>
                                @if($location['capacity'])
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    {{ $location['capacity'] }}
                                </div>
                                @endif
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    {{ $location['employees'] }} {{ __('locations.employees') }}
                                </div>
                            </div>
                            
                            <!-- Specialties -->
                            <div class="mb-4">
                                <h4 class="text-sm font-semibold text-gray-900 mb-2">{{ __('locations.specialties') }}</h4>
                                <div class="flex flex-wrap gap-1">
                                    @foreach(array_slice($location['specialties'], 0, 3) as $specialty)
                                        <span class="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                                            {{ $specialty }}
                                        </span>
                                    @endforeach
                                    @if(count($location['specialties']) > 3)
                                        <span class="inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                                            +{{ count($location['specialties']) - 3 }}
                                        </span>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="flex gap-2">
                                <a href="{{ route('locations.show', $location['slug']) }}" 
                                   class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-center font-semibold transition duration-300">
                                    {{ __('locations.view_details') }}
                                </a>
                                <button onclick="showOnMap({{ $location['latitude'] }}, {{ $location['longitude'] }}, '{{ $location['name'] }}')"
                                        class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg transition duration-300">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-6">{{ __('locations.contact.title') }}</h2>
                <p class="text-gray-600 mb-8">{{ __('locations.contact.subtitle') }}</p>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ __('locations.contact.phone') }}</h3>
                        <p class="text-gray-600">+213 21 51 20 30</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ __('locations.contact.email') }}</h3>
                        <p class="text-gray-600"><EMAIL></p>
                    </div>
                    
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ __('locations.contact.hours') }}</h3>
                        <p class="text-gray-600">{{ __('locations.contact.business_hours') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
// Initialize map
let map;
let markers = [];

document.addEventListener('DOMContentLoaded', function() {
    initializeMap();
});

function initializeMap() {
    // Center map on Algeria
    map = L.map('locations-map').setView([36.7538, 3.0588], 6);
    
    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);
    
    // Add markers for all locations
    const locations = @json($locations);
    
    locations.forEach(location => {
        const markerColor = location.type === 'production' ? 'blue' : 'green';
        const marker = L.marker([location.latitude, location.longitude])
            .addTo(map)
            .bindPopup(`
                <div class="p-2">
                    <h3 class="font-semibold text-lg">${location.name}</h3>
                    <p class="text-sm text-gray-600 mb-2">${location.address}</p>
                    <p class="text-sm mb-2">${location.description}</p>
                    <div class="flex gap-2">
                        <a href="/locations/${location.slug}" class="bg-blue-600 text-white px-3 py-1 rounded text-sm">
                            {{ __('locations.view_details') }}
                        </a>
                        <a href="tel:${location.phone}" class="bg-green-600 text-white px-3 py-1 rounded text-sm">
                            {{ __('locations.call') }}
                        </a>
                    </div>
                </div>
            `);
        
        markers.push({marker, location});
    });
}

function showOnMap(lat, lng, name) {
    map.setView([lat, lng], 12);
    
    // Find and open the popup for this location
    markers.forEach(({marker, location}) => {
        if (location.name === name) {
            marker.openPopup();
        }
    });
    
    // Scroll to map
    document.getElementById('locations-map').scrollIntoView({
        behavior: 'smooth'
    });
}

function filterLocations(type) {
    const cards = document.querySelectorAll('.location-card');
    const buttons = document.querySelectorAll('.filter-btn');
    
    // Update button states
    buttons.forEach(btn => btn.classList.remove('active', 'bg-blue-600', 'text-white'));
    event.target.classList.add('active', 'bg-blue-600', 'text-white');
    
    // Filter cards
    cards.forEach(card => {
        if (type === 'all' || card.dataset.type === type) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Initialize filter buttons
document.addEventListener('DOMContentLoaded', function() {
    const firstButton = document.querySelector('.filter-btn');
    if (firstButton) {
        firstButton.classList.add('bg-blue-600', 'text-white');
    }
});
</script>
@endsection
