@extends('layouts.app')

@section('content')
<div class="h-screen {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <div class="h-full flex flex-col">
        <!-- Header -->
        <div class="bg-white border-b p-4">
            <div class="container mx-auto px-6 flex items-center justify-between">
                <h1 class="text-xl font-bold">{{ $sheet->getTranslation('title', app()->getLocale()) }} - {{ __('technical-sheets.sheet.3d_view.title') }}</h1>
                <a href="{{ route('technical-sheets.show', $sheet) }}" class="text-gray-600 hover:text-gray-800">
                    &times;
                </a>
            </div>
        </div>

        <!-- 3D Viewer -->
        <div class="flex-1 bg-gray-900">
            <div class="h-full flex items-center justify-center" id="model-container">
                <div x-data="{ loading: true }" class="text-center">
                    <!-- Loading State -->
                    <div x-show="loading" class="text-white">
                        <svg class="w-12 h-12 mx-auto mb-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p>{{ __('technical-sheets.sheet.3d_view.loading') }}</p>
                    </div>

                    <!-- 3D Model -->
                    <model-viewer src="{{ $sheet->model_3d_url }}"
                                x-init="$el.addEventListener('load', () => loading = false)"
                                camera-controls
                                auto-rotate
                                shadow-intensity="1">
                    </model-viewer>

                    <!-- Instructions -->
                    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white bg-black bg-opacity-50 px-4 py-2 rounded-lg">
                        {{ __('technical-sheets.sheet.3d_view.instructions') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    model-viewer {
        width: 100%;
        height: 100%;
        background-color: #1a1a1a;
    }
</style>
@endpush

@push('scripts')
<script type="module" src="https://unpkg.com/@google/model-viewer/dist/model-viewer.min.js"></script>
@endpush
