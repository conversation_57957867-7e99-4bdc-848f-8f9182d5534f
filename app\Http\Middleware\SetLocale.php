<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\URL;

class SetLocale
{
    public function handle(Request $request, Closure $next)
    {
        // Obtenir la locale depuis l'URL ou utiliser la locale par défaut
        $locale = $request->segment(1);
        $locales = array_keys(config('localization.locales'));

        if (in_array($locale, $locales)) {
            App::setLocale($locale);
            URL::defaults(['locale' => $locale]);
        } else {
            $defaultLocale = config('localization.default_locale');
            App::setLocale($defaultLocale);
            URL::defaults(['locale' => $defaultLocale]);
        }

        return $next($request);
    }
}
