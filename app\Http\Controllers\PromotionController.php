<?php

namespace App\Http\Controllers;

use App\Models\Promotion;
use App\Models\Product;
use Illuminate\Http\Request;

class PromotionController extends Controller
{
    public function index(Request $request)
    {
        $query = Promotion::query()
            ->where('is_active', true)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now());

        // Apply filters
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('category')) {
            $query->whereHas('products.category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Apply sorting
        $sort = $request->get('sort', 'newest');
        switch ($sort) {
            case 'ending_soon':
                $query->orderBy('end_date', 'asc');
                break;
            case 'biggest_discount':
                $query->orderBy('discount_percentage', 'desc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        $promotions = $query->with(['products', 'products.category'])->paginate(12);
        $types = Promotion::distinct('type')->pluck('type');

        return view('promotions.index', compact('promotions', 'types'));
    }

    public function show(Promotion $promotion)
    {
        if (!$promotion->is_active || $promotion->end_date < now()) {
            abort(404);
        }

        $promotion->load(['products', 'products.category', 'products.images']);
        
        // Get related promotions
        $relatedPromotions = Promotion::where('id', '!=', $promotion->id)
            ->where('is_active', true)
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->take(3)
            ->get();

        return view('promotions.show', compact('promotion', 'relatedPromotions'));
    }

    public function apply(Request $request, Promotion $promotion)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1'
        ]);

        // Check if promotion is still valid
        if (!$promotion->is_active || $promotion->end_date < now()) {
            return response()->json([
                'success' => false,
                'message' => __('promotions.expired')
            ], 400);
        }

        // Check if product is eligible
        if (!$promotion->products->contains($request->product_id)) {
            return response()->json([
                'success' => false,
                'message' => __('promotions.not_eligible')
            ], 400);
        }

        // Calculate discount
        $product = Product::find($request->product_id);
        $originalPrice = $product->price * $request->quantity;
        $discountAmount = $originalPrice * ($promotion->discount_percentage / 100);
        $finalPrice = $originalPrice - $discountAmount;

        return response()->json([
            'success' => true,
            'data' => [
                'original_price' => $originalPrice,
                'discount_amount' => $discountAmount,
                'final_price' => $finalPrice,
                'discount_percentage' => $promotion->discount_percentage
            ]
        ]);
    }

    public function newsletter(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        // Logic to subscribe to newsletter for promotions
        // This could integrate with a mailing service like Mailchimp

        return response()->json([
            'success' => true,
            'message' => __('promotions.newsletter.subscribed')
        ]);
    }
}
