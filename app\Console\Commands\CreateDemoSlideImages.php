<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use Illuminate\Support\Facades\Storage;

class CreateDemoSlideImages extends Command
{
    protected $signature = 'slides:create-demo-images';
    protected $description = 'Créer des images de démonstration pour les slides';

    public function handle()
    {
        $this->info('Création des images de démonstration...');

        // Créer le répertoire s'il n'existe pas
        if (!Storage::exists('slides')) {
            Storage::makeDirectory('slides');
        }

        // URLs d'images de placeholder haute qualité
        $images = [
            [
                'filename' => 'acryphob_demo.jpg',
                'url' => 'https://picsum.photos/2560/1440?random=1',
                'title' => 'ACRYPHOB - Excellence en Peintures'
            ],
            [
                'filename' => 'enap_innovation.jpg',
                'url' => 'https://picsum.photos/2560/1440?random=2',
                'title' => 'ENAP - Innovation & Qualité'
            ],
            [
                'filename' => 'solutions_pro.jpg',
                'url' => 'https://picsum.photos/2560/1440?random=3',
                'title' => 'Solutions Professionnelles'
            ]
        ];

        foreach ($images as $imageData) {
            $this->downloadAndOptimizeImage($imageData);
        }

        $this->info('Images de démonstration créées avec succès !');
    }

    private function downloadAndOptimizeImage($data)
    {
        try {
            // Télécharger l'image
            $imageContent = file_get_contents($data['url']);

            if ($imageContent === false) {
                $this->error("Impossible de télécharger: {$data['url']}");
                return;
            }

            // Sauvegarder l'image originale
            $path = 'slides/' . $data['filename'];
            Storage::put($path, $imageContent);

            // Pour l'instant, on ne crée que l'image originale
            // L'optimisation sera faite par le service d'optimisation lors de l'upload

            $this->info("Image créée: {$data['filename']}");

        } catch (\Exception $e) {
            $this->error("Erreur lors de la création de {$data['filename']}: " . $e->getMessage());
        }
    }




}
