@extends('layouts.app')

@section('content')
<div class="py-12 {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <div class="container mx-auto px-6">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold mb-4">{{ __('videos.gallery.title') }}</h1>
            <p class="text-gray-600">{{ __('videos.gallery.subtitle') }}</p>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Search -->
                <div class="col-span-1 md:col-span-2">
                    <input type="text" 
                           placeholder="{{ __('videos.gallery.search') }}"
                           class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                </div>

                <!-- Category Filter -->
                <div>
                    <select class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value="">{{ __('videos.gallery.filter.all') }}</option>
                        @foreach($categories as $category)
                            <option value="{{ $category }}">{{ $category }}</option>
                        @endforeach
                    </select>
                </div>

                <!-- Difficulty Filter -->
                <div>
                    <select class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value="">{{ __('videos.gallery.filter.difficulty') }}</option>
                        @foreach(['beginner', 'intermediate', 'advanced', 'professional'] as $level)
                            <option value="{{ $level }}">{{ __('videos.gallery.difficulty_levels.' . $level) }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <!-- Videos Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            @foreach($videos as $video)
            <div class="bg-white rounded-lg shadow-lg overflow-hidden group">
                <div class="relative">
                    <img src="{{ $video->thumbnail_url }}" 
                         alt="{{ $video->getTranslation('title', app()->getLocale()) }}" 
                         class="w-full aspect-video object-cover">
                    <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"/>
                        </svg>
                    </div>
                    <div class="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                        {{ gmdate("i:s", $video->duration) }}
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="font-semibold text-lg mb-2">{{ $video->getTranslation('title', app()->getLocale()) }}</h3>
                    <p class="text-gray-600 text-sm mb-2 line-clamp-2">{{ $video->getTranslation('description', app()->getLocale()) }}</p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-blue-600">{{ __('videos.gallery.difficulty_levels.' . $video->difficulty_level) }}</span>
                        <span class="text-gray-500">{{ number_format($video->views) }} views</span>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            {{ $videos->links() }}
        </div>
    </div>
</div>
@endsection
