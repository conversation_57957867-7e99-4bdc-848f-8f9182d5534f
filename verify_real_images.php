<?php
// Script de vérification des vraies images ENAP

echo "🔍 VÉRIFICATION DES VRAIES IMAGES ENAP\n";
echo "=====================================\n\n";

// 1. Vérifier les images réelles
echo "1. 📸 Vérification des images produits ENAP :\n";
$realImages = [
    'acryphob.jpg' => 'ACRYPHOB - Peinture acrylique',
    'blancryl.jpg' => 'BLANCRYL - Peinture blanche premium',
    'decosoie.jpg' => 'DECOSOIE - Finition soyeuse',
    'katifa.jpg' => 'KATIFA - Gamme professionnelle',
    'decoperle.jpg' => 'DECOPERLE - Finition nacrée'
];

$slidesDir = 'public/images/slides/';
foreach ($realImages as $image => $description) {
    $path = $slidesDir . $image;
    if (file_exists($path)) {
        $size = filesize($path);
        $sizeKB = round($size / 1024, 2);
        echo "   ✅ {$image} - {$sizeKB} KB - {$description}\n";
    } else {
        echo "   ❌ {$image} - MANQUANT - {$description}\n";
    }
}

echo "\n";

// 2. Tester l'API avec les nouvelles images
echo "2. 🌐 Test de l'API avec les vraies images :\n";
$apiUrl = 'http://localhost/wsite-enap/public/api/slides';

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'method' => 'GET'
    ]
]);

$response = @file_get_contents($apiUrl, false, $context);

if ($response !== false) {
    $data = json_decode($response, true);
    if ($data && is_array($data)) {
        echo "   ✅ API fonctionne - " . count($data) . " slides trouvés\n\n";
        
        foreach ($data as $index => $slide) {
            echo "      Slide " . ($index + 1) . ": {$slide['title']}\n";
            echo "      Image: {$slide['image_url']}\n";
            
            // Extraire le nom du fichier
            $imageName = basename($slide['image_url']);
            if (isset($realImages[$imageName])) {
                echo "      ✅ Image produit ENAP: {$realImages[$imageName]}\n";
            } else {
                echo "      ⚠️ Image non reconnue: {$imageName}\n";
            }
            
            // Vérifier si l'image est accessible via HTTP
            $imageHeaders = @get_headers($slide['image_url']);
            if ($imageHeaders && strpos($imageHeaders[0], '200') !== false) {
                echo "      ✅ Image accessible via HTTP\n";
            } else {
                echo "      ❌ Image non accessible via HTTP\n";
            }
            echo "\n";
        }
    } else {
        echo "   ❌ API retourne des données invalides\n";
    }
} else {
    echo "   ❌ API non accessible\n";
}

// 3. Vérifier les URLs complètes
echo "3. 🔗 Vérification des URLs complètes :\n";
$baseUrl = 'http://localhost/wsite-enap/public/images/slides/';
foreach (array_keys($realImages) as $image) {
    $url = $baseUrl . $image;
    $headers = @get_headers($url);
    if ($headers && strpos($headers[0], '200') !== false) {
        echo "   ✅ {$url}\n";
    } else {
        echo "   ❌ {$url}\n";
    }
}

echo "\n";

// 4. Comparaison avant/après
echo "4. 📊 COMPARAISON AVANT/APRÈS :\n";
echo "   AVANT: Images SVG génériques\n";
echo "   APRÈS: Vraies photos des produits ENAP\n\n";

echo "   Produits maintenant affichés :\n";
foreach ($realImages as $image => $description) {
    echo "   • {$description}\n";
}

echo "\n";

// 5. Instructions de test
echo "5. 🧪 INSTRUCTIONS DE TEST :\n";
echo "   1. Ouvrez : http://localhost/wsite-enap/public/\n";
echo "   2. Vérifiez que le slider affiche les VRAIES PHOTOS des produits ENAP\n";
echo "   3. Chaque slide doit montrer un produit spécifique (ACRYPHOB, BLANCRYL, etc.)\n";
echo "   4. Les transitions doivent rester fluides avec UNE SEULE image à la fois\n";
echo "   5. Testez la navigation entre les différents produits\n";

echo "\n";

// 6. Avantages des vraies images
echo "6. 🎯 AVANTAGES DES VRAIES IMAGES :\n";
echo "   ✅ Présentation authentique des produits ENAP\n";
echo "   ✅ Meilleure connexion avec les clients\n";
echo "   ✅ Showcase professionnel de la gamme\n";
echo "   ✅ Crédibilité et confiance renforcées\n";
echo "   ✅ Impact visuel plus fort\n";

echo "\n🎉 Vérification terminée !\n";
echo "Votre slider affiche maintenant les vraies photos des produits ENAP !\n";
?>
