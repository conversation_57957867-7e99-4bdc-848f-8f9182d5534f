

<?php $__env->startSection('content'); ?>
<div class="py-12 <?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">
    <div class="container mx-auto px-6">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold mb-4"><?php echo e(__('videos.gallery.title')); ?></h1>
            <p class="text-gray-600"><?php echo e(__('videos.gallery.subtitle')); ?></p>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- Search -->
                <div class="col-span-1 md:col-span-2">
                    <input type="text" 
                           placeholder="<?php echo e(__('videos.gallery.search')); ?>"
                           class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                </div>

                <!-- Category Filter -->
                <div>
                    <select class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value=""><?php echo e(__('videos.gallery.filter.all')); ?></option>
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($category); ?>"><?php echo e($category); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Difficulty Filter -->
                <div>
                    <select class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200">
                        <option value=""><?php echo e(__('videos.gallery.filter.difficulty')); ?></option>
                        <?php $__currentLoopData = ['beginner', 'intermediate', 'advanced', 'professional']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($level); ?>"><?php echo e(__('videos.gallery.difficulty_levels.' . $level)); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>
        </div>

        <!-- Videos Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <?php $__currentLoopData = $videos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $video): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden group">
                <div class="relative">
                    <img src="<?php echo e($video->thumbnail_url); ?>" 
                         alt="<?php echo e($video->getTranslation('title', app()->getLocale())); ?>" 
                         class="w-full aspect-video object-cover">
                    <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"/>
                        </svg>
                    </div>
                    <div class="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                        <?php echo e(gmdate("i:s", $video->duration)); ?>

                    </div>
                </div>
                <div class="p-4">
                    <h3 class="font-semibold text-lg mb-2"><?php echo e($video->getTranslation('title', app()->getLocale())); ?></h3>
                    <p class="text-gray-600 text-sm mb-2 line-clamp-2"><?php echo e($video->getTranslation('description', app()->getLocale())); ?></p>
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-blue-600"><?php echo e(__('videos.gallery.difficulty_levels.' . $video->difficulty_level)); ?></span>
                        <span class="text-gray-500"><?php echo e(number_format($video->views)); ?> views</span>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            <?php echo e($videos->links()); ?>

        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\wsite-enap\resources\views/videos/index.blade.php ENDPATH**/ ?>