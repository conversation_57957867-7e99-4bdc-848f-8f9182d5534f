@extends('layouts.app')

@section('content')
<div class="py-12">
    <div class="container mx-auto px-6">
        <h1 class="text-3xl font-bold mb-8"><PERSON></h1>

        @if($products->count() > 0)
            <!-- Liste des produits -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
                <div class="p-6">
                    <div class="flow-root">
                        <ul role="list" class="-my-6 divide-y divide-gray-200">
                            @foreach($products as $product)
                                <li class="flex py-6">
                                    <div class="h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
                                        <img src="{{ $product['image'] }}" alt="{{ $product['name'] }}"
                                             class="h-full w-full object-cover object-center">
                                    </div>

                                    <div class="ml-4 flex flex-1 flex-col">
                                        <div>
                                            <div class="flex justify-between text-base font-medium text-gray-900">
                                                <h3>{{ $product['name'] }}</h3>
                                                <p class="ml-4">{{ number_format($product['price'], 2) }} DA</p>
                                            </div>
                                        </div>
                                        <div class="flex flex-1 items-end justify-between text-sm">
                                            <div class="flex items-center space-x-2">
                                                <label for="quantity-{{ $product['id'] }}" class="text-gray-500">Quantité</label>
                                                <input type="number" id="quantity-{{ $product['id'] }}" 
                                                       class="w-16 rounded-md border-gray-300"
                                                       value="{{ $product['quantity'] }}"
                                                       min="1"
                                                       data-product-id="{{ $product['id'] }}"
                                                       @change="updateQuantity($event, {{ $product['id'] }})">
                                            </div>
                                            <div class="flex">
                                                <button type="button" @click="removeFromCart({{ $product['id'] }})"
                                                        class="font-medium text-red-600 hover:text-red-500">
                                                    Supprimer
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Résumé de la commande -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="p-6">
                    <h2 class="text-lg font-semibold mb-4">Résumé de la commande</h2>
                    <div class="flow-root">
                        <div class="divide-y divide-gray-200">
                            <div class="py-4">
                                <div class="flex justify-between text-base font-medium text-gray-900">
                                    <p>Sous-total</p>
                                    <p>{{ number_format($total, 2) }} DA</p>
                                </div>
                                <p class="mt-0.5 text-sm text-gray-500">Taxes et frais de livraison calculés à la caisse</p>
                            </div>

                            <div class="py-4">
                                <div class="flex justify-between">
                                    <a href="{{ route('products.index') }}"
                                       class="font-medium text-blue-600 hover:text-blue-500">
                                        Continuer vos achats
                                    </a>
                                    <a href="{{ route('checkout.index') }}"
                                       class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                                        Passer à la caisse
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Votre panier est vide</h3>
                <p class="mt-1 text-sm text-gray-500">Commencez votre shopping en parcourant notre catalogue de produits.</p>
                <div class="mt-6">
                    <a href="{{ route('products.index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        Voir les produits
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

@push('scripts')
<script>
    function updateQuantity(event, productId) {
        const quantity = event.target.value;
        fetch(`/cart/update`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.cartCount === 0) {
                window.location.reload();
            }
        });
    }

    function removeFromCart(productId) {
        fetch(`/cart/remove`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                product_id: productId
            })
        })
        .then(response => response.json())
        .then(data => {
            window.location.reload();
        });
    }
</script>
@endpush
@endsection
