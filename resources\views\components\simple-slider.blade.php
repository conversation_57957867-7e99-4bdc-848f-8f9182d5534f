<!-- Slider Simple pour Test -->
<div id="simple-slider" class="relative h-screen bg-gray-900" x-data="simpleSlider()">
    
    <!-- Container principal -->
    <div class="relative w-full h-full">
        
        <!-- Slides -->
        <div class="relative w-full h-full">
            <template x-for="(slide, index) in slides" :key="slide.id">
                <div class="absolute inset-0 w-full h-full"
                     x-show="currentSlide === index"
                     x-transition:enter="transition-opacity duration-1000"
                     x-transition:enter-start="opacity-0"
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="transition-opacity duration-1000"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0">
                    
                    <!-- Image -->
                    <div class="absolute inset-0 w-full h-full">
                        <img :src="slide.image_url" 
                             :alt="slide.image_alt || slide.title"
                             class="w-full h-full object-cover">
                        
                        <!-- Overlay -->
                        <div class="absolute inset-0 bg-black/30"></div>
                    </div>

                    <!-- Contenu -->
                    <div class="relative z-10 h-full flex items-center">
                        <div class="container mx-auto px-6">
                            <div class="max-w-4xl text-center mx-auto">
                                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white"
                                    x-text="slide.title"
                                    x-show="slide.title"></h1>

                                <p class="text-lg md:text-xl mb-8 text-white/90"
                                   x-text="slide.description"
                                   x-show="slide.description"></p>
                                
                                <div x-show="slide.button_text && slide.button_url">
                                    <a :href="slide.button_url || '#'"
                                       class="inline-flex items-center bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                                        <span x-text="slide.button_text"></span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- État de chargement -->
        <div x-show="loading" class="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div class="text-center text-white">
                <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
                <p class="text-xl">Chargement...</p>
            </div>
        </div>

        <!-- Slide par défaut -->
        <div x-show="!loading && slides.length === 0" class="absolute inset-0 bg-gradient-to-br from-blue-900 to-purple-900">
            <div class="h-full flex items-center justify-center">
                <div class="container mx-auto px-6 text-center text-white">
                    <h1 class="text-6xl font-bold mb-6">ENAP</h1>
                    <p class="text-xl mb-8">Excellence en peintures</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div x-show="slides.length > 1" class="absolute bottom-8 left-0 right-0 z-20">
        <div class="container mx-auto px-6">
            <div class="flex justify-center items-center space-x-6">
                
                <!-- Bouton précédent -->
                <button @click="previousSlide()" 
                        class="p-3 rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors">
                    ←
                </button>

                <!-- Indicateurs -->
                <div class="flex space-x-3">
                    <template x-for="(slide, index) in slides" :key="index">
                        <button @click="goToSlide(index)"
                                class="w-3 h-3 rounded-full transition-colors"
                                :class="{
                                    'bg-white': currentSlide === index,
                                    'bg-white/50': currentSlide !== index
                                }"></button>
                    </template>
                </div>

                <!-- Bouton suivant -->
                <button @click="nextSlide()" 
                        class="p-3 rounded-full bg-white/20 text-white hover:bg-white/30 transition-colors">
                    →
                </button>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
function simpleSlider() {
    return {
        slides: [],
        currentSlide: 0,
        loading: true,
        
        async init() {
            console.log('Initialisation du slider simple...');
            await this.loadSlides();
        },

        async loadSlides() {
            try {
                this.loading = true;
                
                const response = await fetch('/api/slides');
                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
                
                this.slides = await response.json();
                console.log('Slides chargés:', this.slides);
                
                this.loading = false;
            } catch (error) {
                console.error('Erreur:', error);
                this.loading = false;
                this.slides = [];
            }
        },

        nextSlide() {
            if (this.slides.length === 0) return;
            this.currentSlide = (this.currentSlide + 1) % this.slides.length;
        },

        previousSlide() {
            if (this.slides.length === 0) return;
            this.currentSlide = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1;
        },

        goToSlide(index) {
            if (this.slides.length === 0 || index === this.currentSlide) return;
            this.currentSlide = index;
        }
    }
}
</script>
