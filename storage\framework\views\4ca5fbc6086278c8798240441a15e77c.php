<?php $__env->startSection('content'); ?>
<div class="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-green-900 to-green-600 text-white py-20">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-5xl font-bold mb-6"><?php echo e(__('documentation.hero.title')); ?></h1>
                <p class="text-xl mb-8"><?php echo e(__('documentation.hero.subtitle')); ?></p>
                <div class="flex justify-center">
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                        <div class="flex items-center space-x-4">
                            <svg class="w-8 h-8 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <span class="text-lg font-semibold"><?php echo e(__('documentation.hero.access_all')); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4"><?php echo e(__('documentation.search.title')); ?></h2>
                        <p class="text-gray-600"><?php echo e(__('documentation.search.subtitle')); ?></p>
                    </div>
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" placeholder="<?php echo e(__('documentation.search.placeholder')); ?>" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        </div>
                        <div class="md:w-48">
                            <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                <option><?php echo e(__('documentation.search.all_types')); ?></option>
                                <option><?php echo e(__('documentation.search.catalogs')); ?></option>
                                <option><?php echo e(__('documentation.search.color_charts')); ?></option>
                                <option><?php echo e(__('documentation.search.guides')); ?></option>
                                <option><?php echo e(__('documentation.search.technical_docs')); ?></option>
                            </select>
                        </div>
                        <button class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition duration-300">
                            <?php echo e(__('documentation.search.search_btn')); ?>

                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Documentation Categories -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4"><?php echo e(__('documentation.categories.title')); ?></h2>
                    <p class="text-gray-600"><?php echo e(__('documentation.categories.subtitle')); ?></p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- Catalogues -->
                    <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 hover:shadow-lg transition duration-300">
                        <div class="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('documentation.categories.catalogs.title')); ?></h3>
                        <p class="text-gray-600 mb-4"><?php echo e(__('documentation.categories.catalogs.description')); ?></p>
                        <div class="text-sm text-blue-600 font-semibold"><?php echo e(__('documentation.categories.catalogs.count')); ?></div>
                    </div>

                    <!-- Nuanciers -->
                    <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 hover:shadow-lg transition duration-300">
                        <div class="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h4a2 2 0 002-2V9a2 2 0 00-2-2H7a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('documentation.categories.color_charts.title')); ?></h3>
                        <p class="text-gray-600 mb-4"><?php echo e(__('documentation.categories.color_charts.description')); ?></p>
                        <div class="text-sm text-purple-600 font-semibold"><?php echo e(__('documentation.categories.color_charts.count')); ?></div>
                    </div>

                    <!-- Guides d'Application -->
                    <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 hover:shadow-lg transition duration-300">
                        <div class="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('documentation.categories.guides.title')); ?></h3>
                        <p class="text-gray-600 mb-4"><?php echo e(__('documentation.categories.guides.description')); ?></p>
                        <div class="text-sm text-green-600 font-semibold"><?php echo e(__('documentation.categories.guides.count')); ?></div>
                    </div>

                    <!-- Documentation Technique -->
                    <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-6 hover:shadow-lg transition duration-300">
                        <div class="w-16 h-16 bg-orange-600 rounded-lg flex items-center justify-center mb-4">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('documentation.categories.technical.title')); ?></h3>
                        <p class="text-gray-600 mb-4"><?php echo e(__('documentation.categories.technical.description')); ?></p>
                        <div class="text-sm text-orange-600 font-semibold"><?php echo e(__('documentation.categories.technical.count')); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Documents -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4"><?php echo e(__('documentation.featured.title')); ?></h2>
                    <p class="text-gray-600"><?php echo e(__('documentation.featured.subtitle')); ?></p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Document 1 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
                        <div class="h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center">
                            <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('documentation.featured.catalog_general.title')); ?></h3>
                            <p class="text-gray-600 mb-4"><?php echo e(__('documentation.featured.catalog_general.description')); ?></p>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500"><?php echo e(__('documentation.featured.catalog_general.pages')); ?></span>
                                <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-semibold transition duration-300">
                                    <?php echo e(__('documentation.featured.download')); ?>

                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Document 2 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
                        <div class="h-48 bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                            <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h4a2 2 0 002-2V9a2 2 0 00-2-2H7a2 2 0 00-2 2v6a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('documentation.featured.color_chart.title')); ?></h3>
                            <p class="text-gray-600 mb-4"><?php echo e(__('documentation.featured.color_chart.description')); ?></p>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500"><?php echo e(__('documentation.featured.color_chart.colors')); ?></span>
                                <button class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-semibold transition duration-300">
                                    <?php echo e(__('documentation.featured.view')); ?>

                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Document 3 -->
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300">
                        <div class="h-48 bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
                            <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo e(__('documentation.featured.application_guide.title')); ?></h3>
                            <p class="text-gray-600 mb-4"><?php echo e(__('documentation.featured.application_guide.description')); ?></p>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-500"><?php echo e(__('documentation.featured.application_guide.pages')); ?></span>
                                <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-semibold transition duration-300">
                                    <?php echo e(__('documentation.featured.download')); ?>

                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Color Chart Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4"><?php echo e(__('documentation.interactive.title')); ?></h2>
                    <p class="text-gray-600"><?php echo e(__('documentation.interactive.subtitle')); ?></p>
                </div>
                
                <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-8">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                        <div>
                            <h3 class="text-2xl font-bold text-gray-900 mb-4"><?php echo e(__('documentation.interactive.color_chart.title')); ?></h3>
                            <p class="text-gray-600 mb-6"><?php echo e(__('documentation.interactive.color_chart.description')); ?></p>
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <svg class="w-6 h-6 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700"><?php echo e(__('documentation.interactive.color_chart.feature1')); ?></span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-6 h-6 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700"><?php echo e(__('documentation.interactive.color_chart.feature2')); ?></span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-6 h-6 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span class="text-gray-700"><?php echo e(__('documentation.interactive.color_chart.feature3')); ?></span>
                                </div>
                            </div>
                            <div class="mt-6">
                                <a href="<?php echo e(route('color-simulator')); ?>" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition duration-300">
                                    <?php echo e(__('documentation.interactive.color_chart.try_now')); ?>

                                </a>
                            </div>
                        </div>
                        <div class="grid grid-cols-6 gap-2">
                            <!-- Color Palette Preview -->
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-red-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-orange-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-yellow-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-green-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-blue-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-purple-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-pink-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-indigo-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-teal-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-cyan-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-lime-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-emerald-500"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-red-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-orange-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-yellow-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-green-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-blue-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-purple-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-pink-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-indigo-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-teal-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-cyan-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-lime-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-emerald-400"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-red-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-orange-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-yellow-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-green-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-blue-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-purple-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-pink-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-indigo-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-teal-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-cyan-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-lime-600"></div>
                            <div class="aspect-square rounded-lg shadow-sm cursor-pointer hover:scale-110 transition duration-300 bg-emerald-600"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gray-900 text-white">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl font-bold mb-6"><?php echo e(__('documentation.cta.title')); ?></h2>
                <p class="text-xl mb-8 text-gray-300"><?php echo e(__('documentation.cta.subtitle')); ?></p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="<?php echo e(route('technical-sheets.index')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition duration-300">
                        <?php echo e(__('documentation.cta.technical_sheets')); ?>

                    </a>
                    <a href="<?php echo e(route('quotes.create')); ?>" class="bg-transparent border-2 border-white hover:bg-white hover:text-gray-900 text-white px-8 py-3 rounded-lg font-semibold transition duration-300">
                        <?php echo e(__('documentation.cta.contact_expert')); ?>

                    </a>
                </div>
            </div>
        </div>
    </section>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\wsite-enap\resources\views/documentation/index.blade.php ENDPATH**/ ?>