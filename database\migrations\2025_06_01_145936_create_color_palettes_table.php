<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('color_palettes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique(); // Code unique de la couleur
            $table->string('hex_value');
            $table->json('rgb_value'); // {r: 255, g: 255, b: 255}
            $table->json('lab_value')->nullable(); // Pour une meilleure précision des couleurs
            $table->text('description')->nullable();
            $table->boolean('is_available')->default(true);
            $table->json('harmonies')->nullable(); // Couleurs complémentaires, analogues, etc.
            $table->foreignId('product_id')->nullable()->constrained(); // Si la couleur est spécifique à un produit
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('color_palettes');
    }
};
