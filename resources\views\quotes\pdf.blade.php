<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('quotes.quote') }} - {{ $quote->reference_number }}</title>
    <style>
        body {
            font-family: DejaVu Sans, sans-serif;
            line-height: 1.5;
            color: #1a1a1a;
        }
        .container {
            padding: 20px;
        }
        .header {
            border-bottom: 2px solid #4f46e5;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .logo {
            max-width: 200px;
        }
        .quote-info {
            margin-bottom: 30px;
        }
        .quote-ref {
            font-size: 24px;
            color: #4f46e5;
            margin-bottom: 10px;
        }
        .quote-date {
            color: #6b7280;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #4f46e5;
            margin-bottom: 15px;
        }
        .grid {
            display: table;
            width: 100%;
            margin-bottom: 15px;
        }
        .grid-row {
            display: table-row;
        }
        .grid-cell {
            display: table-cell;
            padding: 8px 0;
        }
        .label {
            font-weight: bold;
            color: #4b5563;
            width: 30%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th {
            background-color: #f3f4f6;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            color: #4b5563;
        }
        td {
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
        }
        .total-row {
            font-weight: bold;
            background-color: #f3f4f6;
        }
        .status-banner {
            padding: 15px;
            margin-bottom: 30px;
            border-radius: 6px;
        }
        .status-pending {
            background-color: #fef3c7;
            color: #92400e;
        }
        .status-processed {
            background-color: #dbeafe;
            color: #1e40af;
        }
        .status-accepted {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-rejected {
            background-color: #fee2e2;
            color: #991b1b;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 12px;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <img src="{{ asset('images/logo.png') }}" alt="ENAP Logo" class="logo">
            <div style="float: right; text-align: right;">
                <div class="quote-ref">{{ $quote->reference_number }}</div>
                <div class="quote-date">{{ __('quotes.created_at') }}: {{ $quote->created_at->format('d/m/Y') }}</div>
                <div>{{ __('quotes.valid_until') }}: {{ $quote->valid_until->format('d/m/Y') }}</div>
            </div>
        </div>

        <!-- Status Banner -->
        <div class="status-banner status-{{ $quote->status }}">
            {{ __("quotes.status_{$quote->status}") }} - {{ __("quotes.status_{$quote->status}_message") }}
        </div>

        <!-- Client Information -->
        <div class="section">
            <div class="section-title">{{ __('quotes.client_information') }}</div>
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-cell label">{{ __('quotes.client_name') }}</div>
                    <div class="grid-cell">{{ $quote->client_name }}</div>
                </div>
                <div class="grid-row">
                    <div class="grid-cell label">{{ __('quotes.client_email') }}</div>
                    <div class="grid-cell">{{ $quote->client_email }}</div>
                </div>
                @if($quote->client_phone)
                <div class="grid-row">
                    <div class="grid-cell label">{{ __('quotes.client_phone') }}</div>
                    <div class="grid-cell">{{ $quote->client_phone }}</div>
                </div>
                @endif
                @if($quote->company_name)
                <div class="grid-row">
                    <div class="grid-cell label">{{ __('quotes.company_name') }}</div>
                    <div class="grid-cell">{{ $quote->company_name }}</div>
                </div>
                @endif
            </div>
        </div>

        <!-- Project Information -->
        <div class="section">
            <div class="section-title">{{ __('quotes.project_information') }}</div>
            <div class="grid">
                <div class="grid-row">
                    <div class="grid-cell label">{{ __('quotes.surface_type') }}</div>
                    <div class="grid-cell">{{ __("quotes.{$quote->surface_type}") }}</div>
                </div>
                <div class="grid-row">
                    <div class="grid-cell label">{{ __('quotes.surface_area') }}</div>
                    <div class="grid-cell">{{ $quote->surface_area }} m²</div>
                </div>
                <div class="grid-row">
                    <div class="grid-cell label">{{ __('quotes.coats_number') }}</div>
                    <div class="grid-cell">{{ $quote->coats_number }}</div>
                </div>
            </div>
            <div style="margin-top: 15px;">
                <div class="label">{{ __('quotes.project_description') }}</div>
                <div style="margin-top: 8px;">{{ $quote->project_description }}</div>
            </div>
        </div>

        <!-- Products -->
        <div class="section">
            <div class="section-title">{{ __('quotes.selected_products') }}</div>
            <table>
                <thead>
                    <tr>
                        <th>{{ __('quotes.product') }}</th>
                        <th>{{ __('quotes.unit_price') }}</th>
                        <th>{{ __('quotes.quantity') }}</th>
                        <th>{{ __('quotes.subtotal') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($quote->products as $product)
                        <tr>
                            <td>{{ $product['name'] }}</td>
                            <td>{{ number_format($product['price'], 2) }} DA</td>
                            <td>{{ $product['quantity'] }}</td>
                            <td>{{ number_format($product['subtotal'], 2) }} DA</td>
                        </tr>
                    @endforeach
                </tbody>
                <tfoot>
                    <tr class="total-row">
                        <td colspan="3" style="text-align: right;">{{ __('quotes.total_amount') }}:</td>
                        <td>{{ number_format($quote->total_amount, 2) }} DA</td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>{{ __('quotes.pdf_footer_note') }}</p>
            <p>ENAP - Entreprise Nationale des Peintures</p>
            <p>{{ config('app.address', 'Route Nationale N° 5, Oued Smar, Alger, Algérie') }}</p>
            <p>{{ config('app.phone', '+213 XX XX XX XX') }} | {{ config('app.email', '<EMAIL>') }}</p>
        </div>
    </div>
</body>
</html>
