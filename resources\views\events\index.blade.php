@extends('layouts.app')

@section('content')
<div class="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-purple-900 to-purple-600 text-white py-20">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-5xl font-bold mb-6">{{ __('events.hero.title') }}</h1>
                <p class="text-xl mb-8">{{ __('events.hero.subtitle') }}</p>
                <div class="flex justify-center space-x-8 text-center">
                    <div>
                        <div class="text-4xl font-bold">{{ $events->total() }}</div>
                        <div class="text-purple-200">{{ __('events.hero.total_events') }}</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold">6</div>
                        <div class="text-purple-200">{{ __('events.hero.locations') }}</div>
                    </div>
                    <div>
                        <div class="text-4xl font-bold">1000+</div>
                        <div class="text-purple-200">{{ __('events.hero.participants') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Events -->
    @if($featuredEvents->isNotEmpty())
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">{{ __('events.featured.title') }}</h2>
                    <p class="text-gray-600">{{ __('events.featured.subtitle') }}</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    @foreach($featuredEvents as $event)
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition duration-300 border-2 border-transparent hover:border-purple-200">
                        @if($event->featured_image)
                            <img src="{{ $event->featured_image }}" alt="{{ $event->title }}" class="w-full h-48 object-cover">
                        @else
                            <div class="w-full h-48 bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        @endif
                        
                        <div class="p-6">
                            <div class="flex items-center space-x-2 mb-3">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $event->type_color }}-100 text-{{ $event->type_color }}-800">
                                    {{ __('events.types.' . $event->type) }}
                                </span>
                                @if($event->is_free)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {{ __('events.free') }}
                                    </span>
                                @endif
                                @if($event->is_online)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ __('events.online') }}
                                    </span>
                                @endif
                            </div>
                            
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ $event->title }}</h3>
                            <p class="text-gray-600 mb-4 line-clamp-2">{{ $event->short_description }}</p>
                            
                            <div class="space-y-2 mb-4">
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    {{ $event->formatted_date }}
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {{ $event->location }}
                                </div>
                                @if($event->max_participants)
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    {{ $event->available_spots }} {{ __('events.spots_available') }}
                                </div>
                                @endif
                            </div>
                            
                            <div class="flex gap-2">
                                <a href="{{ route('events.show', $event) }}" 
                                   class="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-center font-semibold transition duration-300">
                                    {{ __('events.view_details') }}
                                </a>
                                @if($event->can_register)
                                <button onclick="quickRegister('{{ $event->id }}')"
                                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-semibold transition duration-300">
                                    {{ __('events.register') }}
                                </button>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    @endif

    <!-- Filters Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <form method="GET" action="{{ route('events.index') }}" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('events.filters.type') }}</label>
                                <select name="type" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                    <option value="all" {{ $type === 'all' ? 'selected' : '' }}>{{ __('events.filters.all_types') }}</option>
                                    @foreach($types as $eventType)
                                        <option value="{{ $eventType }}" {{ $type === $eventType ? 'selected' : '' }}>
                                            {{ __('events.types.' . $eventType) }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('events.filters.location') }}</label>
                                <select name="location" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                    <option value="all" {{ $location === 'all' ? 'selected' : '' }}>{{ __('events.filters.all_locations') }}</option>
                                    @foreach($locations as $eventLocation)
                                        <option value="{{ $eventLocation }}" {{ $location === $eventLocation ? 'selected' : '' }}>
                                            {{ $eventLocation }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('events.filters.month') }}</label>
                                <select name="month" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                    <option value="all" {{ $month === 'all' ? 'selected' : '' }}>{{ __('events.filters.all_months') }}</option>
                                    @for($i = 1; $i <= 12; $i++)
                                        <option value="{{ $i }}" {{ $month == $i ? 'selected' : '' }}>
                                            {{ \Carbon\Carbon::create()->month($i)->format('F') }}
                                        </option>
                                    @endfor
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button type="submit" class="w-full bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold transition duration-300">
                                    {{ __('events.filters.apply') }}
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <div class="mt-6 flex justify-between items-center">
                        <a href="{{ route('events.calendar') }}" 
                           class="inline-flex items-center text-purple-600 hover:text-purple-800 font-medium">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            {{ __('events.view_calendar') }}
                        </a>
                        
                        <div class="text-sm text-gray-500">
                            {{ __('events.showing_events', ['count' => $events->count(), 'total' => $events->total()]) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Events List -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-6">
            <div class="max-w-6xl mx-auto">
                @if($events->isNotEmpty())
                    <div class="space-y-6">
                        @foreach($events as $event)
                        <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition duration-300 border border-gray-200">
                            <div class="flex flex-col lg:flex-row gap-6">
                                @if($event->featured_image)
                                    <div class="lg:w-64 flex-shrink-0">
                                        <img src="{{ $event->featured_image }}" alt="{{ $event->title }}" 
                                             class="w-full h-48 lg:h-32 object-cover rounded-lg">
                                    </div>
                                @endif
                                
                                <div class="flex-1">
                                    <div class="flex flex-wrap items-center gap-2 mb-3">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ $event->type_color }}-100 text-{{ $event->type_color }}-800">
                                            {{ __('events.types.' . $event->type) }}
                                        </span>
                                        @if($event->is_free)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                {{ __('events.free') }}
                                            </span>
                                        @endif
                                        @if($event->is_online)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ __('events.online') }}
                                            </span>
                                        @endif
                                        @if($event->certificate_provided)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                {{ __('events.certificate') }}
                                            </span>
                                        @endif
                                    </div>
                                    
                                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                                        <a href="{{ route('events.show', $event) }}" class="hover:text-purple-600 transition duration-300">
                                            {{ $event->title }}
                                        </a>
                                    </h3>
                                    
                                    <p class="text-gray-600 mb-4 line-clamp-2">{{ $event->short_description }}</p>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500 mb-4">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                            {{ $event->formatted_date }}
                                        </div>
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            {{ $event->location }}
                                        </div>
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            {{ $event->duration }}
                                        </div>
                                    </div>
                                    
                                    <div class="flex flex-col sm:flex-row gap-3 justify-between items-start sm:items-center">
                                        <div class="flex gap-2">
                                            <a href="{{ route('events.show', $event) }}" 
                                               class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg font-semibold transition duration-300">
                                                {{ __('events.view_details') }}
                                            </a>
                                            @if($event->can_register)
                                                <button onclick="quickRegister('{{ $event->id }}')"
                                                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold transition duration-300">
                                                    {{ __('events.register') }}
                                                </button>
                                            @elseif($event->is_full)
                                                <span class="bg-red-100 text-red-800 px-6 py-2 rounded-lg font-semibold">
                                                    {{ __('events.full') }}
                                                </span>
                                            @endif
                                        </div>
                                        
                                        @if($event->max_participants)
                                            <div class="text-sm text-gray-500">
                                                {{ $event->available_spots }} {{ __('events.spots_available') }}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    
                    <!-- Pagination -->
                    @if($events->hasPages())
                    <div class="mt-12">
                        {{ $events->appends(request()->query())->links() }}
                    </div>
                    @endif
                @else
                    <!-- No Events -->
                    <div class="text-center py-12">
                        <svg class="w-24 h-24 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ __('events.no_events') }}</h2>
                        <p class="text-gray-600 mb-8">{{ __('events.no_events_description') }}</p>
                        
                        <a href="{{ route('events.index') }}" 
                           class="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition duration-300">
                            {{ __('events.view_all') }}
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </section>

    <!-- Newsletter Subscription -->
    <section class="py-16 bg-purple-50">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl font-bold text-gray-900 mb-6">{{ __('events.newsletter.title') }}</h2>
                <p class="text-gray-600 mb-8">{{ __('events.newsletter.subtitle') }}</p>
                
                <form id="events-newsletter-form" class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                    <input type="email" id="events-newsletter-email" placeholder="{{ __('events.newsletter.email_placeholder') }}" 
                           class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-lg font-semibold transition duration-300">
                        {{ __('events.newsletter.subscribe') }}
                    </button>
                </form>
                
                <p class="text-sm text-gray-500 mt-4">{{ __('events.newsletter.privacy_notice') }}</p>
            </div>
        </div>
    </section>
</div>

<script>
// Quick registration modal functionality
function quickRegister(eventId) {
    // This would open a modal for quick registration
    // For now, redirect to the event page
    window.location.href = `/events/${eventId}`;
}

// Newsletter subscription
document.getElementById('events-newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('events-newsletter-email').value;
    
    fetch('{{ route("events.newsletter") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ email: email })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            document.getElementById('events-newsletter-email').value = '';
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("events.newsletter.error") }}');
    });
});
</script>
@endsection
