<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'customer_profile_id',
        'order_number',
        'status',
        'total_amount',
        'tax_amount',
        'shipping_amount',
        'payment_method',
        'payment_status',
        'notes',
        'paid_at',
        'shipped_at',
        'delivered_at',
        'cancelled_at'
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'paid_at' => 'datetime',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'cancelled_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function customerProfile()
    {
        return $this->belongsTo(CustomerProfile::class);
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    public function getFormattedTotalAttribute()
    {
        return number_format($this->total_amount, 2) . ' DA';
    }

    public function getFormattedStatusAttribute()
    {
        return match($this->status) {
            'pending' => 'En attente',
            'processing' => 'En traitement',
            'completed' => 'Terminée',
            'cancelled' => 'Annulée',
            default => $this->status
        };
    }

    public function getFormattedPaymentStatusAttribute()
    {
        return match($this->payment_status) {
            'pending' => 'En attente',
            'paid' => 'Payé',
            'failed' => 'Échoué',
            'refunded' => 'Remboursé',
            default => $this->payment_status
        };
    }

    // Méthodes utilitaires
    public function calculateTotal()
    {
        $subtotal = $this->items->sum('subtotal');
        $this->total_amount = $subtotal + $this->tax_amount + $this->shipping_amount;
        return $this;
    }

    public function canBeCancelled()
    {
        return in_array($this->status, ['pending', 'processing']) && !$this->cancelled_at;
    }

    public function cancel()
    {
        if ($this->canBeCancelled()) {
            $this->update([
                'status' => 'cancelled',
                'cancelled_at' => now()
            ]);
        }
    }

    public function markAsPaid()
    {
        $this->update([
            'payment_status' => 'paid',
            'paid_at' => now()
        ]);
    }

    public function markAsShipped()
    {
        $this->update([
            'status' => 'processing',
            'shipped_at' => now()
        ]);
    }

    public function markAsDelivered()
    {
        $this->update([
            'status' => 'completed',
            'delivered_at' => now()
        ]);
    }
}
