<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ColorPalette extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'hex_value',
        'rgb_value',
        'category',
        'complementary_colors',
        'meta_data',
        'is_active'
    ];

    protected $casts = [
        'complementary_colors' => 'array',
        'meta_data' => 'array',
        'is_active' => 'boolean'
    ];

    public function products()
    {
        return $this->belongsToMany(Product::class, 'product_colors')
                    ->withPivot('is_available')
                    ->withTimestamps();
    }

    public function getRgbArrayAttribute()
    {
        if (!$this->rgb_value) {
            return null;
        }

        list($r, $g, $b) = explode(',', $this->rgb_value);
        return [
            'r' => (int) $r,
            'g' => (int) $g,
            'b' => (int) $b
        ];
    }
}
