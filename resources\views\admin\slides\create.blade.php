@extends('layouts.admin')

@section('title', 'Créer un Slide')

@section('content')
<div class="container mx-auto px-6 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">C<PERSON>er un Slide</h1>
            <p class="text-gray-600 mt-2">Ajoutez un nouveau slide au diaporama principal</p>
        </div>
        <a href="{{ route('admin.slides.index') }}" 
           class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Retour à la liste
        </a>
    </div>

    <!-- Formulaire -->
    <form action="{{ route('admin.slides.store') }}" method="POST" enctype="multipart/form-data" class="space-y-8">
        @csrf
        
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Colonne principale -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Informations de base -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Informations de base</h2>
                    
                    <div class="space-y-4">
                        <!-- Titre -->
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                Titre principal *
                            </label>
                            <input type="text" 
                                   id="title" 
                                   name="title" 
                                   value="{{ old('title') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('title') border-red-500 @enderror"
                                   placeholder="Titre du slide"
                                   required>
                            @error('title')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                Description
                            </label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('description') border-red-500 @enderror"
                                      placeholder="Description du slide">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Image -->
                        <div>
                            <label for="image" class="block text-sm font-medium text-gray-700 mb-2">
                                Image du slide * (1920x1080 recommandé)
                            </label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="image" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                            <span>Télécharger une image</span>
                                            <input id="image" name="image" type="file" class="sr-only" accept="image/*" required>
                                        </label>
                                        <p class="pl-1">ou glisser-déposer</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF jusqu'à 5MB</p>
                                </div>
                            </div>
                            @error('image')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Alt text -->
                        <div>
                            <label for="image_alt" class="block text-sm font-medium text-gray-700 mb-2">
                                Texte alternatif de l'image
                            </label>
                            <input type="text" 
                                   id="image_alt" 
                                   name="image_alt" 
                                   value="{{ old('image_alt') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="Description de l'image pour l'accessibilité">
                        </div>
                    </div>
                </div>

                <!-- Bouton d'action -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Bouton d'action (optionnel)</h2>
                    
                    <div class="space-y-4">
                        <!-- Texte du bouton -->
                        <div>
                            <label for="button_text" class="block text-sm font-medium text-gray-700 mb-2">
                                Texte du bouton
                            </label>
                            <input type="text" 
                                   id="button_text" 
                                   name="button_text" 
                                   value="{{ old('button_text') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="Ex: Découvrir nos produits">
                        </div>

                        <!-- URL du bouton -->
                        <div>
                            <label for="button_url" class="block text-sm font-medium text-gray-700 mb-2">
                                URL du bouton
                            </label>
                            <input type="url" 
                                   id="button_url" 
                                   name="button_url" 
                                   value="{{ old('button_url') }}"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                   placeholder="https://example.com">
                        </div>

                        <!-- Target du bouton -->
                        <div>
                            <label for="button_target" class="block text-sm font-medium text-gray-700 mb-2">
                                Ouverture du lien
                            </label>
                            <select id="button_target" 
                                    name="button_target" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="_self" {{ old('button_target') === '_self' ? 'selected' : '' }}>Même onglet</option>
                                <option value="_blank" {{ old('button_target') === '_blank' ? 'selected' : '' }}>Nouvel onglet</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Traductions -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Traductions</h2>
                    
                    <div class="space-y-6">
                        @foreach($locales as $locale)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h3 class="text-lg font-medium text-gray-900 mb-4">
                                    {{ $locale === 'fr' ? 'Français' : 'العربية' }}
                                </h3>
                                
                                <div class="space-y-4">
                                    <!-- Titre traduit -->
                                    <div>
                                        <label for="translations_{{ $locale }}_title" class="block text-sm font-medium text-gray-700 mb-2">
                                            Titre *
                                        </label>
                                        <input type="hidden" name="translations[{{ $loop->index }}][locale]" value="{{ $locale }}">
                                        <input type="text" 
                                               id="translations_{{ $locale }}_title" 
                                               name="translations[{{ $loop->index }}][title]" 
                                               value="{{ old("translations.{$loop->index}.title") }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                               placeholder="Titre en {{ $locale === 'fr' ? 'français' : 'arabe' }}"
                                               required>
                                    </div>

                                    <!-- Description traduite -->
                                    <div>
                                        <label for="translations_{{ $locale }}_description" class="block text-sm font-medium text-gray-700 mb-2">
                                            Description
                                        </label>
                                        <textarea id="translations_{{ $locale }}_description" 
                                                  name="translations[{{ $loop->index }}][description]" 
                                                  rows="3"
                                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                                  placeholder="Description en {{ $locale === 'fr' ? 'français' : 'arabe' }}">{{ old("translations.{$loop->index}.description") }}</textarea>
                                    </div>

                                    <!-- Bouton traduit -->
                                    <div>
                                        <label for="translations_{{ $locale }}_button_text" class="block text-sm font-medium text-gray-700 mb-2">
                                            Texte du bouton
                                        </label>
                                        <input type="text" 
                                               id="translations_{{ $locale }}_button_text" 
                                               name="translations[{{ $loop->index }}][button_text]" 
                                               value="{{ old("translations.{$loop->index}.button_text") }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                               placeholder="Texte du bouton en {{ $locale === 'fr' ? 'français' : 'arabe' }}">
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Colonne latérale -->
            <div class="space-y-6">
                <!-- Paramètres d'affichage -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Paramètres d'affichage</h2>
                    
                    <div class="space-y-4">
                        <!-- Statut -->
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" 
                                       name="is_active" 
                                       value="1"
                                       {{ old('is_active', true) ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Slide actif</span>
                            </label>
                        </div>

                        <!-- Position du texte -->
                        <div>
                            <label for="text_position" class="block text-sm font-medium text-gray-700 mb-2">
                                Position du texte
                            </label>
                            <select id="text_position" 
                                    name="text_position" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="left" {{ old('text_position') === 'left' ? 'selected' : '' }}>Gauche</option>
                                <option value="center" {{ old('text_position') === 'center' ? 'selected' : '' }}>Centre</option>
                                <option value="right" {{ old('text_position') === 'right' ? 'selected' : '' }}>Droite</option>
                            </select>
                        </div>

                        <!-- Couleur du texte -->
                        <div>
                            <label for="text_color" class="block text-sm font-medium text-gray-700 mb-2">
                                Couleur du texte
                            </label>
                            <input type="color" 
                                   id="text_color" 
                                   name="text_color" 
                                   value="{{ old('text_color', '#ffffff') }}"
                                   class="w-full h-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <!-- Couleur de fond -->
                        <div>
                            <label for="background_color" class="block text-sm font-medium text-gray-700 mb-2">
                                Couleur de fond (optionnel)
                            </label>
                            <input type="color" 
                                   id="background_color" 
                                   name="background_color" 
                                   value="{{ old('background_color') }}"
                                   class="w-full h-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                </div>

                <!-- Paramètres d'animation -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Animation</h2>
                    
                    <div class="space-y-4">
                        <!-- Type d'animation -->
                        <div>
                            <label for="animation_type" class="block text-sm font-medium text-gray-700 mb-2">
                                Type d'animation
                            </label>
                            <select id="animation_type" 
                                    name="animation_type" 
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="fade" {{ old('animation_type') === 'fade' ? 'selected' : '' }}>Fondu</option>
                                <option value="slide" {{ old('animation_type') === 'slide' ? 'selected' : '' }}>Glissement</option>
                                <option value="zoom" {{ old('animation_type') === 'zoom' ? 'selected' : '' }}>Zoom</option>
                            </select>
                        </div>

                        <!-- Durée d'affichage -->
                        <div>
                            <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">
                                Durée d'affichage (ms)
                            </label>
                            <input type="number" 
                                   id="duration" 
                                   name="duration" 
                                   value="{{ old('duration', 5000) }}"
                                   min="1000" 
                                   max="10000" 
                                   step="500"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-xs text-gray-500 mt-1">Entre 1000ms (1s) et 10000ms (10s)</p>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="space-y-4">
                        <button type="submit" 
                                class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold">
                            Créer le slide
                        </button>
                        
                        <a href="{{ route('admin.slides.index') }}" 
                           class="w-full bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 transition-colors font-semibold text-center block">
                            Annuler
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

@push('scripts')
<script>
// Prévisualisation de l'image
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Créer un aperçu de l'image
            const preview = document.createElement('img');
            preview.src = e.target.result;
            preview.className = 'mt-4 max-w-full h-48 object-cover rounded-lg border';
            
            // Supprimer l'ancien aperçu s'il existe
            const existingPreview = document.querySelector('.image-preview');
            if (existingPreview) {
                existingPreview.remove();
            }
            
            // Ajouter le nouvel aperçu
            preview.className += ' image-preview';
            e.target.closest('.space-y-1').appendChild(preview);
        };
        reader.readAsDataURL(file);
    }
});
</script>
@endpush
@endsection
