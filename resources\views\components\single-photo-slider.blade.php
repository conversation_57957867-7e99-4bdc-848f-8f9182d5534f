<!-- Slider Photo par Photo ENAP - UNE SEULE IMAGE À LA FOIS -->
<div id="enap-photo-slider" class="relative -mt-32 h-screen overflow-hidden bg-gray-900" x-data="enapPhotoSlider()">
    
    <!-- Container principal du slider -->
    <div class="relative w-full h-full">
        
        <!-- Slides - UNE SEULE VISIBLE À LA FOIS -->
        <div class="relative w-full h-full">
            <template x-for="(slide, index) in slides" :key="slide.id">
                <div class="absolute inset-0 w-full h-full transition-all duration-1000 ease-in-out"
                     x-show="currentSlide === index"
                     x-transition:enter="transition-all duration-1000 ease-out"
                     x-transition:enter-start="opacity-0 transform scale-110"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition-all duration-1000 ease-in"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-95">
                    
                    <!-- Image de fond plein écran -->
                    <div class="absolute inset-0 w-full h-full">
                        <img :src="slide.image_url" 
                             :alt="slide.image_alt || slide.title"
                             class="w-full h-full object-cover"
                             loading="lazy">
                        
                        <!-- Overlay léger pour lisibilité du texte uniquement -->
                        <div class="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
                    </div>

                    <!-- Contenu textuel du slide -->
                    <div class="relative z-10 h-full flex items-center">
                        <div class="container mx-auto px-6">
                            <div class="max-w-4xl"
                                 :class="{
                                     'text-left': slide.text_position === 'left',
                                     'text-center mx-auto': slide.text_position === 'center',
                                     'text-right ml-auto': slide.text_position === 'right'
                                 }">
                                
                                <!-- Titre principal -->
                                <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight text-shadow-lg"
                                    :style="`color: ${slide.text_color || '#ffffff'}; text-shadow: 2px 2px 4px rgba(0,0,0,0.7), 0 0 10px rgba(0,0,0,0.3)`"
                                    x-text="slide.title"
                                    x-show="slide.title"></h1>

                                <!-- Description -->
                                <p class="text-lg md:text-xl lg:text-2xl mb-8 leading-relaxed opacity-95 text-shadow"
                                   :style="`color: ${slide.text_color || '#ffffff'}; text-shadow: 1px 1px 3px rgba(0,0,0,0.8), 0 0 8px rgba(0,0,0,0.4)`"
                                   x-text="slide.description"
                                   x-show="slide.description"></p>
                                
                                <!-- Bouton d'action -->
                                <div x-show="slide.button_text && slide.button_url">
                                    <a :href="slide.button_url || '#'"
                                       :target="slide.button_target || '_self'"
                                       class="inline-flex items-center bg-white bg-opacity-20 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-semibold hover:bg-opacity-30 transition-all duration-300 transform hover:scale-105 border border-white border-opacity-30">
                                        <span x-text="slide.button_text"></span>
                                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- État de chargement -->
        <div x-show="loading" class="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div class="text-center text-white">
                <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-white mx-auto mb-4"></div>
                <p class="text-xl">Chargement des images...</p>
            </div>
        </div>

        <!-- Message d'erreur -->
        <div x-show="error && !loading" class="absolute inset-0 flex items-center justify-center bg-gray-900">
            <div class="text-center text-white">
                <svg class="w-20 h-20 mx-auto mb-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <p class="text-xl mb-4">Erreur lors du chargement</p>
                <button @click="loadSlides()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    Réessayer
                </button>
            </div>
        </div>

        <!-- Slide par défaut si aucune donnée -->
        <div x-show="!loading && !error && slides.length === 0" class="absolute inset-0 bg-gradient-to-br from-blue-900 via-blue-800 to-purple-900">
            <div class="h-full flex items-center justify-center">
                <div class="container mx-auto px-6 text-center text-white">
                    <h1 class="text-6xl lg:text-7xl font-bold mb-6">Excellence ENAP</h1>
                    <p class="text-xl lg:text-2xl mb-8 opacity-90">Leader algérien en peintures et revêtements</p>
                    <a href="/products" class="inline-flex items-center bg-white bg-opacity-20 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-semibold hover:bg-opacity-30 transition-all">
                        Découvrir nos produits
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation et contrôles -->
    <div x-show="slides.length > 1" class="absolute bottom-8 left-0 right-0 z-20">
        <div class="container mx-auto px-6">
            <div class="flex justify-center items-center space-x-6">
                
                <!-- Bouton précédent -->
                <button @click="previousSlide()" 
                        class="p-4 rounded-full bg-white bg-opacity-20 backdrop-blur-sm text-white hover:bg-opacity-30 transition-all duration-300 transform hover:scale-110 border border-white border-opacity-30">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </button>

                <!-- Indicateurs de slides -->
                <div class="flex space-x-3">
                    <template x-for="(slide, index) in slides" :key="index">
                        <button @click="goToSlide(index)"
                                class="w-4 h-4 rounded-full transition-all duration-300 border-2 border-white"
                                :class="{
                                    'bg-white scale-125': currentSlide === index,
                                    'bg-transparent hover:bg-white hover:bg-opacity-50': currentSlide !== index
                                }"></button>
                    </template>
                </div>

                <!-- Bouton suivant -->
                <button @click="nextSlide()" 
                        class="p-4 rounded-full bg-white bg-opacity-20 backdrop-blur-sm text-white hover:bg-opacity-30 transition-all duration-300 transform hover:scale-110 border border-white border-opacity-30">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </button>

                <!-- Bouton play/pause -->
                <button @click="toggleAutoplay()" 
                        class="p-4 rounded-full bg-white bg-opacity-20 backdrop-blur-sm text-white hover:bg-opacity-30 transition-all duration-300 transform hover:scale-110 border border-white border-opacity-30 ml-4">
                    <svg x-show="isPlaying" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6"></path>
                    </svg>
                    <svg x-show="!isPlaying" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Barre de progression -->
    <div x-show="slides.length > 1 && isPlaying" class="absolute bottom-0 left-0 right-0 z-20">
        <div class="h-1 bg-white bg-opacity-20">
            <div class="h-full bg-white transition-all duration-100 ease-linear"
                 :style="`width: ${progress}%`"></div>
        </div>
    </div>

    <!-- Navigation par flèches sur les côtés -->
    <div x-show="slides.length > 1" class="absolute inset-y-0 left-0 right-0 z-10 pointer-events-none">
        <!-- Flèche gauche -->
        <button @click="previousSlide()" 
                class="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 rounded-full bg-white bg-opacity-20 backdrop-blur-sm text-white hover:bg-opacity-30 transition-all duration-300 pointer-events-auto">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </button>
        
        <!-- Flèche droite -->
        <button @click="nextSlide()" 
                class="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 rounded-full bg-white bg-opacity-20 backdrop-blur-sm text-white hover:bg-opacity-30 transition-all duration-300 pointer-events-auto">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>
    </div>
</div>

<!-- Styles CSS pour les animations fluides -->
<style>
/* Animations personnalisées pour le slider */
@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(100%) scale(1.1);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-100%) scale(1.1);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(1.1);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    #enap-photo-slider h1 {
        font-size: 2.5rem !important;
        line-height: 1.2;
    }
    
    #enap-photo-slider p {
        font-size: 1.1rem !important;
    }
    
    #enap-photo-slider .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Optimisation pour les performances et qualité d'image */
#enap-photo-slider img {
    will-change: transform;
    backface-visibility: hidden;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    filter: contrast(1.05) brightness(1.02) saturate(1.1);
    transition: filter 0.3s ease;
}

#enap-photo-slider .transition-all {
    will-change: opacity, transform;
}

/* Effet hover pour améliorer la clarté */
#enap-photo-slider:hover img {
    filter: contrast(1.08) brightness(1.05) saturate(1.15);
}

/* Assurer une bonne qualité sur tous les écrans */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    #enap-photo-slider img {
        image-rendering: -webkit-optimize-contrast;
    }
}
</style>

<!-- JavaScript Alpine.js Component -->
<script>
function enapPhotoSlider() {
    return {
        slides: [],
        currentSlide: 0,
        isPlaying: true,
        loading: true,
        error: false,
        progress: 0,
        autoplayInterval: null,
        progressInterval: null,
        
        async init() {
            console.log('🎬 Initialisation du slider ENAP...');
            await this.loadSlides();
            if (this.slides.length > 0) {
                this.startAutoplay();
                console.log(`✅ Slider initialisé avec ${this.slides.length} slides`);
            }
        },

        async loadSlides() {
            try {
                console.log('📡 Chargement des slides depuis l\'API...');
                this.loading = true;
                this.error = false;
                
                const response = await fetch('/api/slides');
                if (!response.ok) {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
                
                this.slides = await response.json();
                console.log('📸 Slides chargés:', this.slides);
                
                if (this.slides.length === 0) {
                    console.warn('⚠️ Aucun slide trouvé');
                }
                
                this.loading = false;
            } catch (error) {
                console.error('❌ Erreur lors du chargement des slides:', error);
                this.loading = false;
                this.error = true;
                
                // Fallback avec slides par défaut
                this.slides = [];
            }
        },

        nextSlide() {
            if (this.slides.length === 0) return;
            
            console.log(`➡️ Slide suivant: ${this.currentSlide} -> ${(this.currentSlide + 1) % this.slides.length}`);
            this.currentSlide = (this.currentSlide + 1) % this.slides.length;
            this.resetProgress();
        },

        previousSlide() {
            if (this.slides.length === 0) return;
            
            const newSlide = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1;
            console.log(`⬅️ Slide précédent: ${this.currentSlide} -> ${newSlide}`);
            this.currentSlide = newSlide;
            this.resetProgress();
        },

        goToSlide(index) {
            if (this.slides.length === 0 || index === this.currentSlide) return;
            
            console.log(`🎯 Aller au slide: ${this.currentSlide} -> ${index}`);
            this.currentSlide = index;
            this.resetProgress();
        },

        startAutoplay() {
            if (this.slides.length <= 1) return;
            
            this.isPlaying = true;
            const duration = this.getCurrentSlideDuration();
            
            console.log(`▶️ Démarrage autoplay (${duration}ms)`);
            
            // Démarrer la barre de progression
            this.startProgress(duration);
            
            // Démarrer l'autoplay
            this.autoplayInterval = setTimeout(() => {
                this.nextSlide();
                this.startAutoplay();
            }, duration);
        },

        stopAutoplay() {
            console.log('⏸️ Arrêt autoplay');
            this.isPlaying = false;
            if (this.autoplayInterval) {
                clearTimeout(this.autoplayInterval);
                this.autoplayInterval = null;
            }
            this.stopProgress();
        },

        toggleAutoplay() {
            if (this.isPlaying) {
                this.stopAutoplay();
            } else {
                this.startAutoplay();
            }
        },

        getCurrentSlideDuration() {
            if (this.slides.length === 0) return 5000;
            return this.slides[this.currentSlide]?.duration || 5000;
        },

        startProgress(duration) {
            this.progress = 0;
            this.progressInterval = setInterval(() => {
                this.progress += (100 / duration) * 100; // Update every 100ms
                if (this.progress >= 100) {
                    this.progress = 100;
                    this.stopProgress();
                }
            }, 100);
        },

        stopProgress() {
            if (this.progressInterval) {
                clearInterval(this.progressInterval);
                this.progressInterval = null;
            }
        },

        resetProgress() {
            this.stopProgress();
            if (this.isPlaying) {
                const duration = this.getCurrentSlideDuration();
                this.startProgress(duration);
            }
        },

        // Gestion des événements clavier
        handleKeydown(event) {
            switch(event.key) {
                case 'ArrowLeft':
                    event.preventDefault();
                    this.previousSlide();
                    break;
                case 'ArrowRight':
                    event.preventDefault();
                    this.nextSlide();
                    break;
                case ' ':
                    event.preventDefault();
                    this.toggleAutoplay();
                    break;
            }
        }
    }
}

// Initialisation des événements globaux
document.addEventListener('DOMContentLoaded', function() {
    // Support clavier
    document.addEventListener('keydown', function(event) {
        const slider = document.querySelector('#enap-photo-slider');
        if (slider && slider.__x) {
            slider.__x.$data.handleKeydown(event);
        }
    });
    
    console.log('🚀 Slider ENAP prêt !');
});
</script>
