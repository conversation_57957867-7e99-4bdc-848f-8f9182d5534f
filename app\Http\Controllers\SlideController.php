<?php

namespace App\Http\Controllers;

use App\Models\Slide;
use Illuminate\Http\Request;

class Slide<PERSON><PERSON>roller extends Controller
{
    /**
     * Obtenir les slides actifs pour l'affichage public
     */
    public function getActiveSlides()
    {
        $slides = Slide::with('translations')
            ->active()
            ->ordered()
            ->get()
            ->map(function ($slide) {
                return [
                    'id' => $slide->id,
                    'title' => $slide->localized_title,
                    'description' => $slide->localized_description,
                    'image_url' => $slide->image_url,
                    'image_alt' => $slide->image_alt,
                    'button_text' => $slide->localized_button_text,
                    'button_url' => $slide->button_url,
                    'button_target' => $slide->button_target,
                    'background_color' => $slide->background_color,
                    'text_color' => $slide->text_color,
                    'text_position' => $slide->text_position,
                    'animation_type' => $slide->animation_type,
                    'duration' => $slide->duration,
                ];
            });

        return response()->json($slides);
    }

    /**
     * API pour obtenir un slide spécifique
     */
    public function getSlide($id)
    {
        $slide = Slide::with('translations')
            ->active()
            ->findOrFail($id);

        return response()->json([
            'id' => $slide->id,
            'title' => $slide->localized_title,
            'description' => $slide->localized_description,
            'image_url' => $slide->image_url,
            'image_alt' => $slide->image_alt,
            'button_text' => $slide->localized_button_text,
            'button_url' => $slide->button_url,
            'button_target' => $slide->button_target,
            'background_color' => $slide->background_color,
            'text_color' => $slide->text_color,
            'text_position' => $slide->text_position,
            'animation_type' => $slide->animation_type,
            'duration' => $slide->duration,
        ]);
    }
}
