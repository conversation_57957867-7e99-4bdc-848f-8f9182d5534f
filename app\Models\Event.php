<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Event extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'short_description',
        'type',
        'start_date',
        'end_date',
        'location',
        'address',
        'latitude',
        'longitude',
        'max_participants',
        'price',
        'currency',
        'is_free',
        'is_online',
        'meeting_url',
        'featured_image',
        'gallery_images',
        'agenda',
        'requirements',
        'target_audience',
        'learning_objectives',
        'certificate_provided',
        'is_published',
        'is_featured',
        'meta_title',
        'meta_description',
        'view_count',
        'registration_deadline'
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'registration_deadline' => 'datetime',
        'gallery_images' => 'array',
        'agenda' => 'array',
        'requirements' => 'array',
        'learning_objectives' => 'array',
        'is_free' => 'boolean',
        'is_online' => 'boolean',
        'is_published' => 'boolean',
        'is_featured' => 'boolean',
        'certificate_provided' => 'boolean',
        'price' => 'decimal:2'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($event) {
            if (empty($event->slug)) {
                $event->slug = Str::slug($event->title);
            }
        });
    }

    public function registrations(): HasMany
    {
        return $this->hasMany(EventRegistration::class);
    }

    public function speakers(): BelongsToMany
    {
        return $this->belongsToMany(EventSpeaker::class, 'event_speaker_assignments')
                    ->withPivot(['role', 'bio', 'order'])
                    ->withTimestamps();
    }

    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function getStatusAttribute()
    {
        $now = now();
        
        if ($this->end_date < $now) {
            return 'completed';
        } elseif ($this->start_date <= $now && $this->end_date >= $now) {
            return 'ongoing';
        } elseif ($this->registration_deadline && $this->registration_deadline < $now) {
            return 'registration_closed';
        } else {
            return 'upcoming';
        }
    }

    public function getAvailableSpotsAttribute()
    {
        if (!$this->max_participants) {
            return null;
        }

        $confirmedRegistrations = $this->registrations()
            ->where('status', 'confirmed')
            ->count();

        return max(0, $this->max_participants - $confirmedRegistrations);
    }

    public function getIsFullAttribute()
    {
        return $this->max_participants && $this->available_spots <= 0;
    }

    public function getCanRegisterAttribute()
    {
        $now = now();
        
        return $this->is_published 
            && $this->status === 'upcoming'
            && !$this->is_full
            && (!$this->registration_deadline || $this->registration_deadline >= $now);
    }

    public function getDurationAttribute()
    {
        $diff = $this->start_date->diff($this->end_date);
        
        if ($diff->days > 0) {
            return $diff->days . ' ' . __('events.days');
        } elseif ($diff->h > 0) {
            return $diff->h . ' ' . __('events.hours');
        } else {
            return $diff->i . ' ' . __('events.minutes');
        }
    }

    public function getFormattedDateAttribute()
    {
        if ($this->start_date->isSameDay($this->end_date)) {
            return $this->start_date->format('d/m/Y') . ' ' . 
                   $this->start_date->format('H:i') . ' - ' . 
                   $this->end_date->format('H:i');
        } else {
            return $this->start_date->format('d/m/Y H:i') . ' - ' . 
                   $this->end_date->format('d/m/Y H:i');
        }
    }

    public function getTypeColorAttribute()
    {
        $colors = [
            'formation' => 'blue',
            'conference' => 'green',
            'workshop' => 'yellow',
            'webinar' => 'purple',
            'exhibition' => 'red',
            'networking' => 'cyan'
        ];

        return $colors[$this->type] ?? 'gray';
    }

    public function getTypeIconAttribute()
    {
        $icons = [
            'formation' => 'academic-cap',
            'conference' => 'microphone',
            'workshop' => 'cog',
            'webinar' => 'video-camera',
            'exhibition' => 'photograph',
            'networking' => 'users'
        ];

        return $icons[$this->type] ?? 'calendar';
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeUpcoming($query)
    {
        return $query->where('end_date', '>=', now());
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByLocation($query, $location)
    {
        return $query->where('location', $location);
    }

    public function scopeInMonth($query, $month, $year = null)
    {
        $year = $year ?: now()->year;
        return $query->whereYear('start_date', $year)
                    ->whereMonth('start_date', $month);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'LIKE', "%{$search}%")
              ->orWhere('description', 'LIKE', "%{$search}%")
              ->orWhere('location', 'LIKE', "%{$search}%");
        });
    }
}
