<?php

namespace App\Http\Controllers;

use App\Models\ProductionUnit;
use Illuminate\Http\Request;

class ProductionUnitController extends Controller
{
    public function index()
    {
        $units = ProductionUnit::all();
        return view('units.index', compact('units'));
    }

    public function show(ProductionUnit $unit)
    {
        return view('units.show', compact('unit'));
    }

    public function getLocations()
    {
        $units = ProductionUnit::select('id', 'name', 'latitude', 'longitude', 'address', 'city', 'wilaya')
            ->where('is_active', true)
            ->get();

        return response()->json([
            'type' => 'FeatureCollection',
            'features' => $units->map(function ($unit) {
                return [
                    'type' => 'Feature',
                    'geometry' => [
                        'type' => 'Point',
                        'coordinates' => [$unit->longitude, $unit->latitude]
                    ],
                    'properties' => [
                        'id' => $unit->id,
                        'name' => $unit->name,
                        'address' => $unit->full_address,
                        'isOpen' => $unit->isOpenNow()
                    ]
                ];
            })
        ]);
    }
}
