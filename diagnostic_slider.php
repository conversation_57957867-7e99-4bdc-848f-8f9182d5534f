<?php
// Script de diagnostic pour le slider ENAP

echo "🔍 DIAGNOSTIC DU SLIDER ENAP\n";
echo "============================\n\n";

// 1. Vérifier les images
echo "1. 📸 Vérification des images :\n";
$slidesDir = 'public/images/slides/';
$images = ['slide-1.svg', 'slide-2.svg', 'slide-3.svg', 'slide-4.svg', 'slide-5.svg'];

foreach ($images as $image) {
    $path = $slidesDir . $image;
    if (file_exists($path)) {
        $size = filesize($path);
        echo "   ✅ {$image} - {$size} bytes\n";
    } else {
        echo "   ❌ {$image} - MANQUANT\n";
    }
}

echo "\n";

// 2. Tester l'API
echo "2. 🌐 Test de l'API :\n";
$apiUrl = 'http://localhost/wsite-enap/public/api/slides';

$context = stream_context_create([
    'http' => [
        'timeout' => 10,
        'method' => 'GET'
    ]
]);

$response = @file_get_contents($apiUrl, false, $context);

if ($response !== false) {
    $data = json_decode($response, true);
    if ($data && is_array($data)) {
        echo "   ✅ API fonctionne - " . count($data) . " slides trouvés\n";
        
        foreach ($data as $index => $slide) {
            echo "      Slide " . ($index + 1) . ": {$slide['title']}\n";
            echo "      Image: {$slide['image_url']}\n";
            
            // Vérifier si l'image est accessible
            $imageHeaders = @get_headers($slide['image_url']);
            if ($imageHeaders && strpos($imageHeaders[0], '200') !== false) {
                echo "      ✅ Image accessible\n";
            } else {
                echo "      ❌ Image non accessible\n";
            }
            echo "\n";
        }
    } else {
        echo "   ❌ API retourne des données invalides\n";
    }
} else {
    echo "   ❌ API non accessible\n";
}

echo "\n";

// 3. Vérifier les fichiers du slider
echo "3. 📁 Vérification des fichiers :\n";
$files = [
    'resources/views/components/single-photo-slider.blade.php',
    'app/Models/Slide.php',
    'app/Http/Controllers/SlideController.php',
    'database/seeders/SlideSeeder.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "   ✅ {$file}\n";
    } else {
        echo "   ❌ {$file} - MANQUANT\n";
    }
}

echo "\n";

// 4. Vérifier les routes
echo "4. 🛣️ Test des routes :\n";
$routes = [
    'http://localhost/wsite-enap/public/' => 'Page d\'accueil',
    'http://localhost/wsite-enap/public/api/slides' => 'API Slides'
];

foreach ($routes as $url => $description) {
    $headers = @get_headers($url);
    if ($headers && strpos($headers[0], '200') !== false) {
        echo "   ✅ {$description} - {$url}\n";
    } else {
        echo "   ❌ {$description} - {$url}\n";
    }
}

echo "\n";

// 5. Instructions de test
echo "5. 🧪 INSTRUCTIONS DE TEST :\n";
echo "   1. Ouvrez : http://localhost/wsite-enap/public/\n";
echo "   2. Vérifiez que le slider s'affiche avec UNE SEULE image à la fois\n";
echo "   3. Testez les contrôles de navigation (flèches, indicateurs)\n";
echo "   4. Vérifiez les transitions fluides entre les slides\n";
echo "   5. Testez le responsive sur mobile (F12 > mode mobile)\n";

echo "\n";

// 6. Résolution des problèmes
echo "6. 🔧 EN CAS DE PROBLÈME :\n";
echo "   - Vérifiez que Apache/Nginx est démarré\n";
echo "   - Vérifiez que MySQL est démarré\n";
echo "   - Exécutez : php artisan cache:clear\n";
echo "   - Exécutez : php artisan config:clear\n";
echo "   - Vérifiez les logs : storage/logs/laravel.log\n";

echo "\n🎉 Diagnostic terminé !\n";
?>
