<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\BlogPost;
use App\Models\TechnicalSheet;
use App\Models\Video;
use App\Models\Promotion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class SearchController extends Controller
{
    public function index(Request $request)
    {
        $query = $request->get('q', '');
        $type = $request->get('type', 'all');
        $category = $request->get('category', '');
        
        if (empty($query)) {
            return view('search.index', [
                'query' => $query,
                'results' => collect(),
                'total' => 0,
                'suggestions' => $this->getPopularSearches()
            ]);
        }

        $results = collect();
        $total = 0;

        // Search in different content types based on filter
        if ($type === 'all' || $type === 'products') {
            $products = $this->searchProducts($query, $category);
            $results = $results->merge($products->map(function ($item) {
                return [
                    'type' => 'product',
                    'title' => $item->name,
                    'description' => $item->description,
                    'url' => route('products.show', $item),
                    'image' => $item->images->first()?->url,
                    'category' => $item->category?->name,
                    'relevance' => $this->calculateRelevance($item, request('q'))
                ];
            }));
            $total += $products->count();
        }

        if ($type === 'all' || $type === 'technical_sheets') {
            $sheets = $this->searchTechnicalSheets($query);
            $results = $results->merge($sheets->map(function ($item) {
                return [
                    'type' => 'technical_sheet',
                    'title' => $item->title,
                    'description' => $item->description,
                    'url' => route('technical-sheets.show', $item),
                    'image' => null,
                    'category' => $item->category?->name,
                    'relevance' => $this->calculateRelevance($item, request('q'))
                ];
            }));
            $total += $sheets->count();
        }

        if ($type === 'all' || $type === 'videos') {
            $videos = $this->searchVideos($query);
            $results = $results->merge($videos->map(function ($item) {
                return [
                    'type' => 'video',
                    'title' => $item->title,
                    'description' => $item->description,
                    'url' => route('videos.show', $item),
                    'image' => $item->thumbnail_url,
                    'category' => $item->category,
                    'relevance' => $this->calculateRelevance($item, request('q'))
                ];
            }));
            $total += $videos->count();
        }

        if ($type === 'all' || $type === 'blog') {
            $posts = $this->searchBlogPosts($query);
            $results = $results->merge($posts->map(function ($item) {
                return [
                    'type' => 'blog_post',
                    'title' => $item->title,
                    'description' => $item->excerpt,
                    'url' => route('blog.show', $item),
                    'image' => $item->featured_image,
                    'category' => $item->category?->name,
                    'relevance' => $this->calculateRelevance($item, request('q'))
                ];
            }));
            $total += $posts->count();
        }

        if ($type === 'all' || $type === 'promotions') {
            $promotions = $this->searchPromotions($query);
            $results = $results->merge($promotions->map(function ($item) {
                return [
                    'type' => 'promotion',
                    'title' => $item->title,
                    'description' => $item->description,
                    'url' => route('promotions.show', $item),
                    'image' => $item->image_url,
                    'category' => 'promotion',
                    'relevance' => $this->calculateRelevance($item, request('q'))
                ];
            }));
            $total += $promotions->count();
        }

        // Sort by relevance
        $results = $results->sortByDesc('relevance')->values();

        // Log search for analytics
        $this->logSearch($query, $total);

        return view('search.index', [
            'query' => $query,
            'results' => $results,
            'total' => $total,
            'type' => $type,
            'category' => $category,
            'suggestions' => $this->getSearchSuggestions($query)
        ]);
    }

    public function suggestions(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $suggestions = collect();

        // Product suggestions
        $products = Product::where('name', 'LIKE', "%{$query}%")
            ->orWhere('description', 'LIKE', "%{$query}%")
            ->limit(5)
            ->get(['name', 'slug']);

        $suggestions = $suggestions->merge($products->map(function ($product) {
            return [
                'text' => $product->name,
                'type' => 'product',
                'url' => route('products.show', $product)
            ];
        }));

        // Add popular searches
        $popularSearches = $this->getPopularSearches()
            ->filter(function ($search) use ($query) {
                return stripos($search, $query) !== false;
            })
            ->take(3);

        $suggestions = $suggestions->merge($popularSearches->map(function ($search) {
            return [
                'text' => $search,
                'type' => 'popular',
                'url' => route('search.index', ['q' => $search])
            ];
        }));

        return response()->json($suggestions->take(8)->values());
    }

    private function searchProducts($query, $category = '')
    {
        $queryBuilder = Product::where('is_active', true)
            ->where(function ($q) use ($query) {
                $q->where('name', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%")
                  ->orWhere('reference', 'LIKE', "%{$query}%");
            });

        if ($category) {
            $queryBuilder->whereHas('category', function ($q) use ($category) {
                $q->where('slug', $category);
            });
        }

        return $queryBuilder->with(['category', 'images'])->limit(20)->get();
    }

    private function searchTechnicalSheets($query)
    {
        return TechnicalSheet::where('is_published', true)
            ->where(function ($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%")
                  ->orWhere('reference_number', 'LIKE', "%{$query}%");
            })
            ->with('category')
            ->limit(10)
            ->get();
    }

    private function searchVideos($query)
    {
        return Video::where(function ($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%")
                  ->orWhere('tags', 'LIKE', "%{$query}%");
            })
            ->limit(10)
            ->get();
    }

    private function searchBlogPosts($query)
    {
        return BlogPost::where('status', 'published')
            ->where('published_at', '<=', now())
            ->where(function ($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('content', 'LIKE', "%{$query}%")
                  ->orWhere('excerpt', 'LIKE', "%{$query}%");
            })
            ->with('category')
            ->limit(10)
            ->get();
    }

    private function searchPromotions($query)
    {
        return Promotion::active()
            ->where(function ($q) use ($query) {
                $q->where('title', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%");
            })
            ->limit(5)
            ->get();
    }

    private function calculateRelevance($item, $query)
    {
        $score = 0;
        $queryLower = strtolower($query);
        
        // Title match (highest weight)
        if (isset($item->title) && stripos($item->title, $query) !== false) {
            $score += 10;
            if (stripos($item->title, $query) === 0) {
                $score += 5; // Bonus for starting with query
            }
        }
        
        if (isset($item->name) && stripos($item->name, $query) !== false) {
            $score += 10;
            if (stripos($item->name, $query) === 0) {
                $score += 5;
            }
        }

        // Description match (medium weight)
        if (isset($item->description) && stripos($item->description, $query) !== false) {
            $score += 5;
        }

        // Exact match bonus
        if ((isset($item->title) && strtolower($item->title) === $queryLower) ||
            (isset($item->name) && strtolower($item->name) === $queryLower)) {
            $score += 20;
        }

        return $score;
    }

    private function getPopularSearches()
    {
        // This could be stored in database or cache
        return collect([
            'peinture bâtiment',
            'peinture industrielle',
            'vernis',
            'anti-corrosion',
            'peinture marine',
            'enduit',
            'simulateur couleur',
            'calculateur peinture'
        ]);
    }

    private function getSearchSuggestions($query)
    {
        // Return related search suggestions
        $suggestions = collect();
        
        if (stripos($query, 'peinture') !== false) {
            $suggestions->push('peinture bâtiment', 'peinture industrielle', 'peinture marine');
        }
        
        if (stripos($query, 'couleur') !== false) {
            $suggestions->push('simulateur couleur', 'nuancier', 'palette couleur');
        }

        return $suggestions->take(5);
    }

    private function logSearch($query, $resultCount)
    {
        // Log search for analytics - could be stored in database
        // This helps understand user behavior and improve search
        \Log::info('Search performed', [
            'query' => $query,
            'result_count' => $resultCount,
            'user_agent' => request()->userAgent(),
            'ip' => request()->ip()
        ]);
    }
}
