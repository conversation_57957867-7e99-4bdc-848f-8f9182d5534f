<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Quote extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'reference_number',
        'client_name',
        'client_email',
        'client_phone',
        'company_name',
        'project_description',
        'surface_type',
        'surface_area',
        'coats_number',
        'products',
        'technical_requirements',
        'total_amount',
        'status',
        'internal_notes',
        'valid_until'
    ];

    protected $casts = [
        'products' => 'array',
        'technical_requirements' => 'array',
        'surface_area' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'valid_until' => 'datetime',
        'coats_number' => 'integer'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function calculateTotalAmount()
    {
        if (!$this->products) {
            return 0;
        }

        $total = 0;
        foreach ($this->products as $item) {
            $product = Product::find($item['product_id']);
            if ($product) {
                $price = $this->user && $this->user->is_professional 
                    ? $product->professional_price 
                    : $product->price;
                $total += $price * $item['quantity'];
            }
        }

        return $total;
    }

    public static function generateReferenceNumber()
    {
        $prefix = 'DEV';
        $date = now()->format('Ymd');
        $random = strtoupper(substr(uniqid(), -4));
        
        return "{$prefix}-{$date}-{$random}";
    }
}
