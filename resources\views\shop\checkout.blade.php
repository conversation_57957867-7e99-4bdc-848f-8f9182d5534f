@extends('layouts.app')

@section('content')
<div class="py-12">
    <div class="container mx-auto px-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Formulaire de commande -->
            <div>
                <h2 class="text-2xl font-semibold mb-6">Informations de commande</h2>
                
                <form action="{{ route('checkout.store') }}" method="POST" class="space-y-6">
                    @csrf

                    <!-- Informations entreprise -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-medium mb-4">Informations entreprise</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="company_name" class="block text-sm font-medium text-gray-700">Nom de l'entreprise</label>
                                <input type="text" name="company_name" id="company_name" 
                                       class="mt-1 block w-full rounded-md border-gray-300"
                                       value="{{ old('company_name', $customerProfile?->company_name) }}" required>
                                @error('company_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="contact_person" class="block text-sm font-medium text-gray-700">Personne à contacter</label>
                                <input type="text" name="contact_person" id="contact_person" 
                                       class="mt-1 block w-full rounded-md border-gray-300"
                                       value="{{ old('contact_person', $customerProfile?->contact_person) }}" required>
                                @error('contact_person')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                                    <input type="email" name="email" id="email" 
                                           class="mt-1 block w-full rounded-md border-gray-300"
                                           value="{{ old('email', $customerProfile?->contact_email) }}" required>
                                    @error('email')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700">Téléphone</label>
                                    <input type="tel" name="phone" id="phone" 
                                           class="mt-1 block w-full rounded-md border-gray-300"
                                           value="{{ old('phone', $customerProfile?->contact_phone) }}" required>
                                    @error('phone')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Adresse de livraison -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-medium mb-4">Adresse de livraison</h3>
                        
                        <div class="space-y-4">
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700">Adresse</label>
                                <input type="text" name="address" id="address" 
                                       class="mt-1 block w-full rounded-md border-gray-300"
                                       value="{{ old('address', $customerProfile?->address) }}" required>
                                @error('address')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="city" class="block text-sm font-medium text-gray-700">Ville</label>
                                    <input type="text" name="city" id="city" 
                                           class="mt-1 block w-full rounded-md border-gray-300"
                                           value="{{ old('city', $customerProfile?->city) }}" required>
                                    @error('city')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="state" class="block text-sm font-medium text-gray-700">Wilaya</label>
                                    <input type="text" name="state" id="state" 
                                           class="mt-1 block w-full rounded-md border-gray-300"
                                           value="{{ old('state', $customerProfile?->state) }}" required>
                                    @error('state')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div>
                                <label for="postal_code" class="block text-sm font-medium text-gray-700">Code postal</label>
                                <input type="text" name="postal_code" id="postal_code" 
                                       class="mt-1 block w-full rounded-md border-gray-300"
                                       value="{{ old('postal_code', $customerProfile?->postal_code) }}" required>
                                @error('postal_code')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Mode de paiement -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-medium mb-4">Mode de paiement</h3>
                        
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <input type="radio" name="payment_method" id="bank_transfer" value="bank_transfer" 
                                       class="h-4 w-4 border-gray-300 text-blue-600" checked>
                                <label for="bank_transfer" class="text-sm font-medium text-gray-700">Virement bancaire</label>
                            </div>

                            <div class="flex items-center space-x-3">
                                <input type="radio" name="payment_method" id="cash_delivery" value="cash_delivery" 
                                       class="h-4 w-4 border-gray-300 text-blue-600">
                                <label for="cash_delivery" class="text-sm font-medium text-gray-700">Paiement à la livraison</label>
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h3 class="text-lg font-medium mb-4">Notes supplémentaires</h3>
                        
                        <div>
                            <textarea name="notes" id="notes" rows="4" 
                                      class="mt-1 block w-full rounded-md border-gray-300"
                                      placeholder="Informations complémentaires pour votre commande...">{{ old('notes') }}</textarea>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" 
                                class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                            Confirmer la commande
                        </button>
                    </div>
                </form>
            </div>

            <!-- Résumé de la commande -->
            <div>
                <h2 class="text-2xl font-semibold mb-6">Résumé de la commande</h2>

                <div class="bg-white rounded-lg shadow">
                    <div class="p-6 space-y-6">
                        <!-- Liste des produits -->
                        <div class="divide-y divide-gray-200">
                            @foreach($products as $product)
                                <div class="py-4 flex justify-between">
                                    <div>
                                        <h4 class="font-medium">{{ $product['name'] }}</h4>
                                        <p class="text-sm text-gray-500">Quantité: {{ $product['quantity'] }}</p>
                                    </div>
                                    <p class="font-medium">{{ number_format($product['subtotal'], 2) }} DA</p>
                                </div>
                            @endforeach
                        </div>

                        <!-- Totaux -->
                        <div class="border-t pt-6 space-y-4">
                            <div class="flex justify-between">
                                <span class="font-medium">Sous-total</span>
                                <span>{{ number_format($total, 2) }} DA</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium">Livraison</span>
                                <span>Calculé à la confirmation</span>
                            </div>
                            <div class="flex justify-between text-lg font-bold">
                                <span>Total estimé</span>
                                <span>{{ number_format($total, 2) }} DA</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
