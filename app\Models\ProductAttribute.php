<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductAttribute extends Model
{
    protected $fillable = [
        'product_id',
        'name',
        'value',
        'unit',
        'sort_order'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function getFormattedValueAttribute()
    {
        return $this->unit ? "{$this->value} {$this->unit}" : $this->value;
    }
}
