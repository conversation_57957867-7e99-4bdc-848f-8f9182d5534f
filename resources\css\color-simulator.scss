// Color Simulator Styles
.color-simulator {
    display: flex;
    flex-direction: row;
    height: 100vh;
    
    // Left Sidebar - Room Templates
    .room-templates {
        width: 200px;
        background: #f5f5f5;
        padding: 1rem;
        overflow-y: auto;
        
        .room-template {
            cursor: pointer;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s;
            
            &:hover {
                background-color: #e0e0e0;
            }
            
            &.selected {
                background-color: #2196f3;
                color: white;
            }
            
            img {
                width: 100%;
                height: auto;
                border-radius: 4px;
            }
        }
    }
    
    // Main Canvas Area
    .canvas-container {
        flex: 1;
        position: relative;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        
        canvas {
            max-width: 100%;
            max-height: 100%;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        
        .toolbar {
            position: absolute;
            bottom: 1rem;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            padding: 0.5rem;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            display: flex;
            gap: 0.5rem;
            
            button {
                padding: 0.5rem 1rem;
                border: none;
                border-radius: 4px;
                background: #2196f3;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                
                &:hover {
                    background: #1976d2;
                }
                
                &:disabled {
                    background: #bdbdbd;
                    cursor: not-allowed;
                }
                
                svg {
                    width: 1.2em;
                    height: 1.2em;
                }
            }
        }
    }
    
    // Right Sidebar - Color Panel
    .color-panel {
        width: 300px;
        background: #f5f5f5;
        padding: 1rem;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        overflow-y: auto;
        
        .color-palette {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.5rem;
            
            .color {
                aspect-ratio: 1;
                border-radius: 4px;
                cursor: pointer;
                border: 2px solid transparent;
                transition: transform 0.2s;
                
                &:hover {
                    transform: scale(1.1);
                }
                
                &.selected {
                    border-color: #2196f3;
                }
            }
        }
        
        .harmonies {
            .harmony {
                margin-bottom: 1rem;
                
                h3 {
                    margin-bottom: 0.5rem;
                    font-size: 1rem;
                }
                
                .harmony-colors {
                    display: flex;
                    gap: 0.5rem;
                    
                    .harmony-color {
                        width: 2rem;
                        height: 2rem;
                        border-radius: 4px;
                    }
                }
            }
        }
        
        .product-suggestions {
            .product {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 0.5rem;
                background: white;
                border-radius: 4px;
                margin-bottom: 0.5rem;
                
                img {
                    width: 50px;
                    height: 50px;
                    object-fit: cover;
                    border-radius: 4px;
                }
                
                .product-info {
                    flex: 1;
                    
                    h4 {
                        margin: 0;
                        font-size: 0.9rem;
                    }
                    
                    p {
                        margin: 0;
                        font-size: 0.8rem;
                        color: #666;
                    }
                }
            }
        }
    }
}

// Notifications
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    padding: 1rem;
    border-radius: 4px;
    color: white;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
    
    &.success {
        background: #4caf50;
    }
    
    &.error {
        background: #f44336;
    }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
