<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;

class PaintCalculatorController extends Controller
{
    public function index()
    {
        $products = Product::where('is_active', true)
            ->whereNotNull('coverage') // m²/L
            ->orderBy('name')
            ->get();

        return view('calculator.index', compact('products'));
    }

    public function calculate(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'surface_type' => 'required|in:wall,ceiling,floor,metal,wood',
            'width' => 'required|numeric|min:0.1',
            'length' => 'required|numeric|min:0.1',
            'height' => 'nullable|numeric|min:0.1',
            'windows' => 'nullable|integer|min:0',
            'doors' => 'nullable|integer|min:0',
            'coats' => 'required|integer|min:1|max:5',
            'wastage' => 'required|numeric|min:0|max:30'
        ]);

        $product = Product::findOrFail($request->product_id);
        
        // Calcul de la surface
        $area = $this->calculateArea(
            $request->surface_type,
            $request->width,
            $request->length,
            $request->height ?? 0,
            $request->windows ?? 0,
            $request->doors ?? 0
        );

        // Calcul du volume de peinture nécessaire
        $coverage = $product->coverage; // m²/L
        $coats = $request->coats;
        $wastage = $request->wastage / 100; // Convertir le pourcentage en décimal

        $volume = ($area * $coats * (1 + $wastage)) / $coverage;

        // Arrondir aux litres supérieurs
        $volume = ceil($volume);

        // Calculer le prix estimatif
        $price = $volume * $product->price;

        // Déterminer les conditionnements recommandés
        $packagings = $this->getRecommendedPackaging($volume, $product->available_packagings);

        return response()->json([
            'success' => true,
            'result' => [
                'area' => round($area, 2),
                'volume_needed' => $volume,
                'coats' => $coats,
                'price_estimate' => round($price, 2),
                'recommended_packaging' => $packagings,
                'product' => [
                    'name' => $product->name,
                    'coverage' => $product->coverage
                ]
            ]
        ]);
    }

    private function calculateArea($type, $width, $length, $height = 0, $windows = 0, $doors = 0)
    {
        switch ($type) {
            case 'wall':
                // Pour les murs, calculer le périmètre x hauteur
                $area = 2 * ($width + $length) * $height;
                // Soustraire les fenêtres (en moyenne 1.5m²) et portes (en moyenne 2m²)
                $area -= ($windows * 1.5) + ($doors * 2);
                break;

            case 'ceiling':
            case 'floor':
                // Pour plafond et sol, simple multiplication
                $area = $width * $length;
                break;

            case 'metal':
            case 'wood':
                // Pour métal et bois, calcul basique de surface
                $area = $width * $length;
                break;

            default:
                $area = 0;
        }

        return max(0, $area); // Ne pas retourner de surface négative
    }

    private function getRecommendedPackaging($volume, $availablePackagings)
    {
        // Trier les conditionnements par volume décroissant
        rsort($availablePackagings);

        $result = [];
        $remaining = $volume;

        foreach ($availablePackagings as $packaging) {
            if ($remaining <= 0) break;

            $count = floor($remaining / $packaging);
            if ($count > 0) {
                $result[] = [
                    'volume' => $packaging,
                    'count' => $count
                ];
                $remaining -= $packaging * $count;
            }
        }

        // S'il reste un volume, ajouter le plus petit conditionnement disponible
        if ($remaining > 0 && !empty($availablePackagings)) {
            $smallestPackaging = end($availablePackagings);
            $result[] = [
                'volume' => $smallestPackaging,
                'count' => 1
            ];
        }

        return $result;
    }
}
