@extends('layouts.app')

@section('content')
<div class="py-12 {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <div class="container mx-auto px-6">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <!-- Header -->
            <div class="p-6 border-b">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold">{{ $sheet->getTranslation('title', app()->getLocale()) }}</h1>
                        <div class="text-sm text-gray-600 mt-2">
                            <span class="font-medium">{{ __('technical-sheets.sheet.reference') }}:</span>
                            {{ $sheet->reference_number }} |
                            <span class="font-medium">{{ __('technical-sheets.sheet.version') }}:</span>
                            {{ $sheet->version }} |
                            <span class="font-medium">{{ __('technical-sheets.sheet.published') }}:</span>
                            {{ $sheet->publication_date->format('d/m/Y') }}
                        </div>
                    </div>
                    
                    <div class="flex space-x-4">
                        <!-- Download PDF -->
                        <a href="{{ route('technical-sheets.download', $sheet) }}" 
                           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"/>
                            </svg>
                            {{ __('technical-sheets.sheet.download.pdf') }}
                        </a>

                        @if($sheet->hasModel3D())
                        <!-- 3D View -->
                        <a href="{{ route('technical-sheets.view3d', $sheet) }}" 
                           class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                            <svg class="w-5 h-5 me-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z"/>
                            </svg>
                            {{ __('technical-sheets.sheet.3d_view.title') }}
                        </a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="p-6">
                <!-- Description -->
                <div class="prose max-w-none mb-8">
                    {{ $sheet->getTranslation('description', app()->getLocale()) }}
                </div>

                <!-- Technical Specifications -->
                <div class="mb-8">
                    <h2 class="text-2xl font-bold mb-4">{{ __('technical-sheets.sheet.specifications') }}</h2>
                    <div class="bg-gray-50 rounded-lg p-6">
                        @foreach($sheet->getTranslation('technical_specs', app()->getLocale()) as $group => $specs)
                        <div class="mb-6 last:mb-0">
                            <h3 class="text-lg font-semibold mb-3">{{ $group }}</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($specs as $key => $value)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">{{ $key }}</span>
                                    <span class="font-medium">{{ $value }}</span>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- Application Steps -->
                @if($sheet->application_steps)
                <div class="mb-8">
                    <h2 class="text-2xl font-bold mb-4">{{ __('technical-sheets.sheet.application') }}</h2>
                    <div class="space-y-4">
                        @foreach($sheet->getTranslation('application_steps', app()->getLocale()) as $index => $step)
                        <div class="flex">
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center">
                                {{ $index + 1 }}
                            </div>
                            <div class="ms-4">
                                <p>{{ $step }}</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Safety Instructions -->
                @if($sheet->safety_instructions)
                <div class="mb-8">
                    <h2 class="text-2xl font-bold mb-4">{{ __('technical-sheets.sheet.safety') }}</h2>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                        <ul class="list-disc list-inside space-y-2 text-yellow-800">
                            @foreach($sheet->getTranslation('safety_instructions', app()->getLocale()) as $instruction)
                            <li>{{ $instruction }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                @endif

                <!-- Storage Conditions -->
                @if($sheet->storage_conditions)
                <div class="mb-8">
                    <h2 class="text-2xl font-bold mb-4">{{ __('technical-sheets.sheet.storage') }}</h2>
                    <div class="bg-gray-50 rounded-lg p-6">
                        <ul class="list-disc list-inside space-y-2">
                            @foreach($sheet->getTranslation('storage_conditions', app()->getLocale()) as $condition)
                            <li>{{ $condition }}</li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Email Modal -->
<div x-data="{ open: false }" x-cloak>
    <div x-show="open" 
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <h3 class="text-lg font-bold mb-4">{{ __('technical-sheets.sheet.download.email') }}</h3>
            
            <form @submit.prevent="sendEmail">
                <input type="email" 
                       placeholder="<EMAIL>"
                       class="w-full rounded-lg border-gray-300 mb-4">
                
                <div class="flex justify-end space-x-2">
                    <button type="button" 
                            @click="open = false"
                            class="px-4 py-2 text-gray-600 hover:text-gray-800">
                        {{ __('Cancel') }}
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        {{ __('Send') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
