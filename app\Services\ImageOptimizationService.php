<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;
use Intervention\Image\ImageManager;

class ImageOptimizationService
{
    protected $manager;
    
    // Tailles standard pour le slider
    protected $sliderSizes = [
        'original' => ['width' => 2560, 'height' => 1440, 'quality' => 90],
        '1920w' => ['width' => 1920, 'height' => 1080, 'quality' => 85],
        '1200w' => ['width' => 1200, 'height' => 675, 'quality' => 85],
        '768w' => ['width' => 768, 'height' => 432, 'quality' => 80],
        '480w' => ['width' => 480, 'height' => 270, 'quality' => 75],
    ];

    public function __construct()
    {
        $this->manager = new ImageManager(['driver' => 'gd']);
    }

    /**
     * Optimise une image uploadée pour le slider
     */
    public function optimizeSliderImage($file, string $directory = 'slides'): array
    {
        $originalName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $extension = $this->getBestExtension($file);
        $timestamp = time();
        $baseFilename = "{$originalName}_{$timestamp}";
        
        $optimizedImages = [];
        
        // Créer l'image source
        $image = $this->manager->make($file->getRealPath());
        
        // Optimiser pour chaque taille
        foreach ($this->sliderSizes as $sizeName => $config) {
            $filename = $sizeName === 'original' 
                ? "{$baseFilename}.{$extension}"
                : "{$baseFilename}_{$sizeName}.{$extension}";
            
            $optimizedImage = clone $image;
            
            // Redimensionner en gardant les proportions
            $optimizedImage->fit(
                $config['width'], 
                $config['height'], 
                function ($constraint) {
                    $constraint->upsize();
                    $constraint->aspectRatio();
                }
            );
            
            // Appliquer des filtres pour améliorer la qualité
            $this->applyQualityFilters($optimizedImage);
            
            // Sauvegarder avec compression optimisée
            $path = "{$directory}/{$filename}";
            $this->saveOptimizedImage($optimizedImage, $path, $extension, $config['quality']);
            
            $optimizedImages[$sizeName] = [
                'path' => $path,
                'url' => Storage::url($path),
                'width' => $config['width'],
                'height' => $config['height'],
                'size' => Storage::size($path)
            ];
        }
        
        // Nettoyer la mémoire
        $image->destroy();
        
        return $optimizedImages;
    }

    /**
     * Applique des filtres pour améliorer la qualité visuelle
     */
    protected function applyQualityFilters($image)
    {
        // Améliorer la netteté
        $image->sharpen(10);
        
        // Ajuster légèrement le contraste et la luminosité
        $image->contrast(2);
        $image->brightness(1);
        
        // Saturation légèrement augmentée pour des couleurs plus vives
        $image->colorize(0, 0, 0, 0.02);
        
        return $image;
    }

    /**
     * Sauvegarde l'image avec les paramètres optimaux
     */
    protected function saveOptimizedImage($image, string $path, string $extension, int $quality)
    {
        switch (strtolower($extension)) {
            case 'webp':
                Storage::put($path, $image->encode('webp', $quality));
                break;
            case 'png':
                // Pour PNG, utiliser la compression sans perte
                Storage::put($path, $image->encode('png', 9));
                break;
            case 'jpg':
            case 'jpeg':
            default:
                Storage::put($path, $image->encode('jpg', $quality));
                break;
        }
    }

    /**
     * Détermine la meilleure extension pour l'image
     */
    protected function getBestExtension(UploadedFile $file): string
    {
        $originalExtension = strtolower($file->getClientOriginalExtension());
        
        // Préférer WebP pour une meilleure compression
        if (function_exists('imagewebp')) {
            return 'webp';
        }
        
        // Sinon, utiliser JPEG pour les photos et PNG pour les images avec transparence
        if (in_array($originalExtension, ['png', 'gif']) && $this->hasTransparency($file)) {
            return 'png';
        }
        
        return 'jpg';
    }

    /**
     * Vérifie si l'image a de la transparence
     */
    protected function hasTransparency(UploadedFile $file): bool
    {
        try {
            $image = $this->manager->make($file->getRealPath());
            
            // Vérifier la transparence pour PNG
            if ($file->getClientOriginalExtension() === 'png') {
                return true; // Assumer transparence pour PNG
            }
            
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Génère le srcset pour une image optimisée
     */
    public function generateSrcSet(array $optimizedImages): string
    {
        $srcset = [];
        
        foreach ($optimizedImages as $sizeName => $imageData) {
            if ($sizeName !== 'original') {
                $srcset[] = "{$imageData['url']} {$imageData['width']}w";
            }
        }
        
        return implode(', ', $srcset);
    }

    /**
     * Supprime toutes les versions d'une image
     */
    public function deleteOptimizedImages(string $basePath)
    {
        $directory = dirname($basePath);
        $filename = pathinfo($basePath, PATHINFO_FILENAME);
        
        // Supprimer toutes les variantes
        foreach (array_keys($this->sliderSizes) as $sizeName) {
            $patterns = [
                "{$directory}/{$filename}.jpg",
                "{$directory}/{$filename}.jpeg",
                "{$directory}/{$filename}.png",
                "{$directory}/{$filename}.webp",
                "{$directory}/{$filename}_{$sizeName}.jpg",
                "{$directory}/{$filename}_{$sizeName}.jpeg",
                "{$directory}/{$filename}_{$sizeName}.png",
                "{$directory}/{$filename}_{$sizeName}.webp",
            ];
            
            foreach ($patterns as $pattern) {
                if (Storage::exists($pattern)) {
                    Storage::delete($pattern);
                }
            }
        }
    }

    /**
     * Obtient les informations d'une image optimisée
     */
    public function getImageInfo(string $path): array
    {
        if (!Storage::exists($path)) {
            return [];
        }

        try {
            $image = $this->manager->make(Storage::path($path));
            
            return [
                'width' => $image->width(),
                'height' => $image->height(),
                'size' => Storage::size($path),
                'mime_type' => $image->mime(),
                'url' => Storage::url($path)
            ];
        } catch (\Exception $e) {
            return [];
        }
    }
}
