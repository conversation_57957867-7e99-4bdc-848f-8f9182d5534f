@extends('layouts.app')

@section('title', $category->name . ' - Blog ENAP')

@section('content')
<div class="bg-gray-50 py-12">
    <div class="container mx-auto px-4">
        <!-- En-tête -->
        <div class="mb-12">
            <nav class="flex mb-4" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2">
                    <li>
                        <a href="{{ route('home') }}" class="text-gray-500 hover:text-gray-700">Accueil</a>
                    </li>
                    <li>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </li>
                    <li>
                        <a href="{{ route('blog.index') }}" class="text-gray-500 hover:text-gray-700">Blog</a>
                    </li>
                    <li>
                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </li>
                    <li>
                        <span class="text-gray-900">{{ $category->name }}</span>
                    </li>
                </ol>
            </nav>

            <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ $category->name }}</h1>
            <p class="text-xl text-gray-600">{{ $category->description }}</p>
        </div>

        <!-- Liste des articles -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($posts as $post)
            <article class="bg-white rounded-lg shadow-lg overflow-hidden group hover:shadow-xl transition-shadow">
                <a href="{{ route('blog.show', $post->slug) }}" class="block">
                    <div class="relative h-48">
                        <img src="{{ $post->featured_image }}" 
                             alt="{{ $post->title }}" 
                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                    </div>
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600">
                            {{ $post->title }}
                        </h2>
                        <p class="text-gray-600 mb-4 line-clamp-3">{{ $post->excerpt }}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                @if($post->author->avatar)
                                <img src="{{ $post->author->avatar }}" 
                                     alt="{{ $post->author->name }}"
                                     class="w-8 h-8 rounded-full">
                                @endif
                                <div class="text-sm">
                                    <p class="text-gray-900 font-medium">{{ $post->author->name }}</p>
                                    <p class="text-gray-500">{{ $post->formatted_date }}</p>
                                </div>
                            </div>
                            <span class="text-blue-600 font-medium">Lire la suite →</span>
                        </div>
                    </div>
                </a>
            </article>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-12">
            {{ $posts->links() }}
        </div>
    </div>
</div>
@endsection
