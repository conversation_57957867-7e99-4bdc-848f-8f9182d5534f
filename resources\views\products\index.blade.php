@extends('layouts.app')

@section('content')
<div class="py-12 {{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <div class="container mx-auto px-6">
        <!-- Filters -->
        <div class="mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <h2 class="text-lg font-semibold mb-4">{{ __('products.catalog.filter') }}</h2>
                <form action="{{ route('products.index') }}" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('products.catalog.all_categories') }}</label>
                        <select name="category" class="w-full rounded-md border-gray-300">
                            <option value="">{{ __('products.catalog.all_categories') }}</option>
                            @foreach($categories as $category)
                                <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                    {{ $category->getTranslation('name', app()->getLocale()) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">{{ __('products.filters.attributes.surface') }}</label>
                        <select name="surface" class="w-full rounded-md border-gray-300">
                            <option value="">{{ __('products.catalog.all_categories') }}</option>
                            <option value="interior" {{ request('surface') == 'interior' ? 'selected' : '' }}>{{ __('products.filters.attributes.surface') }} - {{ __('Intérieur') }}</option>
                            <option value="exterior" {{ request('surface') == 'exterior' ? 'selected' : '' }}>{{ __('products.filters.attributes.surface') }} - {{ __('Extérieur') }}</option>
                        </select>
                    </div>

                    <div class="flex items-end">
                        <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                            {{ __('products.catalog.filter') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($products as $product)
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <img src="{{ $product->image_url }}" alt="{{ $product->getTranslation('name', app()->getLocale()) }}" class="w-full h-64 object-cover">
                <div class="p-6">
                    <h3 class="text-xl font-semibold mb-2">{{ $product->getTranslation('name', app()->getLocale()) }}</h3>
                    <p class="text-gray-600 mb-4">{{ $product->getTranslation('short_description', app()->getLocale()) }}</p>
                    <div class="flex justify-between items-center">
                        <a href="{{ route('products.show', $product->id) }}" class="text-blue-600 font-semibold hover:text-blue-800">
                            {{ __('Détails') }} &rarr;
                        </a>
                        <a href="{{ route('quotes.create', ['product_id' => $product->id]) }}" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            {{ __('products.product.request_quote') }}
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            {{ $products->links() }}
        </div>
    </div>
</div>
@endsection
