<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BlogCategoryController extends Controller
{
    public function index()
    {
        $categories = BlogCategory::withCount('posts')
            ->orderBy('order')
            ->paginate(10);

        return view('admin.blog.categories.index', compact('categories'));
    }

    public function create()
    {
        $categories = BlogCategory::all();
        return view('admin.blog.categories.form', compact('categories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:blog_categories,id',
            'order' => 'nullable|integer'
        ]);

        $validated['slug'] = Str::slug($validated['name']);

        BlogCategory::create($validated);

        return redirect()
            ->route('admin.blog.categories.index')
            ->with('success', 'Catégorie créée avec succès');
    }

    public function edit(BlogCategory $category)
    {
        $categories = BlogCategory::where('id', '!=', $category->id)->get();
        return view('admin.blog.categories.form', compact('category', 'categories'));
    }

    public function update(Request $request, BlogCategory $category)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:blog_categories,id',
            'order' => 'nullable|integer'
        ]);

        $validated['slug'] = Str::slug($validated['name']);

        $category->update($validated);

        return redirect()
            ->route('admin.blog.categories.index')
            ->with('success', 'Catégorie mise à jour avec succès');
    }

    public function destroy(BlogCategory $category)
    {
        if ($category->posts()->exists()) {
            return redirect()
                ->route('admin.blog.categories.index')
                ->with('error', 'Cette catégorie contient des articles et ne peut pas être supprimée');
        }

        $category->delete();

        return redirect()
            ->route('admin.blog.categories.index')
            ->with('success', 'Catégorie supprimée avec succès');
    }
}
