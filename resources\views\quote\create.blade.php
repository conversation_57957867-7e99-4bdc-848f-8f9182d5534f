@extends('layouts.app')

@section('content')
<div class="py-12">
    <div class="container mx-auto px-6">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-3xl font-bold mb-8"><PERSON><PERSON><PERSON> <PERSON></h1>

            @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                <strong class="font-bold">Succès!</strong>
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
            @endif

            <form action="{{ route('quote.store') }}" method="POST" class="space-y-6">
                @csrf

                <!-- Client Information -->
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h2 class="text-xl font-semibold mb-4">Informations du Client</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="company_name" class="block text-sm font-medium text-gray-700 mb-1">Nom de l'Entreprise</label>
                            <input type="text" name="company_name" id="company_name" required
                                   class="w-full rounded-md border-gray-300 @error('company_name') border-red-500 @enderror">
                            @error('company_name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="contact_name" class="block text-sm font-medium text-gray-700 mb-1">Nom du Contact</label>
                            <input type="text" name="contact_name" id="contact_name" required
                                   class="w-full rounded-md border-gray-300 @error('contact_name') border-red-500 @enderror">
                            @error('contact_name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" name="email" id="email" required
                                   class="w-full rounded-md border-gray-300 @error('email') border-red-500 @enderror">
                            @error('email')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                            <input type="tel" name="phone" id="phone" required
                                   class="w-full rounded-md border-gray-300 @error('phone') border-red-500 @enderror">
                            @error('phone')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Project Details -->
                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <h2 class="text-xl font-semibold mb-4">Détails du Projet</h2>
                    
                    <div class="space-y-6">
                        <div>
                            <label for="project_type" class="block text-sm font-medium text-gray-700 mb-1">Type de Projet</label>
                            <select name="project_type" id="project_type" required
                                    class="w-full rounded-md border-gray-300 @error('project_type') border-red-500 @enderror">
                                <option value="">Sélectionnez un type</option>
                                <option value="residential">Résidentiel</option>
                                <option value="commercial">Commercial</option>
                                <option value="industrial">Industriel</option>
                            </select>
                            @error('project_type')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="surface_area" class="block text-sm font-medium text-gray-700 mb-1">Surface à Peindre (m²)</label>
                            <input type="number" name="surface_area" id="surface_area" required min="1"
                                   class="w-full rounded-md border-gray-300 @error('surface_area') border-red-500 @enderror">
                            @error('surface_area')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="products" class="block text-sm font-medium text-gray-700 mb-1">Produits Souhaités</label>
                            <select name="products[]" id="products" multiple required
                                    class="w-full rounded-md border-gray-300 @error('products') border-red-500 @enderror">
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}" {{ in_array($product->id, old('products', [])) ? 'selected' : '' }}>
                                        {{ $product->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('products')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="additional_info" class="block text-sm font-medium text-gray-700 mb-1">Informations Supplémentaires</label>
                            <textarea name="additional_info" id="additional_info" rows="4"
                                      class="w-full rounded-md border-gray-300 @error('additional_info') border-red-500 @enderror"></textarea>
                            @error('additional_info')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        Envoyer la demande
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
