<?php

namespace App\Http\Controllers;

use App\Models\UserProject;
use App\Models\Product;
use App\Models\ColorPalette;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class UserProjectController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $projects = UserProject::where('user_id', Auth::id())
            ->orderBy('updated_at', 'desc')
            ->paginate(12);

        $stats = [
            'total' => UserProject::where('user_id', Auth::id())->count(),
            'planning' => UserProject::where('user_id', Auth::id())->where('status', 'planning')->count(),
            'in_progress' => UserProject::where('user_id', Auth::id())->where('status', 'in_progress')->count(),
            'completed' => UserProject::where('user_id', Auth::id())->where('status', 'completed')->count(),
        ];

        return view('dashboard.projects.index', compact('projects', 'stats'));
    }

    public function create()
    {
        $colors = ColorPalette::all();
        $products = Product::all();
        
        return view('dashboard.projects.create', compact('colors', 'products'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:residential,commercial,industrial,marine,automotive',
            'surface_area' => 'nullable|numeric|min:0',
            'budget' => 'nullable|numeric|min:0',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'colors_used' => 'nullable|array',
            'products_used' => 'nullable|array',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'notes' => 'nullable|string|max:2000',
        ]);

        $project = new UserProject($request->all());
        $project->user_id = Auth::id();
        $project->status = 'planning';

        // Handle image uploads
        if ($request->hasFile('images')) {
            $imagePaths = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('projects', 'public');
                $imagePaths[] = $path;
            }
            $project->images = $imagePaths;
        }

        $project->save();

        return redirect()->route('dashboard.projects.show', $project)
            ->with('success', __('projects.created_successfully'));
    }

    public function show(UserProject $project)
    {
        $this->authorize('view', $project);

        $colors = [];
        if ($project->colors_used) {
            $colors = ColorPalette::whereIn('id', $project->colors_used)->get();
        }

        $products = [];
        if ($project->products_used) {
            $products = Product::whereIn('id', $project->products_used)->get();
        }

        return view('dashboard.projects.show', compact('project', 'colors', 'products'));
    }

    public function edit(UserProject $project)
    {
        $this->authorize('update', $project);

        $colors = ColorPalette::all();
        $products = Product::all();
        
        return view('dashboard.projects.edit', compact('project', 'colors', 'products'));
    }

    public function update(Request $request, UserProject $project)
    {
        $this->authorize('update', $project);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|in:residential,commercial,industrial,marine,automotive',
            'surface_area' => 'nullable|numeric|min:0',
            'budget' => 'nullable|numeric|min:0',
            'status' => 'required|in:planning,in_progress,completed,on_hold',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'colors_used' => 'nullable|array',
            'products_used' => 'nullable|array',
            'images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'notes' => 'nullable|string|max:2000',
        ]);

        $project->fill($request->all());

        // Handle new image uploads
        if ($request->hasFile('images')) {
            $imagePaths = $project->images ?? [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('projects', 'public');
                $imagePaths[] = $path;
            }
            $project->images = $imagePaths;
        }

        $project->save();

        return redirect()->route('dashboard.projects.show', $project)
            ->with('success', __('projects.updated_successfully'));
    }

    public function destroy(UserProject $project)
    {
        $this->authorize('delete', $project);

        // Delete associated images
        if ($project->images) {
            foreach ($project->images as $imagePath) {
                Storage::disk('public')->delete($imagePath);
            }
        }

        $project->delete();

        return redirect()->route('dashboard.projects.index')
            ->with('success', __('projects.deleted_successfully'));
    }

    public function removeImage(UserProject $project, $imageIndex)
    {
        $this->authorize('update', $project);

        $images = $project->images ?? [];
        
        if (isset($images[$imageIndex])) {
            Storage::disk('public')->delete($images[$imageIndex]);
            unset($images[$imageIndex]);
            $project->images = array_values($images);
            $project->save();
        }

        return response()->json(['success' => true]);
    }

    public function calculateCost(Request $request)
    {
        $request->validate([
            'type' => 'required|in:residential,commercial,industrial,marine,automotive',
            'surface_area' => 'required|numeric|min:0',
            'products' => 'nullable|array',
        ]);

        $baseRates = [
            'residential' => 25,
            'commercial' => 35,
            'industrial' => 45,
            'marine' => 60,
            'automotive' => 80,
        ];

        $baseCost = $request->surface_area * $baseRates[$request->type];
        
        // Add product-specific costs if products are selected
        $productCost = 0;
        if ($request->products) {
            $products = Product::whereIn('id', $request->products)->get();
            foreach ($products as $product) {
                $productCost += $product->price_per_unit ?? 0;
            }
        }

        $totalCost = $baseCost + $productCost;

        return response()->json([
            'base_cost' => $baseCost,
            'product_cost' => $productCost,
            'total_cost' => $totalCost,
            'formatted_total' => number_format($totalCost, 2) . ' DZD',
        ]);
    }
}
