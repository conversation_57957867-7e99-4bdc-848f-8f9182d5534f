@extends('layouts.app')

@section('content')
<div class="py-12">
    <div class="container mx-auto px-6">
        <h1 class="text-3xl font-bold mb-8">Nos Unités de Production</h1>

        <!-- Map Section -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-8">
            <div id="map" class="h-[500px]"></div>
        </div>

        <!-- Units List -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($units as $unit)
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <img src="{{ $unit->image_url }}" alt="{{ $unit->name }}" class="w-full h-48 object-cover">
                
                <div class="p-6">
                    <h2 class="text-xl font-semibold mb-2">{{ $unit->name }}</h2>
                    
                    <div class="space-y-3 mb-4">
                        <p class="flex items-center text-gray-600">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            {{ $unit->address }}
                        </p>
                        
                        <p class="flex items-center text-gray-600">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                            {{ $unit->phone }}
                        </p>
                        
                        <p class="flex items-center text-gray-600">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                            {{ $unit->email }}
                        </p>
                    </div>

                    <div class="border-t pt-4">
                        <h3 class="font-semibold mb-2">Produits Fabriqués</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($unit->products as $product)
                                <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">
                                    {{ $product->name }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>

@push('scripts')
<script src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.maps_api_key') }}&callback=initMap" 
        async defer></script>
<script>
function initMap() {
    const algeria = { lat: 36.7538, lng: 3.0588 };
    const map = new google.maps.Map(document.getElementById('map'), {
        zoom: 6,
        center: algeria,
    });

    const units = @json($units->map(function($unit) {
        return [
            'name' => $unit->name,
            'lat' => $unit->latitude,
            'lng' => $unit->longitude,
            'address' => $unit->address
        ];
    }));

    units.forEach(unit => {
        const marker = new google.maps.Marker({
            position: { lat: parseFloat(unit.lat), lng: parseFloat(unit.lng) },
            map: map,
            title: unit.name
        });

        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div class="p-2">
                    <h3 class="font-semibold">${unit.name}</h3>
                    <p class="text-sm">${unit.address}</p>
                </div>
            `
        });

        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
    });
}
</script>
@endpush
@endsection
